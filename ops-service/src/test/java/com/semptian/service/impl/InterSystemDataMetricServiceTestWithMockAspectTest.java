/*
package com.semptian.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.semptian.component.DorisStreamLoadUtil;
import com.semptian.entity.BusinessAccountConfig;
import com.semptian.entity.BusinessMetricConfig;
import com.semptian.model.vo.AdsOpsOpsApiAccessResult;
import com.semptian.service.*;
import com.semptian.service.constants.TestConstants;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest(cn.hutool.http.HttpUtil.class)
@Slf4j
public class InterSystemDataMetricServiceTestWithMockAspectTest {

    private BusinessAccountConfigService businessAccountConfigService;

    private BusinessMetricConfigService businessMetricConfigService;

    @InjectMocks
    private InterSystemDataProducerServiceImpl interSystemDataProducerService;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 创建一个共享的mock对象，确保所有地方使用同一个实例
        businessMetricConfigService = MockDataStubBuilder.buildMock(BusinessMetricConfigService.class);
        businessAccountConfigService = MockDataStubBuilder.buildMock(BusinessAccountConfigService.class);

        // 手动注入 mock 对象
        ReflectionTestUtils.setField(interSystemDataProducerService, "businessMetricConfigService", businessMetricConfigService);
        ReflectionTestUtils.setField(interSystemDataProducerService, "businessSysConfigService", MockDataStubBuilder.buildMock(BusinessSysConfigService.class));
        ReflectionTestUtils.setField(interSystemDataProducerService, "businessAccountConfigService", businessAccountConfigService);
        ReflectionTestUtils.setField(interSystemDataProducerService, "businessSampleConfigService", MockDataStubBuilder.buildMock(BusinessSampleConfigService.class));
        ReflectionTestUtils.setField(interSystemDataProducerService, "apiConfigService", MockDataStubBuilder.buildMock(BusinessApiConfigService.class));
        ReflectionTestUtils.setField(interSystemDataProducerService, "redisTemplate", MockDataStubBuilder.buildMock(RedisTemplate.class));
        ReflectionTestUtils.setField(interSystemDataProducerService, "dorisStreamLoad", MockDataStubBuilder.buildMock(DorisStreamLoadUtil.class));

        //手动注入常量，如maxSimpleSize
        ReflectionTestUtils.setField(interSystemDataProducerService, "maxSampleSize", 10000);
        ReflectionTestUtils.setField(interSystemDataProducerService, "searchThreadNum", 8);
        ReflectionTestUtils.setField(interSystemDataProducerService, "archiveThreadNum", 4);
        ReflectionTestUtils.setField(interSystemDataProducerService, "caseThreadNum", 2);

        //设置isUseMock为false，因为在测试中，我们不希望生产 mock 数据
        ReflectionTestUtils.setField(interSystemDataProducerService, "isUseMock", false);

        // 静态 mock 对象
        MockDataStubBuilder.buildMock(HttpResponse.class);
        MockDataStubBuilder.buildMock(HttpRequest.class);
        MockDataStubBuilder.buildMock(HttpUtil.class);
    }

    //校验最终写入到Doris中的数据是账号的两倍（因为每个账号都会去访问全息和搜索并返回一条数据）
    @Test
    public void MetricId1Test() {
        List<AdsOpsOpsApiAccessResult> adsOpsOpsApiAccessResults = interSystemDataProducerService.collectMetrics(1, "2025-04-10,2025-04-10");
        //验证adsOpsOpsApiAccessResults的size
        BusinessMetricConfig businessMetricConfig = businessMetricConfigService.getByIdWithService(1);
        List<BusinessAccountConfig> businessAccountConfigs = businessAccountConfigService.getAccountConfigByMetricId(businessMetricConfig, 10000, "2025-04-10,2025-04-10");
        Assert.assertNotNull("businessAccountConfigs不应为空", businessAccountConfigs);
        Assert.assertFalse("businessAccountConfigs不应为空列表", businessAccountConfigs.isEmpty());
        //这里体现了中间函数的校验方式
        Assert.assertEquals(businessAccountConfigs.size() * 2, adsOpsOpsApiAccessResults.size());
    }

    @Test
    public void MetricId2Test() {
        // 测试指标ID为2的情况
        List<AdsOpsOpsApiAccessResult> results = interSystemDataProducerService.collectMetrics(2, "2025-04-10,2025-04-10");

        // 验证结果不为空
        Assert.assertNotNull(results);
        Assert.assertFalse(results.isEmpty());

        //验证adsOpsOpsApiAccessResults的size
        BusinessMetricConfig businessMetricConfig = businessMetricConfigService.getByIdWithService(3);
        List<BusinessAccountConfig> businessAccountConfigs = businessAccountConfigService.getAccountConfigByMetricId(businessMetricConfig, 10000, "2025-04-10,2025-04-10");
        List<BusinessAccountConfig> businessAccountConfigs2 = businessAccountConfigService.getAccountConfigByMetricIdWithStaticAccount(businessMetricConfig, 10000, "2025-04-10,2025-04-10");
        Assert.assertNotNull("businessAccountConfigs不应为空", businessAccountConfigs);
        Assert.assertFalse("businessAccountConfigs不应为空列表", businessAccountConfigs.isEmpty());
        //这里results
        Assert.assertEquals(businessAccountConfigs.size() * 2L + businessAccountConfigs2.size() * 3L, results.size());
    }

    @Test
    public void MetricId3Test() {
        // 测试指标ID为3的情况
        List<AdsOpsOpsApiAccessResult> results = interSystemDataProducerService.collectMetrics(3, "2025-04-10,2025-04-10");
        // 验证结果不为空
        Assert.assertNotNull(results);
        Assert.assertFalse(results.isEmpty());
        //验证adsOpsOpsApiAccessResults的size
        BusinessMetricConfig businessMetricConfig = businessMetricConfigService.getByIdWithService(3);
        List<BusinessAccountConfig> businessAccountConfigs = businessAccountConfigService.getAccountConfigByMetricId(businessMetricConfig, 10000, "2025-04-10,2025-04-10");
        List<BusinessAccountConfig> businessAccountConfigs2 = businessAccountConfigService.getAccountConfigByMetricIdWithStaticAccount(businessMetricConfig, 10000, "2025-04-10,2025-04-10");
        Assert.assertNotNull("businessAccountConfigs不应为空", businessAccountConfigs);
        Assert.assertFalse("businessAccountConfigs不应为空列表", businessAccountConfigs.isEmpty());
        //这里results
        Assert.assertEquals(businessAccountConfigs.size() * 2L + businessAccountConfigs2.size() * 3L, results.size());
    }

    //号码数据差异分析的通联账号明细，排除自身账号场景
    @Test
    public void MetricId8Test() {
        log.info("开始测试指标ID 8的数据采集...");

        // 测试指标ID为8的情况
        List<AdsOpsOpsApiAccessResult> results = interSystemDataProducerService.collectMetrics(8, "2025-04-10,2025-04-10");
        log.info("采集到总数据条数: {}", results.size());

        // 验证结果不为空
        Assert.assertNotNull(results);
        Assert.assertFalse(results.isEmpty());

//        results.stream()
//                .filter(result -> result.getAccount().equals("************") && result.getSystemName().equals("综合搜索"))
//                .forEach(result -> {
//                    log.info("验证账号 '************' 在系统 '{}' 中的数据", result.getSystemName());
//                    switch (result.getSystemName()) {
//                        case "综合搜索":
//                            //String kvContent = result.getKvContent();
//                            //应当排除自己,所以应该是不包Assert.assertFalse(kvContent.contains("************"));
//                            break;
//                    }
//                });

        log.info("指标ID 8的测试全部通过");
    }

    //测试固网Radius的访问网络次数指标采集时，账号为大小写混合场景（应该忽略大小写进行匹配取值）
    @Test
    public void MetricId11Test() {
        log.info("开始测试指标ID 11的数据采集...");

        // 测试指标ID为11的情况
        List<AdsOpsOpsApiAccessResult> results = interSystemDataProducerService.collectMetrics(11, "2025-04-10,2025-04-10");
        log.info("采集到总数据条数: {}", results.size());

        // 验证结果不为空
        Assert.assertNotNull(results);
        Assert.assertFalse(results.isEmpty());

        results.stream().filter(result -> result.getAccount().equals("Gljhomeradius")).forEach(result -> {
            log.info("验证账号 'Gljhomeradius' 在系统 '{}' 中的数据", result.getSystemName());
            switch (result.getSystemName()) {
                case "综合搜索":
                    Assert.assertEquals(result.getKvContent(), "{\"gljhomeradius\":\"260\"}");
                    Assert.assertEquals(result.getStatCount(), 260l);
                    log.info("综合搜索系统数据验证通过: kvContent={}, statCount={}", result.getKvContent(), result.getStatCount());
                    break;
                case "全息档案":
                    Assert.assertEquals(result.getKvContent(), "[{\"num\":\"248\"}]");
                    Assert.assertEquals(result.getStatCount(), 248l);
                    log.info("全息档案系统数据验证通过: kvContent={}, statCount={}", result.getKvContent(), result.getStatCount());
                    break;
            }
        });

        log.info("指标ID 11的测试全部通过");
    }

    @Test
    public void testProduceSampleAccount() {
        // 测试生成样本账号的方法
        interSystemDataProducerService.produceSampleAccount(10000);
    }

    @Test
    public void testCollectMetricsWithCustomTimeRange() {
        // 测试使用自定义时间范围的情况
        String customTimeRange = "2025-04-01,2025-04-01";
        List<AdsOpsOpsApiAccessResult> results = interSystemDataProducerService.collectMetrics(1, customTimeRange);

        // 验证结果为空(不能跨天)
        Assert.assertTrue(results.isEmpty());
    }

    @Test
    public void testCollectMetricsResultProperties() {
        // 测试结果对象的属性是否正确设置
        List<AdsOpsOpsApiAccessResult> results = interSystemDataProducerService.collectMetrics(1, "2025-04-10,2025-04-10");

        // 验证结果不为空
        Assert.assertFalse(results.isEmpty());

        // 验证结果中的每个对象的属性
        results.forEach(result -> {
            // 验证基本属性
            Assert.assertNotNull("指标ID不应为空", result.getMetricId());
            Assert.assertNotNull("系统名称不应为空", result.getSystemName());
            Assert.assertNotNull("账号不应为空", result.getAccount());
            Assert.assertNotNull("API路径不应为空", result.getApiPath());
            Assert.assertNotNull("请求时间不应为空", result.getRequestTime());
            Assert.assertNotNull("统计日期不应为空", result.getStatDate());

            // 验证系统名称是否为预期的三个系统之一
            Assert.assertTrue("系统名称应为预期的三个系统之一", result.getSystemName().equals(TestConstants.SEARCH_NAME) || result.getSystemName().equals(TestConstants.ARCHIVE_NAME) || result.getSystemName().equals(TestConstants.CASE_NAME));
        });
    }
}
*/

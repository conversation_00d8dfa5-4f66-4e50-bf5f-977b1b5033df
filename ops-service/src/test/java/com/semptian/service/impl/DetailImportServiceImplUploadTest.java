package com.semptian.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 上传文件导入功能测试
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class DetailImportServiceImplUploadTest {

    @Resource
    private DetailImportServiceImpl detailImportService;

    @Test
    public void testUploadAndImportCsv() {
        try {
            // 创建测试CSV内容
            String csvContent = "账号,协议类型编码,data_id,时间戳\n" +
                    "<EMAIL>,email,12345,1642780800000\n" +
                    "13800138000,call,67890,1642780800000\n" +
                    "testuser,im,11111,1642780800000";

            // 创建MockMultipartFile
            MultipartFile mockFile = new MockMultipartFile(
                    "file",
                    "test_import.csv",
                    "text/csv",
                    csvContent.getBytes(StandardCharsets.UTF_8)
            );

            // 测试上传导入
            Integer testMetricId = 8;
            String tableName = null; // 使用默认表名

            log.info("开始测试上传文件导入 | 指标ID: {} | 文件大小: {} bytes", testMetricId, mockFile.getSize());

            String result = detailImportService.uploadAndImportCsv(mockFile, testMetricId, tableName);

            log.info("上传文件导入测试完成 | 结果: {}", result);

            // 验证结果包含任务ID
            assert result.contains("任务ID") : "结果应该包含任务ID";
            assert result.contains("已提交") : "结果应该表示任务已提交";

            // 等待一段时间让异步任务执行
            Thread.sleep(5000);

            // 提取任务ID并查询状态
            String taskId = extractTaskIdFromResult(result);
            if (taskId != null) {
                Object status = detailImportService.getImportTaskStatus(taskId);
                log.info("任务状态查询结果: {}", status);
            }

        } catch (Exception e) {
            log.error("上传文件导入测试失败", e);
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testUploadLargeFile() {
        try {
            // 创建较大的测试CSV内容
            StringBuilder csvBuilder = new StringBuilder();
            csvBuilder.append("账号,协议类型编码,data_id,时间戳\n");

            // 生成1000行测试数据
            for (int i = 1; i <= 1000; i++) {
                csvBuilder.append(String.format("<EMAIL>,email,%d,%d\n",
                        i, 10000 + i, System.currentTimeMillis()));
            }

            String csvContent = csvBuilder.toString();

            // 创建MockMultipartFile
            MultipartFile mockFile = new MockMultipartFile(
                    "file",
                    "large_test_import.csv",
                    "text/csv",
                    csvContent.getBytes(StandardCharsets.UTF_8)
            );

            // 测试上传导入
            Integer testMetricId = 8;
            String tableName = null;

            log.info("开始测试大文件上传导入 | 指标ID: {} | 文件大小: {} bytes | 数据行数: 1000",
                    testMetricId, mockFile.getSize());

            String result = detailImportService.uploadAndImportCsv(mockFile, testMetricId, tableName);

            log.info("大文件上传导入测试完成 | 结果: {}", result);

            // 验证结果
            assert result.contains("任务ID") : "结果应该包含任务ID";

            // 等待更长时间让异步任务执行
            Thread.sleep(10000);

            // 查询任务状态
            String taskId = extractTaskIdFromResult(result);
            if (taskId != null) {
                Object status = detailImportService.getImportTaskStatus(taskId);
                log.info("大文件任务状态查询结果: {}", status);
            }

        } catch (Exception e) {
            log.error("大文件上传导入测试失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 从结果字符串中提取任务ID
     */
    private String extractTaskIdFromResult(String result) {
        try {
            // 结果格式：上传导入任务已提交，任务ID: xxxxxxxx
            String[] parts = result.split("任务ID:");
            if (parts.length > 1) {
                return parts[1].trim();
            }
        } catch (Exception e) {
            log.warn("提取任务ID失败: {}", e.getMessage());
        }
        return null;
    }

    @Test
    public void testUploadInvalidFile() {
        try {
            // 创建无效的CSV内容（缺少必要字段）
            String csvContent = "无效字段1,无效字段2\n" +
                    "值1,值2\n" +
                    "值3,值4";

            // 创建MockMultipartFile
            MultipartFile mockFile = new MockMultipartFile(
                    "file",
                    "invalid_test.csv",
                    "text/csv",
                    csvContent.getBytes(StandardCharsets.UTF_8)
            );

            // 测试上传导入
            Integer testMetricId = 8;
            String tableName = null;

            log.info("开始测试无效文件上传导入 | 指标ID: {} | 文件大小: {} bytes", testMetricId, mockFile.getSize());

            String result = detailImportService.uploadAndImportCsv(mockFile, testMetricId, tableName);

            log.info("无效文件上传导入测试完成 | 结果: {}", result);

            // 等待异步任务执行
            Thread.sleep(3000);

            // 查询任务状态，应该显示失败
            String taskId = extractTaskIdFromResult(result);
            if (taskId != null) {
                Object status = detailImportService.getImportTaskStatus(taskId);
                log.info("无效文件任务状态查询结果: {}", status);
            }

        } catch (Exception e) {
            log.error("无效文件上传导入测试失败", e);
            // 这个测试预期可能会失败，所以不抛出异常
            log.info("无效文件测试按预期失败");
        }
    }
}

package com.semptian.service.impl;

import com.semptian.entity.DetailFieldConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;

/**
 * Doris列映射测试类
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class DorisColumnMappingTest {

    @Resource
    private DorisColumnMappingUtil dorisColumnMappingUtil;

    @Test
    public void testCallProtocolColumnMapping() {
        // 模拟通话协议的字段配置
        List<DetailFieldConfig> fieldConfigs = createCallProtocolConfigs();
        
        // 获取表列定义
        List<String> tableColumns = dorisColumnMappingUtil.getProtocolTableColumns("call", fieldConfigs);
        
        log.info("通话协议表列定义: {}", tableColumns);
        log.info("总列数: {}", tableColumns.size());
        
        // 模拟CSV数据
        Map<String, Object> csvRow = createCallCsvData();
        
        // 验证数据兼容性
        DorisColumnMappingUtil.ValidationResult validation = 
                dorisColumnMappingUtil.validateDataCompatibility(csvRow, tableColumns);
        
        log.info("数据验证结果: {}", validation.isValid());
        if (!validation.isValid()) {
            log.warn("验证错误: {}", validation.getErrorMessage());
        }
        
        // 转换为Stream Load格式
        String streamLoadData = dorisColumnMappingUtil.convertRowToStreamLoadFormat(csvRow, tableColumns);
        log.info("Stream Load数据: {}", streamLoadData);
        
        // 验证列数
        String[] columns = streamLoadData.split("\t");
        log.info("生成的列数: {} | 期望列数: {}", columns.length, tableColumns.size());
        
        assert columns.length == tableColumns.size() : "列数不匹配";
    }

    @Test
    public void testEmailProtocolColumnMapping() {
        // 模拟邮件协议的字段配置
        List<DetailFieldConfig> fieldConfigs = createEmailProtocolConfigs();
        
        // 获取表列定义
        List<String> tableColumns = dorisColumnMappingUtil.getProtocolTableColumns("email", fieldConfigs);
        
        log.info("邮件协议表列定义: {}", tableColumns);
        log.info("总列数: {}", tableColumns.size());
        
        // 模拟CSV数据
        Map<String, Object> csvRow = createEmailCsvData();
        
        // 验证数据兼容性
        DorisColumnMappingUtil.ValidationResult validation = 
                dorisColumnMappingUtil.validateDataCompatibility(csvRow, tableColumns);
        
        log.info("数据验证结果: {}", validation.isValid());
        if (!validation.isValid()) {
            log.warn("验证错误: {}", validation.getErrorMessage());
        }
        
        // 转换为Stream Load格式
        String streamLoadData = dorisColumnMappingUtil.convertRowToStreamLoadFormat(csvRow, tableColumns);
        log.info("Stream Load数据: {}", streamLoadData);
        
        // 验证列数
        String[] columns = streamLoadData.split("\t");
        log.info("生成的列数: {} | 期望列数: {}", columns.length, tableColumns.size());
        
        assert columns.length == tableColumns.size() : "列数不匹配";
    }

    private List<DetailFieldConfig> createCallProtocolConfigs() {
        List<DetailFieldConfig> configs = new ArrayList<>();
        
        // 按照tb_ops_detail_field_config表中call协议的字段创建配置
        configs.add(createFieldConfig("action_map", 1));
        configs.add(createFieldConfig("called_number", 2));
        configs.add(createFieldConfig("calling_number", 3));
        configs.add(createFieldConfig("call_type_map", 4));
        configs.add(createFieldConfig("capture_day", 5));
        configs.add(createFieldConfig("capture_time", 6));
        configs.add(createFieldConfig("data_id", 7));
        configs.add(createFieldConfig("data_type_map", 8));
        configs.add(createFieldConfig("duration", 9));
        configs.add(createFieldConfig("end_time", 10));
        configs.add(createFieldConfig("start_time", 11));
        
        return configs;
    }

    private List<DetailFieldConfig> createEmailProtocolConfigs() {
        List<DetailFieldConfig> configs = new ArrayList<>();
        
        // 按照tb_ops_detail_field_config表中email协议的字段创建配置
        configs.add(createFieldConfig("action_map", 1));
        configs.add(createFieldConfig("auth_account", 2));
        configs.add(createFieldConfig("auth_type_map", 3));
        configs.add(createFieldConfig("capture_day", 4));
        configs.add(createFieldConfig("capture_time", 5));
        configs.add(createFieldConfig("data_id", 6));
        configs.add(createFieldConfig("data_type_map", 7));
        configs.add(createFieldConfig("mail_bcc", 8));
        configs.add(createFieldConfig("mail_cc", 9));
        configs.add(createFieldConfig("mail_date", 10));
        configs.add(createFieldConfig("mail_from", 11));
        configs.add(createFieldConfig("mail_to", 12));
        configs.add(createFieldConfig("spam_flag", 13));
        configs.add(createFieldConfig("sub_protocol_map", 14));
        configs.add(createFieldConfig("username", 15));
        
        return configs;
    }

    private DetailFieldConfig createFieldConfig(String fieldName, int id) {
        DetailFieldConfig config = new DetailFieldConfig();
        config.setId(id);
        config.setFieldName(fieldName);
        config.setIsExport(1);
        return config;
    }

    private Map<String, Object> createCallCsvData() {
        Map<String, Object> data = new HashMap<>();
        
        // 通用字段
        data.put("metric_id", "1");
        data.put("system_name", "搜索");
        data.put("data_level", "HBase");
        data.put("table_name", "明细表");
        data.put("protocol_type", "call");
        data.put("account", "***********");
        
        // 协议特定字段
        data.put("action_map", "呼叫");
        data.put("called_number", "***********");
        data.put("calling_number", "***********");
        data.put("call_type_map", "呼出");
        data.put("capture_day", "2025-01-20");
        data.put("capture_time", "10:00:00");
        data.put("data_id", "data001");
        data.put("data_type_map", "通话");
        data.put("duration", "300");
        data.put("end_time", "2025-01-20 10:05:00");
        data.put("start_time", "2025-01-20 10:00:00");
        
        return data;
    }

    private Map<String, Object> createEmailCsvData() {
        Map<String, Object> data = new HashMap<>();
        
        // 通用字段
        data.put("metric_id", "2");
        data.put("system_name", "搜索");
        data.put("data_level", "HBase");
        data.put("table_name", "明细表");
        data.put("protocol_type", "email");
        data.put("account", "<EMAIL>");
        
        // 协议特定字段
        data.put("action_map", "发送");
        data.put("auth_account", "<EMAIL>");
        data.put("auth_type_map", "SMTP");
        data.put("capture_day", "2025-01-20");
        data.put("capture_time", "14:30:00");
        data.put("data_id", "email001");
        data.put("data_type_map", "邮件");
        data.put("mail_bcc", "");
        data.put("mail_cc", "<EMAIL>");
        data.put("mail_date", "2025-01-20 14:30:00");
        data.put("mail_from", "<EMAIL>");
        data.put("mail_to", "<EMAIL>");
        data.put("spam_flag", "0");
        data.put("sub_protocol_map", "SMTP");
        data.put("username", "user");
        
        return data;
    }
}

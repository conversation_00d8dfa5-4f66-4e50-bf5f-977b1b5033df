{"args": [12], "result": {"id": 12, "metricModelName": "Fixed RADIUS数据差异分析", "metricName": "关联虚拟账号数量", "compareSystems": "综合搜索,全息档案", "description": "特定时间范围内某个Fixed RADIUS账号在LIS协议中关联到的虚拟账号", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, "method": "getByIdWithService", "class": "BusinessMetricConfigServiceImpl", "timestamp": "2025-04-15T21:02:01.877", "argsType": ["java.lang.Integer"]}
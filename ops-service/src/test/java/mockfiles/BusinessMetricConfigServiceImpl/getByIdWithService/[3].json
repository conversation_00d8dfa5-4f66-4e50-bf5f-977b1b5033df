{"args": [3], "result": {"id": 3, "metricModelName": "Email数据差异分析", "metricName": "关联RADIUS账号数量", "compareSystems": "综合搜索,全息档案,案件管理", "description": "Email账号进行上网行为时关联的RADIUS账号，包括fixed radius、mobile radius、fixed ip", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, "method": "getByIdWithService", "class": "BusinessMetricConfigServiceImpl", "timestamp": "2025-04-15T21:01:28.062", "argsType": ["java.lang.Integer"]}
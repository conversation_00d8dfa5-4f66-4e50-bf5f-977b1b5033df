{"args": [2], "result": {"id": 2, "metricModelName": "Email数据差异分析", "metricName": "通联账号及次数", "compareSystems": "综合搜索,全息档案,案件管理", "description": "Email账号在特定时间范围内通联的其他Email账号及通联次数", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, "method": "getByIdWithService", "class": "BusinessMetricConfigServiceImpl", "timestamp": "2025-04-15T21:01:25.286", "argsType": ["java.lang.Integer"]}
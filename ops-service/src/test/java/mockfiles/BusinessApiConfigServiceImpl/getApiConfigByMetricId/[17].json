{"args": [17], "result": [{"id": 45, "systemName": "综合搜索", "apiPath": "/senior/query.json?lang=zh_CN", "method": "POST", "paramsTemplate": {"readStatus": "", "dateFormat": "timestamp", "isFieldValAccurate": "", "fieldVal": "{\"resource_table_name\":\"(deye_v64_fixednetradius)\",\"auth_account\":\"#{account}\"}", "pageSize": 20, "tags": "{}", "filterId": 3, "onPage": 1, "aggsField": "", "startTime": 112233, "multiSortField": "{\"_score\":1}", "resultShowType": 1, "endTime": 445566, "fieldList": ["resource_table_name", "AND", "auth_account"], "timeLabel": "0", "containsDetail": true, "resourceTableNames": ["deye_v64_mobilenetradius", "deye_v64_fixednetradius"], "aggs": ""}, "responseMapping": {"getKey": [], "conditionKey": [], "data.total": 0, "is_kv": 0}, "metricId": 17}, {"id": 46, "systemName": "全息档案", "apiPath": "/radius_arc_detail/auth_record.json", "method": "GET", "paramsTemplate": {"arcId": "#{arcId}", "onPage": 1, "size": 100, "dateFormat": "stringDate", "startDay": "#{startDay}", "arcAccount": "#{account}", "ip": "", "endDay": "#{endDay}", "dateOption": 0, "arcAccountType": "1020001", "lang": "zh_CN", "arcType": "1"}, "responseMapping": {"getKey": [], "conditionKey": [], "data.detail.total": 0, "is_kv": 0}, "metricId": 17}, {"id": 47, "systemName": "案件管理", "apiPath": "/clue_hit/page_list.json?lang=zh_CN", "method": "POST", "paramsTemplate": {"noContentFilter": 0, "protocolGroupCode": 2101, "dateFormat": "timestamp", "protocolGroupName": "Fixed RADIUS(72)", "clueId": 333333, "secFilterParam": {}, "isReadNum": 0, "currentTime": 0, "unReadNum": 36, "total": 36, "onPage": 1, "size": 30, "clueName": "#{clueName}", "protocolCode": "", "caseId": 111111, "caseName": "#{caseName}", "filterNum": 0, "objectName": "#{objectName}", "startTime": 112233, "endTime": 445566, "objectId": 222222, "similarFilter": 0, "hitTotal": 36}, "responseMapping": {"getKey": [], "conditionKey": [], "data.hitTotal": 0, "is_kv": 0}, "metricId": 17}], "method": "getApiConfigByMetricId", "class": "BusinessApiConfigServiceImpl", "timestamp": "2025-04-15T21:02:17.756", "argsType": ["java.lang.Integer"]}
{"args": [12], "result": [{"id": 32, "systemName": "综合搜索", "apiPath": "/multi/query.json?lang=zh_DZ", "method": "POST", "paramsTemplate": {"readStatus": "", "fileName": "", "dateFormat": "timestamp", "pageSize": 20, "file": null, "onPage": 1, "spamFilter": 0, "isFilterSimilar": false, "secondQueryKeywords": [], "startTime": 112233, "keyword": "\"#{account}\"", "dimension": [], "resultType": 1, "order": "_score", "containsAttach": "", "tags": "", "asc": false, "resultShowType": 1, "aggregationField": "virtual_account", "endTime": 445566, "timeLabel": "0", "aggQueryType": 3, "resourceTableNames": ["deye_v64_email", "deye_v64_http", "deye_v64_ftp", "deye_v64_terminal", "deye_v64_im", "deye_v64_sns", "deye_v64_news", "deye_v64_engine", "deye_v64_remotectrl", "deye_v64_vpn", "deye_v64_tool", "deye_v64_travel", "deye_v64_lbs", "deye_v64_telnet", "deye_v64_multimedia", "deye_v64_entertainment", "deye_v64_shopping", "deye_v64_finance", "deye_v64_voip", "deye_v64_other", "deye_v64_call", "deye_v64_sms", "deye_v64_fax", "deye_v64_mobilenetradius", "deye_v64_fixednetradius"], "aggs": "", "resourceType": 0}, "responseMapping": {"getKey": [], "conditionKey": [], "data.fieldInfo.fieldCount.virtual_account": 2, "is_kv": 1}, "metricId": 12}, {"id": 33, "systemName": "全息档案", "apiPath": "/radius_arc_detail/get_virtual_account_info.json", "method": "GET", "paramsTemplate": {"arcId": "#{arcId}", "dateFormat": "stringDate", "startDay": "#{startDay}", "dataType": "", "dateOption": 0, "arcAccountType": 1020001, "onPage": 1, "size": 200, "sortType": "", "arcAccount": "#{account}", "endDay": "#{endDay}", "sortField": "", "lang": "zh_CN", "keyword": ""}, "responseMapping": {"getKey": ["virtualAccount", "behaviorNum"], "data.list": 1, "conditionKey": [], "is_kv": 1}, "metricId": 12}], "method": "getApiConfigByMetricId", "class": "BusinessApiConfigServiceImpl", "timestamp": "2025-04-15T21:02:01.905", "argsType": ["java.lang.Integer"]}
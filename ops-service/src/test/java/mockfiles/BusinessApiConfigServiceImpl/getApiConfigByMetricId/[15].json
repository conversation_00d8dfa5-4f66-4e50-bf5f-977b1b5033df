{"args": [15], "result": [{"id": 39, "systemName": "综合搜索", "apiPath": "/senior/query.json?lang=zh_CN", "method": "POST", "paramsTemplate": {"readStatus": "", "dateFormat": "timestamp", "isFieldValAccurate": "", "fieldVal": "{\"from_id\":\"#{account}\",\"to_id\":\"#{account}\"}", "pageSize": 20, "tags": "{}", "filterId": 6, "onPage": 1, "aggsField": "auth_account", "startTime": 112233, "multiSortField": "{\"_score\":1}", "resultShowType": 1, "aggregationField": "auth_account", "endTime": 445566, "fieldList": ["from_id", "OR", "to_id"], "timeLabel": "0", "resultType": 1, "containsDetail": true, "aggQueryType": 3, "resourceTableNames": ["deye_v64_im"], "aggs": ""}, "responseMapping": {"data.fieldInfo.fieldCount.auth_account": 2, "getKey": [], "conditionKey": [], "is_kv": 1}, "metricId": 15}, {"id": 40, "systemName": "全息档案", "apiPath": "/common_arc_detail/auth_record.json", "method": "GET", "paramsTemplate": {"arcId": "#{arcId}", "dateFormat": "stringDate", "startDay": "#{startDay}", "dataType": "", "dateOption": 0, "virtualAppType": 444444, "arcAccountType": 444444, "virtualAccountAppType": 444444, "type": 0, "arcType": 6, "archiveType": 6, "onPage": 1, "size": 100, "sortType": "", "arcAccount": "#{account}", "endDay": "#{endDay}", "sortField": "", "lang": "zh_CN", "keyword": "", "virtualAccount": "#{account}"}, "responseMapping": {"getKey": ["account", "num"], "data.list": 1, "conditionKey": [], "is_kv": 1}, "metricId": 15}, {"id": 41, "systemName": "案件管理", "apiPath": "/clue_hit/statistics_histogram.json", "method": "GET", "paramsTemplate": {"arcId": "#{arcId}", "dateFormat": "stringDate", "startDay": "#{startDay}", "dataType": "", "dateOption": 0, "virtualAppType": 1030001, "arcAccountType": 1030001, "virtualAccountAppType": 1030001, "type": 0, "arcType": 6, "archiveType": 6, "onPage": 1, "size": 1000, "sortType": "", "arcAccount": "#{account}", "endDay": "#{endDay}", "sortField": "", "lang": "zh_CN", "keyword": "", "virtualAccount": "#{account}"}, "responseMapping": {"getKey": ["statisticsItem", "mainTimeCount"], "data.list": 1, "conditionKey": [], "is_kv": 1}, "metricId": 15}], "method": "getApiConfigByMetricId", "class": "BusinessApiConfigServiceImpl", "timestamp": "2025-04-15T21:02:11.439", "argsType": ["java.lang.Integer"]}
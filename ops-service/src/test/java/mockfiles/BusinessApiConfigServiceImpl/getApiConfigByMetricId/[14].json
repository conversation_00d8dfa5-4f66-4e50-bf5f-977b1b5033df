{"args": [14], "result": [{"id": 37, "systemName": "综合搜索", "apiPath": "/multi/query.json?lang=zh_DZ", "method": "POST", "paramsTemplate": {"readStatus": "", "fileName": "", "dateFormat": "timestamp", "pageSize": 20, "file": null, "onPage": 1, "spamFilter": 0, "isFilterSimilar": false, "secondQueryKeywords": [], "startTime": 112233, "keyword": "\"#{account}\"", "dimension": [], "resultType": 1, "order": "_score", "containsAttach": "", "tags": "", "asc": false, "resultShowType": 1, "aggregationField": "virtual_account", "endTime": 445566, "timeLabel": "0", "aggQueryType": 3, "resourceTableNames": ["deye_v64_im"], "aggs": "", "resourceType": 0}, "responseMapping": {"getKey": [], "conditionKey": [], "data.fieldInfo.fieldCount.virtual_account": 2, "is_kv": 1, "excludeKey": ["#{account}"]}, "metricId": 14}, {"id": 38, "systemName": "全息档案", "apiPath": "/im_arc_detail/get_frequent_number_info.json", "method": "GET", "paramsTemplate": {"arcId": "#{arcId}", "dateFormat": "stringDate", "startDay": "#{startDay}", "dataType": "", "dateOption": 0, "virtualAppType": "444444", "arcAccountType": "444444", "virtualAccountAppType": "444444", "arcType": 6, "archiveType": 6, "size": 200, "arcAccount": "#{account}", "endDay": "#{endDay}", "lang": "zh_CN", "virtualAccount": "#{account}"}, "responseMapping": {"getKey": ["account", "num"], "data.account": 1, "conditionKey": [], "is_kv": 1}, "metricId": 14}], "method": "getApiConfigByMetricId", "class": "BusinessApiConfigServiceImpl", "timestamp": "2025-04-15T21:02:10.322", "argsType": ["java.lang.Integer"]}
{"args": [2], "result": [{"id": 4, "systemName": "综合搜索", "apiPath": "/multi/query.json?lang=zh_CN", "method": "POST", "paramsTemplate": {"readStatus": "", "fileName": "", "dateFormat": "timestamp", "pageSize": 20, "file": null, "onPage": 1, "spamFilter": 0, "isFilterSimilar": false, "secondQueryKeywords": [], "startTime": 112233, "keyword": "\"#{account}\"", "dimension": [], "resultType": 1, "order": "_score", "containsAttach": "", "tags": "", "asc": false, "resultShowType": 1, "aggregationField": "virtual_account", "endTime": 445566, "timeLabel": "0", "aggQueryType": 3, "resourceTableNames": ["deye_v64_email"], "aggs": "", "resourceType": 0}, "responseMapping": {"getKey": [], "conditionKey": [], "data.fieldInfo.fieldCount.virtual_account": 2, "is_kv": 1, "excludeKey": ["#{account}"]}, "metricId": 2}, {"id": 5, "systemName": "全息档案", "apiPath": "/email_arc_detail/get_frequent_email_info.json", "method": "GET", "paramsTemplate": {"arcId": "#{arcId}", "dateFormat": "stringDate", "startDay": "#{startDay}", "dataType": "", "dateOption": 0, "arcAccountType": "", "arcType": 2, "archiveType": 2, "size": 200, "arcAccount": "#{account}", "endDay": "#{endDay}", "lang": "zh_CN", "virtualAccount": "#{account}"}, "responseMapping": {"getKey": ["email", "num"], "conditionKey": [], "data.emails": 1, "is_kv": 1}, "metricId": 2}, {"id": 6, "systemName": "案件管理", "apiPath": "/clue_hit/statistics_histogram.json", "method": "GET", "paramsTemplate": {"orderType": 0, "protocolGroupCode": 101, "dateFormat": "timestamp", "mainStartTime": 112233, "clueId": "#{clueId}", "emailAddrType": 2, "onPage": 1, "size": 2000, "protocolCode": "1010001,1019999", "compareEndTime": 0, "statisticsItem": 9, "lang": "zh_CN", "compareStartTime": 0, "mainEndTime": 445566}, "responseMapping": {"getKey": ["statisticsItem", "mainTimeCount"], "data.list": 1, "conditionKey": [], "is_kv": 1}, "metricId": 2}], "method": "getApiConfigByMetricId", "class": "BusinessApiConfigServiceImpl", "timestamp": "2025-04-15T21:01:25.519", "argsType": ["java.lang.Integer"]}
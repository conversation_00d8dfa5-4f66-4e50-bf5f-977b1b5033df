{"args": [11], "result": [{"id": 29, "systemName": "综合搜索", "apiPath": "/multi/query.json?lang=zh_CN", "method": "POST", "paramsTemplate": {"readStatus": "", "fileName": "", "dateFormat": "timestamp", "pageSize": 20, "file": null, "onPage": 1, "spamFilter": 0, "isFilterSimilar": false, "secondQueryKeywords": [], "startTime": 112233, "keyword": "\"#{account}\"", "dimension": [], "resultType": 1, "order": "_score", "containsAttach": "", "tags": "", "asc": false, "resultShowType": 1, "aggregationField": "auth_account", "endTime": 445566, "timeLabel": "0", "aggQueryType": 3, "resourceTableNames": ["deye_v64_email", "deye_v64_http", "deye_v64_ftp", "deye_v64_terminal", "deye_v64_im", "deye_v64_sns", "deye_v64_news", "deye_v64_engine", "deye_v64_remotectrl", "deye_v64_vpn", "deye_v64_tool", "deye_v64_travel", "deye_v64_lbs", "deye_v64_telnet", "deye_v64_multimedia", "deye_v64_entertainment", "deye_v64_shopping", "deye_v64_finance", "deye_v64_voip", "deye_v64_other"], "aggs": "", "resourceType": 0}, "responseMapping": {"data.fieldInfo.fieldCount.auth_account": 2, "getKey": ["#{account}"], "conditionKey": [], "is_kv": 0}, "metricId": 11}, {"id": 30, "systemName": "全息档案", "apiPath": "/common_arc_detail/get_active_trend.json", "method": "GET", "paramsTemplate": {"arcId": "#{arcId}", "dateFormat": "stringDate", "startDay": "#{startDay}", "arcAccount": "#{account}", "endDay": "#{endDay}", "behavior_type": "2", "arcAccountType": "1020001", "lang": "zh_CN", "arcType": "1"}, "responseMapping": {"getKey": ["num"], "data.pr": 1, "conditionKey": [], "is_kv": 0}, "metricId": 11}], "method": "getApiConfigByMetricId", "class": "BusinessApiConfigServiceImpl", "timestamp": "2025-04-15T21:01:55.303", "argsType": ["java.lang.Integer"]}
{"args": [10], "result": [{"id": 26, "systemName": "综合搜索", "apiPath": "/senior/query.json?lang=zh_CN", "method": "POST", "paramsTemplate": {"readStatus": "", "dateFormat": "timestamp", "isFieldValAccurate": "", "fieldVal": "{\"auth_account\":\"#{account}\"}", "pageSize": 20, "tags": "{}", "filterId": 22, "onPage": 1, "aggsField": "", "startTime": 112233, "multiSortField": "{\"capture_time\":1}", "resultShowType": 1, "endTime": 445566, "fieldList": ["auth_account"], "timeLabel": "0", "containsDetail": true, "resourceTableNames": ["deye_v64_location"], "aggs": ""}, "responseMapping": {"getKey": [], "conditionKey": [], "data.total": 0, "is_kv": 0}, "metricId": 10}, {"id": 27, "systemName": "全息档案", "apiPath": "/phone_arc_detail/his_trajectory.json", "method": "GET", "paramsTemplate": {"arcId": "#{arcId}", "trajectoryType": 1, "dateFormat": "stringDate", "startDay": "#{startDay}", "dateOption": 0, "arcAccountType": "1020004", "arcType": "5", "archiveType": "5", "onPage": 1, "size": 20, "sortType": "", "arcAccount": "#{account}", "endDay": "#{endDay}", "sortField": "", "lang": "zh_CN", "keyword": ""}, "responseMapping": {"getKey": [], "conditionKey": [], "data.total": 0, "is_kv": 0}, "metricId": 10}, {"id": 28, "systemName": "案件管理", "apiPath": "/clue_hit/history_trajectory.json", "method": "GET", "paramsTemplate": {"onPage": 1, "phoneNumber": "#{account}", "size": 20, "dateFormat": "timestamp", "startTime": 112233, "endTime": 445566, "lang": "zh_CN", "type": 1, "clueId": "#{clueId}", "keyword": ""}, "responseMapping": {"getKey": [], "conditionKey": [], "data.total": 0, "is_kv": 0}, "metricId": 10}], "method": "getApiConfigByMetricId", "class": "BusinessApiConfigServiceImpl", "timestamp": "2025-04-15T21:01:53.884", "argsType": ["java.lang.Integer"]}
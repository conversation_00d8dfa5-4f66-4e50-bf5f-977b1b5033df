{"args": [{"id": 10, "metricModelName": "号码数据差异分析", "metricName": "基站位置", "compareSystems": "综合搜索,全息档案,案件管理", "description": "特定时间范围内，某个号码的基站位置变化记录", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1910643127101321217, "metricId": 10, "accountType": "PHONE", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "12", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643127101321218, "metricId": 10, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "12", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643127101321219, "metricId": 10, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "12", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643127101321220, "metricId": 10, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "12", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643127101321221, "metricId": 10, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "12", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643127101321222, "metricId": 10, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "12", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643127101321223, "metricId": 10, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "12", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643127101321224, "metricId": 10, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "12", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643127101321225, "metricId": 10, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "12", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643127101321226, "metricId": 10, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "12", "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricId", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:53.867", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}
{"args": [{"id": 15, "metricModelName": "IM账号数据差异分析", "metricName": "关联RADIUS账号数量", "compareSystems": "综合搜索,全息档案", "description": "特定时间范围内，某IM账号上网使用的网络认证账号数量", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1910643133254365186, "metricId": 15, "accountType": "IM", "accountValue": "anon-5306aa47b65377b", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "19", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133254365187, "metricId": 15, "accountType": "IM", "accountValue": "*********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "19", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133254365188, "metricId": 15, "accountType": "IM", "accountValue": "*********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "19", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133254365189, "metricId": 15, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "19", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133254365190, "metricId": 15, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "19", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133254365191, "metricId": 15, "accountType": "IM", "accountValue": "*********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "19", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133254365192, "metricId": 15, "accountType": "IM", "accountValue": "*********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "19", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133254365193, "metricId": 15, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "19", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133254365194, "metricId": 15, "accountType": "IM", "accountValue": "*********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "19", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133254365195, "metricId": 15, "accountType": "IM", "accountValue": "*********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "19", "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricId", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:02:11.424", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}
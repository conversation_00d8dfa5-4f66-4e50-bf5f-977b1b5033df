{"args": [{"id": 5, "metricModelName": "号码数据差异分析", "metricName": "上网明细", "compareSystems": "综合搜索,全息档案", "description": "某个号码特定时间范围内作为认证账号在各LIS数据中出现的次数", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1910643122294648833, "metricId": 5, "accountType": "PHONE", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "7", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122294648834, "metricId": 5, "accountType": "PHONE", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "7", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122294648835, "metricId": 5, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "7", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122294648836, "metricId": 5, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "7", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122294648837, "metricId": 5, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "7", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122294648838, "metricId": 5, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "7", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122294648839, "metricId": 5, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "7", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122294648840, "metricId": 5, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "7", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122294648841, "metricId": 5, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "7", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122294648842, "metricId": 5, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "7", "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricId", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:34.893", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}
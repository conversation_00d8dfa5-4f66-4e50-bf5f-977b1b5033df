{"args": [{"id": 13, "metricModelName": "IM账号数据差异分析", "metricName": "活跃次数", "compareSystems": "综合搜索,全息档案", "description": "特定时间范围内，某个IM账号在系统内出现的次数", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1910643130779725826, "metricId": 13, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "1", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643130779725827, "metricId": 13, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "1", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643130779725828, "metricId": 13, "accountType": "IM", "accountValue": "********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "1", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643130779725829, "metricId": 13, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "1", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643130779725830, "metricId": 13, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "1", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643130779725831, "metricId": 13, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "1", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643130779725832, "metricId": 13, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "1", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643130779725833, "metricId": 13, "accountType": "IM", "accountValue": "*********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "1", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643130779725834, "metricId": 13, "accountType": "IM", "accountValue": "*********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "1", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643130779725835, "metricId": 13, "accountType": "IM", "accountValue": "*********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "1", "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricId", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:02:08.584", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}
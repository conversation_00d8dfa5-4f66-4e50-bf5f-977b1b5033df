{"args": [{"id": 3, "metricModelName": "Email数据差异分析", "metricName": "关联RADIUS账号数量", "compareSystems": "综合搜索,全息档案,案件管理", "description": "Email账号进行上网行为时关联的RADIUS账号，包括fixed radius、mobile radius、fixed ip", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1910643121325764609, "metricId": 3, "accountType": "EMAIL", "accountValue": "infog@nardi349", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "5", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121325764610, "metricId": 3, "accountType": "EMAIL", "accountValue": "infog@nardi49", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "5", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121325764611, "metricId": 3, "accountType": "EMAIL", "accountValue": "infog@nardi37", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "5", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121325764612, "metricId": 3, "accountType": "EMAIL", "accountValue": "infog@nardi406", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "5", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121325764613, "metricId": 3, "accountType": "EMAIL", "accountValue": "infog@nardi205", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "5", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121325764614, "metricId": 3, "accountType": "EMAIL", "accountValue": "infog@nardi981", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "5", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121325764615, "metricId": 3, "accountType": "EMAIL", "accountValue": "infog@nardi709", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "5", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121325764616, "metricId": 3, "accountType": "EMAIL", "accountValue": "infog@nardi833", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "5", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121325764617, "metricId": 3, "accountType": "EMAIL", "accountValue": "<EMAIL>", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "5", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121325764618, "metricId": 3, "accountType": "EMAIL", "accountValue": "<EMAIL>", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "5", "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricId", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:28.096", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}
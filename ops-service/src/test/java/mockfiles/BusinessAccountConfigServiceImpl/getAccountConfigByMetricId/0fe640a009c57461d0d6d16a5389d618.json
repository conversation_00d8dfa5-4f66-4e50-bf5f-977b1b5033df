{"args": [{"id": 4, "metricModelName": "号码数据差异分析", "metricName": "上网次数", "compareSystems": "综合搜索,全息档案", "description": "某个号码特定时间范围内在LIS数据中作为认证账号出现的次数", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1910643121904578561, "metricId": 4, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "6", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121841664001, "metricId": 4, "accountType": "PHONE", "accountValue": "10018", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "6", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121904578562, "metricId": 4, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "6", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121841664002, "metricId": 4, "accountType": "PHONE", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "6", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121904578563, "metricId": 4, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "6", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121841664003, "metricId": 4, "accountType": "PHONE", "accountValue": "140122", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "6", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121904578564, "metricId": 4, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "6", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121841664004, "metricId": 4, "accountType": "PHONE", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "6", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121841664005, "metricId": 4, "accountType": "PHONE", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "6", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643121841664006, "metricId": 4, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "6", "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricId", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:30.011", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}
{"args": [{"id": 11, "metricModelName": "Fixed RADIUS数据差异分析", "metricName": "访问网络次数", "compareSystems": "综合搜索,全息档案", "description": "特定时间范围内Fixed RADIUS账号作为认证账号出现在LIS协议数据中的次数", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1910643128409944066, "metricId": 11, "accountType": "RADIUS", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "2", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643128409944067, "metricId": 11, "accountType": "RADIUS", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "2", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643128409944068, "metricId": 11, "accountType": "RADIUS", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "2", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643128409944069, "metricId": 11, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "2", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643128409944070, "metricId": 11, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "2", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643128409944071, "metricId": 11, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "2", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643128409944072, "metricId": 11, "accountType": "RADIUS", "accountValue": "0026954274p", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "2", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643128409944073, "metricId": 11, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "2", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643128409944074, "metricId": 11, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "2", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643128409944075, "metricId": 11, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "2", "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricId", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:55.282", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}
{"args": [{"id": 10, "metricModelName": "号码数据差异分析", "metricName": "基站位置", "compareSystems": "综合搜索,全息档案,案件管理", "description": "特定时间范围内，某个号码的基站位置变化记录", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1902683268504031240, "metricId": 10, "accountType": "PHONE", "accountValue": "**************", "sampleWeight": 100, "sampleStrategy": "FIXED", "caseParam": {"clueId": "119102"}, "dynamic_condition_id": null, "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricIdWithStaticAccount", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:53.877", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}
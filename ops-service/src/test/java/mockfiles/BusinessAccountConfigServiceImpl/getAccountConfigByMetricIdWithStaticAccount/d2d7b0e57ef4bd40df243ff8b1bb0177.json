{"args": [{"id": 5, "metricModelName": "号码数据差异分析", "metricName": "上网明细", "compareSystems": "综合搜索,全息档案", "description": "某个号码特定时间范围内作为认证账号在各LIS数据中出现的次数", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [], "method": "getAccountConfigByMetricIdWithStaticAccount", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:34.901", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}
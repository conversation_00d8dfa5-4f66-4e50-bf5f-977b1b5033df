{"args": [{"id": 17, "metricModelName": "Fixed RADIUS数据差异分析", "metricName": "认证记录", "compareSystems": "综合搜索,全息档案", "description": "特定时间范围内，某个Fixed RADIUS账号的认证记录", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1902914531580071940, "metricId": 17, "accountType": "RADIUS", "accountValue": "pengxiaIP", "sampleWeight": 100, "sampleStrategy": "FIXED", "caseParam": {"caseId": 583, "clueId": 119096, "objectId": 17849}, "dynamic_condition_id": null, "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricIdWithStaticAccount", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:02:17.750", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}
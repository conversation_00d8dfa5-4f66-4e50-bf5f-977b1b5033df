{"args": [{"id": 2, "metricModelName": "Email数据差异分析", "metricName": "通联账号及次数", "compareSystems": "综合搜索,全息档案,案件管理", "description": "Email账号在特定时间范围内通联的其他Email账号及通联次数", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1901935740991504395, "metricId": 2, "accountType": "EMAIL", "accountValue": "<EMAIL>", "sampleWeight": 100, "sampleStrategy": "FIXED", "caseParam": {"clueId": "119241"}, "dynamic_condition_id": null, "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricIdWithStaticAccount", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:25.456", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}
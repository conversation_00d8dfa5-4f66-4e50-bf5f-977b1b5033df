{"args": [{"id": 11, "metricModelName": "Fixed RADIUS数据差异分析", "metricName": "访问网络次数", "compareSystems": "综合搜索,全息档案", "description": "特定时间范围内Fixed RADIUS账号作为认证账号出现在LIS协议数据中的次数", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1902683268504031237, "metricId": 11, "accountType": "RADIUS", "accountValue": "Gljhomeradius", "sampleWeight": 100, "sampleStrategy": "FIXED", "caseParam": null, "dynamic_condition_id": null, "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricIdWithStaticAccount", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:55.293", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}
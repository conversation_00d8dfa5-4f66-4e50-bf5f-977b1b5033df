{"result": {"url": "http://192.168.80.158:8109/archives_web/email_arc_detail/get_frequent_email_info.json?arcId=cd4777f8266ad1569e1d4a428537dd19&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&arcAccountType=&arcType=2&archiveType=2&size=200&arcAccount=infog@nardi432&endDay=2025-04-10&lang=zh_CN&virtualAccount=infog@nardi432", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 0, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8109/archives_web/email_arc_detail/get_frequent_email_info.json?arcId=cd4777f8266ad1569e1d4a428537dd19&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&arcAccountType=&arcType=2&archiveType=2&size=200&arcAccount=infog%40nardi432&endDay=2025-04-10&lang=zh_CN&virtualAccount=infog%40nardi432\""], "timestamp": "2025-04-15T21:01:26.302", "argsType": ["java.lang.String"]}
{"result": {"url": "http://192.168.80.158:8105/case-system/clue_hit/statistics_histogram.json?orderType=0&protocolGroupCode=101&dateFormat=timestamp&mainStartTime=1744214400000&clueId=119241&emailAddrType=2&onPage=1&size=2000&protocolCode=1010001,1019999&compareEndTime=0&statisticsItem=9&lang=zh_CN&compareStartTime=0&mainEndTime=1744300799999", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 1, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8105/case-system/clue_hit/statistics_histogram.json?orderType=0&protocolGroupCode=101&dateFormat=timestamp&mainStartTime=1744214400000&clueId=119241&emailAddrType=2&onPage=1&size=2000&protocolCode=1010001%2C1019999&compareEndTime=0&statisticsItem=9&lang=zh_CN&compareStartTime=0&mainEndTime=1744300799999\""], "timestamp": "2025-04-15T21:01:25.803", "argsType": ["java.lang.String"]}
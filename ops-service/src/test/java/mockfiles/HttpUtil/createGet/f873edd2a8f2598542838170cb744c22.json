{"result": {"url": "http://192.168.80.158:8109/archives_web/phone_arc_detail/get_virtual_account_info.json?arcId=99bb5e5ab79eed67e1301e0048c5df15&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&onPage=1&size=500&sortType=&arcAccount=************&endDay=2025-04-10&sortField=&lang=zh_CN&keyword=", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 0, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8109/archives_web/phone_arc_detail/get_virtual_account_info.json?arcId=99bb5e5ab79eed67e1301e0048c5df15&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&onPage=1&size=500&sortType=&arcAccount=************&endDay=2025-04-10&sortField=&lang=zh_CN&keyword=\""], "timestamp": "2025-04-15T21:01:40.499", "argsType": ["java.lang.String"]}
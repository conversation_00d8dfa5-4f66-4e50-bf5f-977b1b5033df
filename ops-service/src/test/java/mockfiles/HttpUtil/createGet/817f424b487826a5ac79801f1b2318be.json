{"result": {"url": "http://192.168.80.158:8109/archives_web/phone_arc_detail/get_virtual_account_info.json?arcId=95a05b1edc4be6d272d12618f3028c7f&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&onPage=1&size=500&sortType=&arcAccount=************&endDay=2025-04-10&sortField=&lang=zh_CN&keyword=", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 1, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8109/archives_web/phone_arc_detail/get_virtual_account_info.json?arcId=95a05b1edc4be6d272d12618f3028c7f&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&onPage=1&size=500&sortType=&arcAccount=************&endDay=2025-04-10&sortField=&lang=zh_CN&keyword=\""], "timestamp": "2025-04-15T21:01:40.900", "argsType": ["java.lang.String"]}
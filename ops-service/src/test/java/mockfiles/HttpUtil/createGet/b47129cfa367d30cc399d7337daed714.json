{"result": {"url": "http://192.168.80.158:8109/archives_web/email_arc_detail/get_frequent_email_info.json?arcId=53bae6e5e62f67f9a4c0171cb4a65405&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&arcAccountType=&arcType=2&archiveType=2&size=200&arcAccount=infog@nardi76&endDay=2025-04-10&lang=zh_CN&virtualAccount=infog@nardi76", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 0, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8109/archives_web/email_arc_detail/get_frequent_email_info.json?arcId=53bae6e5e62f67f9a4c0171cb4a65405&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&arcAccountType=&arcType=2&archiveType=2&size=200&arcAccount=infog%40nardi76&endDay=2025-04-10&lang=zh_CN&virtualAccount=infog%40nardi76\""], "timestamp": "2025-04-15T21:01:26.672", "argsType": ["java.lang.String"]}
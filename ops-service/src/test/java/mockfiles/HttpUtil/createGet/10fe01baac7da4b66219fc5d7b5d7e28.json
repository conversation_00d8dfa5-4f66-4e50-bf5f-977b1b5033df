{"result": {"url": "http://192.168.80.158:8109/archives_web/common_arc_detail/get_active_trend.json?arcId=3c1cf13e96d82a42c6303b7d69120a07&dateFormat=stringDate&startDay=2025-04-10&arcAccount=<EMAIL>&endDay=2025-04-10&dataType=&behavior_type=2&arcAccountType=&lang=zh_CN&arcType=2", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 2, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8109/archives_web/common_arc_detail/get_active_trend.json?arcId=3c1cf13e96d82a42c6303b7d69120a07&dateFormat=stringDate&startDay=2025-04-10&arcAccount=hputuzmf%40lowfyr.com&endDay=2025-04-10&dataType=&behavior_type=2&arcAccountType=&lang=zh_CN&arcType=2\""], "timestamp": "2025-04-15T21:01:24.133", "argsType": ["java.lang.String"]}
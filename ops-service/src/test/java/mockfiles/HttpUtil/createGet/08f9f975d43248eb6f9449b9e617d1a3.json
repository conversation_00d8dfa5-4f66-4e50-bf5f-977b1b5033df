{"result": {"url": "http://192.168.80.158:8109/archives_web/radius_arc_detail/get_virtual_account_info.json?arcId=1bdf85daacebd9817ec4baa8588f6031&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&arcAccountType=1020001&onPage=1&size=200&sortType=&arcAccount=**********&endDay=2025-04-10&sortField=&lang=zh_CN&keyword=", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 0, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8109/archives_web/radius_arc_detail/get_virtual_account_info.json?arcId=1bdf85daacebd9817ec4baa8588f6031&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&arcAccountType=1020001&onPage=1&size=200&sortType=&arcAccount=**********&endDay=2025-04-10&sortField=&lang=zh_CN&keyword=\""], "timestamp": "2025-04-15T21:02:03.035", "argsType": ["java.lang.String"]}
{"result": {"url": "http://192.168.80.158:8109/archives_web/arc_relation/get_relation_expansion.json?arcId=06182de3f9a776c499eabb38624821c8&authAccount=&dateFormat=stringDate&connectType=0&dateOption=0&arcType=5&expansionRule=1,2,3&onPage=1&size=200&minLinkCount=1&startTime=2025-04-10&endTime=2025-04-10&lang=zh_CN&account=************", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 1, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8109/archives_web/arc_relation/get_relation_expansion.json?arcId=06182de3f9a776c499eabb38624821c8&authAccount=&dateFormat=stringDate&connectType=0&dateOption=0&arcType=5&expansionRule=1%2C2%2C3&onPage=1&size=200&minLinkCount=1&startTime=2025-04-10&endTime=2025-04-10&lang=zh_CN&account=************\""], "timestamp": "2025-04-15T21:01:48.646", "argsType": ["java.lang.String"]}
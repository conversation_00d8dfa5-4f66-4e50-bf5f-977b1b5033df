{"result": {"url": "http://**************:8109/archives_web/radius_arc_detail/auth_record.json?arcId=741620e87592bd54539011bdba1c3f6f&onPage=1&size=100&dateFormat=stringDate&startDay=2025-04-10&arcAccount=**********&ip=&endDay=2025-04-10&dateOption=0&arcAccountType=1020001&lang=zh_CN&arcType=1", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 0, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://**************:8109/archives_web/radius_arc_detail/auth_record.json?arcId=741620e87592bd54539011bdba1c3f6f&onPage=1&size=100&dateFormat=stringDate&startDay=2025-04-10&arcAccount=**********&ip=&endDay=2025-04-10&dateOption=0&arcAccountType=1020001&lang=zh_CN&arcType=1\""], "timestamp": "2025-04-15T21:02:18.630", "argsType": ["java.lang.String"]}
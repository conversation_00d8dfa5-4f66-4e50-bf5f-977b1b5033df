{
  "result" : {
    "url" : "http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN",
    "method" : "POST",
    "connection" : null,
    "keepAlive" : true
  },
  "executionTime" : 1,
  "method" : "createPost",
  "className" : "cn.hutool.http.HttpUtil",
  "arguments" : [ "\"http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN\"" ],
  "timestamp" : "2025-04-15T21:01:23.484",
  "argsType" : [ "java.lang.String" ]
}
{
  "result" : {
    "url" : "http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN",
    "method" : "POST",
    "connection" : null,
    "keepAlive" : true
  },
  "executionTime" : 1,
  "method" : "createPost",
  "className" : "cn.hutool.http.HttpUtil",
  "arguments" : [ "\"http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN\"" ],
  "timestamp" : "2025-04-15T21:01:23.483",
  "argsType" : [ "java.lang.String" ]
}
{
  "result" : {
    "url" : "http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN",
    "method" : "POST",
    "connection" : null,
    "keepAlive" : true
  },
  "executionTime" : 0,
  "method" : "createPost",
  "className" : "cn.hutool.http.HttpUtil",
  "arguments" : [ "\"http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN\"" ],
  "timestamp" : "2025-04-15T21:01:23.482",
  "argsType" : [ "java.lang.String" ]
}
{
  "result" : {
    "url" : "http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN",
    "method" : "POST",
    "connection" : null,
    "keepAlive" : true
  },
  "executionTime" : 1,
  "method" : "createPost",
  "className" : "cn.hutool.http.HttpUtil",
  "arguments" : [ "\"http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN\"" ],
  "timestamp" : "2025-04-15T21:01:23.482",
  "argsType" : [ "java.lang.String" ]
}
{
  "result" : {
    "url" : "http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN",
    "method" : "POST",
    "connection" : null,
    "keepAlive" : true
  },
  "executionTime" : 0,
  "method" : "createPost",
  "className" : "cn.hutool.http.HttpUtil",
  "arguments" : [ "\"http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN\"" ],
  "timestamp" : "2025-04-15T21:01:23.481",
  "argsType" : [ "java.lang.String" ]
}
{
  "result" : {
    "url" : "http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN",
    "method" : "POST",
    "connection" : null,
    "keepAlive" : true
  },
  "executionTime" : 1,
  "method" : "createPost",
  "className" : "cn.hutool.http.HttpUtil",
  "arguments" : [ "\"http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN\"" ],
  "timestamp" : "2025-04-15T21:01:23.481",
  "argsType" : [ "java.lang.String" ]
}
{
  "result" : {
    "url" : "http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN",
    "method" : "POST",
    "connection" : null,
    "keepAlive" : true
  },
  "executionTime" : 1,
  "method" : "createPost",
  "className" : "cn.hutool.http.HttpUtil",
  "arguments" : [ "\"http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN\"" ],
  "timestamp" : "2025-04-15T21:01:23.479",
  "argsType" : [ "java.lang.String" ]
}
{
  "result" : {
    "url" : "http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN",
    "method" : "POST",
    "connection" : null,
    "keepAlive" : true
  },
  "executionTime" : 1,
  "method" : "createPost",
  "className" : "cn.hutool.http.HttpUtil",
  "arguments" : [ "\"http://192.168.80.158:8888/search/multi/query.json?lang=zh_CN\"" ],
  "timestamp" : "2025-04-15T21:01:23.479",
  "argsType" : [ "java.lang.String" ]
}

{"result": {"status": 200, "ok": true, "cookies": [], "gzip": false, "deflate": false, "chunked": true, "cookieStr": null}, "executionTime": 192, "method": "execute", "className": "cn.hutool.http.HttpRequest", "arguments": ["{\"arcId\":\"3a6d1eddea0189996e7aca2559cd70a6\",\"dateFormat\":\"stringDate\",\"startDay\":\"2025-04-10\",\"dataType\":\"\",\"dateOption\":0,\"arcAccountType\":\"\",\"arcType\":2,\"archiveType\":2,\"size\":200,\"arcAccount\":\"<EMAIL>\",\"endDay\":\"2025-04-10\",\"lang\":\"zh_CN\",\"virtualAccount\":\"<EMAIL>\"}"], "timestamp": "2025-04-15T21:01:26.072", "argsType": ["com.alibaba.fastjson.JSONObject"]}
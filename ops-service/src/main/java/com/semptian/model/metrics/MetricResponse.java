package com.semptian.model.metrics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
public class MetricResponse {
    //指标ID
    private Long metricId;
    //指标名称
    private String name;
    //指标类型
    private String monitorType;
    //指标描述
    private String description;
    //整体差异率
    private Double overallDiffRate;
    //整体差异率阈值
    private Double threshold;
    //是否超过阈值
    private Boolean isOverThreshold;
    //对比的系统类型
    private List<RelatedSystem> relatedSystems;

    @Data
    @Builder
    @AllArgsConstructor
    public static class RelatedSystem {
        //系统ID
        private Integer appId;
        //系统名称
        private String value;
    }
}
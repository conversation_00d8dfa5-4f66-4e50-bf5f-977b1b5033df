package com.semptian.model.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/8
 * @Description
 **/
@Data
@ApiModel
public class PositionModel {

    @ApiModelProperty(value = "职位id,当有职位id的时候为编辑，无id时为添加")
    private Integer id;

    @ApiModelProperty(value = "职位名称")
    private String postName;

    @ApiModelProperty(value = "描述")
    private String describe;

    @ApiModelProperty(value = "当前用户id")
    private Long curUser;

    @ApiModelProperty(value = "职位类型 0-超级管理员;1-操作;2-审批;3-审计")
    private Integer postType;

    @ApiModelProperty(value = "职位对应的应用权限id,多个id之间用逗号分隔")
    private String appIds;

    @ApiModelProperty(value = "职位对应的菜单权限id,多个id之间用逗号分隔")
    private String menuIds;

    @ApiModelProperty(value = "职位对应的操作权限id,多个id之间用逗号分隔")
    private String operateIds;

    @ApiModelProperty(value = "是否为其他类型的管理员")
    private Integer isManager;

    private String changeName;
}

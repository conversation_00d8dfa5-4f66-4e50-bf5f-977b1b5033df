package com.semptian.model.finger;

import lombok.Data;

import java.util.List;

/**
 * @author: SunQi
 * @create: 2021/03/10
 * desc: 指纹添加模型
 **/
@Data
public class FingerModel {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 手指编号
     */
    private Integer fingerNum;

    /**
     * 指纹信息， 一个手指可以录入3个指纹
     */
    private List<String> fingerDesc;

    /**
     * 创建时间
     */
    private Long createTime;
}

package com.semptian.model.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户登录模型
 *
 * <AUTHOR>
 * @date 2020/2/26 17:40
 */
@ApiModel(value = "用户登录对象")
@Data
public class UserLoginModel {

    @ApiModelProperty(value = "账号", example = "gui")
    private String account;

    @ApiModelProperty(value = "密码", example = "rwerwer")
    private String password;

    private boolean backLogin;
}

package com.semptian.model.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/23
 * @Description
 **/
@ApiModel(value = "用户密码修改模型")
@Data
public class UserPasswordModel {

    @ApiModelProperty(value = "用户账号")
    private String account;

    @ApiModelProperty(value = "用户老密码")
    private String oldPassword;

    @ApiModelProperty("更新密码")
    private String password;
}

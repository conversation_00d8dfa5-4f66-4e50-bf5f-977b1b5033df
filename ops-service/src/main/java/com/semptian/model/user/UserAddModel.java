package com.semptian.model.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户新增模型
 *
 * <AUTHOR>
 * @date 2020/2/26 17:40
 */
@ApiModel(value = "用户新增对象")
@Data
public class UserAddModel {

    @ApiModelProperty(value = "用户名", example = "gui")
    private String userName;

    @ApiModelProperty(value = "账号", example = "gui")
    private String account;

    @ApiModelProperty(value = "描述", example = "faf")
    private String userDesc;

    @ApiModelProperty(value = "用户角色", example = "1,2,3")
    private String userRole;

    @ApiModelProperty(value = "用户所属组织id")
    private String userOrg;

    @ApiModelProperty(value = "用户为审批角色可管理的组织,多个id直接用逗号分隔")
    private String manageOrgs;

    @ApiModelProperty(value = "用户手机号")
    private String telephone;

    @ApiModelProperty(value = "用户性别: 1 - 男 ; 0 - 女")
    private int gender;

    @ApiModelProperty(value = "用户默认图像地址")
    private String defaultImageUrl;

    @ApiModelProperty(value = "过期时间:格式yyyy-MM-dd;永久有效 - permanent;一个月 - one_month;两个月 - two_month")
    private String expiration;

    @ApiModelProperty(value = "用户指纹信息")
    private String finger;


}

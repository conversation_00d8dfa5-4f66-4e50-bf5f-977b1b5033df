package com.semptian.model.dto;

import com.semptian.enums.ModuleNameEnum;
import com.semptian.enums.TaskStatusEnum;
import com.semptian.enums.TimeModeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@AllArgsConstructor
@Builder
public class TaskQueryDTO {
    private ModuleNameEnum module;
    private TimeModeEnum timeMode;
    private TaskStatusEnum status;
    private String chartName;
    private String keyword;
    private String sort;
    boolean desc;
    private Integer page = 1;
    private Integer size = 20;
}
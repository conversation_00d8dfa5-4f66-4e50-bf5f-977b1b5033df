package com.semptian.model.organization;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/17
 * @Description
 **/
@Data
@ApiModel
public class OrgModel {

    @ApiModelProperty(value = "组织id,当有组织id的时候为编辑，无id时为添加")
    private String id;

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @ApiModelProperty(value = "父节点id")
    private String parentId;

    @ApiModelProperty(value = "组织类型 1-操作,2-审批,3-审计,4-其他")
    private Integer orgType;

    @ApiModelProperty(value = "类型 1-部门 2-用户组")
    private Integer type;

    @ApiModelProperty(value = "组织描述")
    private String orgDesc;

}

package com.semptian.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * API访问结果监控表（含统计量）
 */
@Data
@AllArgsConstructor
@Builder
@ToString
public class AdsOpsOpsApiAccessResult {
    /**
     * 指标ID
     */
    private String metricId;

    /**
     * 统计日期
     */
    private LocalDate statDate;

    /**
     * 操作账号
     */
    private String account;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 接口路径
     */
    private String apiPath;

    /**
     * 请求时间
     */
    private LocalDateTime requestTime;

    /**
     * 响应状态码
     */
    private Short responseStatus;

    /**
     * 耗时(ms)
     */
    private Integer costTime;

    /**
     * 响应数据
     */
    private String responseData;

    /**
     * 统计结果数量
     */
    private long statCount = 0L;

    /**
     * 是否成功
     */
    private Boolean isSuccess;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 是否比较kv结构
     */
    private boolean isKv;

    /**
     * kv的具体内容
     */
    private String kvContent;

    /**
     * 请求参数JSON
     */
    private String params;

    /**
     * 是否动态账号
     */
    private boolean isDynamic;

    /**
     * 冗余字段
     */
    private String redundantField;
}

package com.semptian.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.BusinessSampleConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
@DS("archive-doris")
public interface ArchiveSampleConfigMapper {
    @Select("${sql}")
    List<Map<String, Object>> executeDynamicSQL(String sql);
}
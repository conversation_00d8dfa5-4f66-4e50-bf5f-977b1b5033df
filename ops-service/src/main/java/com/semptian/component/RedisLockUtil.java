package com.semptian.component;

/**
 * <AUTHOR>
 * Date:2021/6/15
 * Description Redis 实现分布式锁
 **/

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RedisLockUtil {

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 是否开启事务
     */
    @Value("${redis.lock.enable}")
    private boolean enable;

    /**
     * 过期时间
     */
    private static final int EXPIRATION_TIME = 60000;

    /**
     * 缓存锁
     *
     * @param id 锁唯一标识
     * @return
     */
    private static String getLockRedisKey(String id) {
        return "lock:" + id;
    }

    /**
     * 加锁关键数据
     *
     * @param id
     * @return true:得到锁，false:未得到锁
     */
    public boolean tryLock(String id) {
        if (!enable) {
            return true;
        }
        long time = System.currentTimeMillis();
        String oldTime = stringRedisTemplate.opsForValue().getAndSet(getLockRedisKey(id), String.valueOf(time));
        if (StringUtils.isBlank(oldTime)) {
            return true;
        }
        if (time - Long.parseLong(oldTime) > EXPIRATION_TIME) {
            return true;
        }
        log.info("data is lock, id: {}", id);
        return false;
    }

    /**
     * 解锁关键数据
     *
     * @param id
     */
    public void unlock(String id) {
        if (!enable) {
            return;
        }
        stringRedisTemplate.delete(getLockRedisKey(id));
    }


    /**
     * 固定时间值的锁，用于过期时间自动解锁设置，可防止持续调用导致永久锁问题
     *
     * @param id
     * @return true:得到锁，false:未得到锁
     */
    public boolean tryLockByValidityTime(String id) {
        if (!enable) {
            return true;
        }
        long nowTime = System.currentTimeMillis();
        Boolean lock = stringRedisTemplate.opsForValue().setIfAbsent(getLockRedisKey(id), String.valueOf(nowTime));
        if (lock != null && lock) {
            return true;
        }
        String oldTime = stringRedisTemplate.opsForValue().get(getLockRedisKey(id));
        if (StringUtils.isNotBlank(oldTime) && nowTime - Long.parseLong(oldTime) > EXPIRATION_TIME) {
            stringRedisTemplate.delete(getLockRedisKey(id));
        }
        log.info("data is lock, id: {}", id);
        return false;
    }

    /**
     * 延期解锁
     *
     * @param seconds 秒
     * @param id      锁ID
     */
    public void setLockExpire(String id, int seconds) {
        if (!enable) {
            return;
        }
        stringRedisTemplate.expire(getLockRedisKey(id), seconds, TimeUnit.SECONDS);
    }
}

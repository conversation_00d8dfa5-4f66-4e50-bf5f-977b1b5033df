package com.semptian.component;

import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.client.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * HBase查询工具类
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Slf4j
@Component
public class HBaseQueryUtil {

    @Value("${hbase.zookeeper.quorum:172.16.80.11,172.16.80.12,172.16.80.13,172.16.80.14,172.16.80.10,172.16.80.16}")
    private String zookeeperQuorum;

    @Value("${hbase.zookeeper.port:2181}")
    private String zookeeperPort;

    @Value("${hbase.zookeeper.znode.parent:/hbase-unsecure}")
    private String znodeParent;

    @Value("${hbase.client.timeout:60000}")
    private String clientTimeout;

    @Value("${hbase.rpc.timeout:60000}")
    private String rpcTimeout;

    @Value("${hbase.client.retries.number:3}")
    private String retriesNumber;

    @Value("${hbase.batch.size:1000}")
    private int batchSize;

    private Connection connection;

    @PostConstruct
    public void init() {
        try {
            Configuration config = HBaseConfiguration.create();

            // 基础ZooKeeper配置
            config.set("hbase.zookeeper.quorum", zookeeperQuorum);
            config.set("hbase.zookeeper.property.clientPort", zookeeperPort);
            config.set("zookeeper.znode.parent", znodeParent);

            // 超时配置
            config.set("hbase.client.operation.timeout", clientTimeout);
            config.set("hbase.rpc.timeout", rpcTimeout);
            config.set("hbase.client.scanner.timeout.period", clientTimeout);

            // 重试配置
            config.set("hbase.client.retries.number", retriesNumber);
            config.set("hbase.client.pause", "1000");

            // 连接池配置
            config.set("hbase.client.max.total.tasks", "100");
            config.set("hbase.client.max.perserver.tasks", "10");

            // 其他优化配置
            config.set("hbase.regionserver.lease.period", "60000");
            config.set("hbase.client.scanner.caching", "1000");

            connection = ConnectionFactory.createConnection(config);
            log.info("[HBase] 连接初始化成功 | ZK集群: {} | 端口: {} | ZNode路径: {} | 超时: {}ms | 重试次数: {} | 批次大小: {}",
                    zookeeperQuorum, zookeeperPort, znodeParent, clientTimeout, retriesNumber, batchSize);

            // 测试连接
            testConnection();

        } catch (IOException e) {
            log.error("[HBase] 连接初始化失败", e);
            throw new RuntimeException("HBase连接初始化失败", e);
        }
    }

    @PreDestroy
    public void close() {
        if (connection != null) {
            try {
                connection.close();
                log.info("[HBase] 连接关闭成功");
            } catch (IOException e) {
                log.error("[HBase] 连接关闭失败", e);
            }
        }
    }

    /**
     * 批量查询HBase数据
     *
     * @param tableName 表名
     * @param rowKeys   行键列表
     * @return 查询结果
     */
    public List<Map<String, Object>> batchGet(String tableName, List<String> rowKeys) {
        return batchGet(tableName, rowKeys, "B");
    }

    /**
     * 批量查询HBase数据，指定列族（支持分批处理）
     *
     * @param tableName    表名
     * @param rowKeys      行键列表
     * @param columnFamily 列族名称
     * @return 查询结果
     */
    public List<Map<String, Object>> batchGet(String tableName, List<String> rowKeys, String columnFamily) {
        List<Map<String, Object>> results = new ArrayList<>();

        if (rowKeys == null || rowKeys.isEmpty()) {
            return results;
        }

        // 如果rowKeys数量超过批次大小，进行分批处理
        if (rowKeys.size() > batchSize) {
            log.info("[HBase] 开始分批查询 | 表: {} | 列族: {} | 总数: {} | 批次大小: {}",
                    tableName, columnFamily, rowKeys.size(), batchSize);

            int totalBatches = (int) Math.ceil((double) rowKeys.size() / batchSize);

            for (int i = 0; i < totalBatches; i++) {
                int startIndex = i * batchSize;
                int endIndex = Math.min(startIndex + batchSize, rowKeys.size());
                List<String> batchRowKeys = rowKeys.subList(startIndex, endIndex);

                log.debug("[HBase] 执行第 {}/{} 批查询 | 当前批次数量: {}",
                         i + 1, totalBatches, batchRowKeys.size());

                List<Map<String, Object>> batchResults = executeBatchGet(tableName, batchRowKeys, columnFamily);
                results.addAll(batchResults);
            }

            log.info("[HBase] 分批查询完成 | 表: {} | 列族: {} | 总请求数: {} | 总结果数: {} | 批次数: {}",
                    tableName, columnFamily, rowKeys.size(), results.size(), totalBatches);
        } else {
            // 单批次处理
            results = executeBatchGet(tableName, rowKeys, columnFamily);
        }

        return results;
    }

    /**
     * 执行单批次HBase查询
     *
     * @param tableName    表名
     * @param rowKeys      行键列表（单批次）
     * @param columnFamily 列族名称
     * @return 查询结果
     */
    private List<Map<String, Object>> executeBatchGet(String tableName, List<String> rowKeys, String columnFamily) {
        List<Map<String, Object>> results = new ArrayList<>();

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {
            // 创建批量Get请求
            List<Get> gets = new ArrayList<>();
            for (String rowKey : rowKeys) {
                Get get = new Get(rowKey.getBytes());
                // 指定列族
                get.addFamily(columnFamily.getBytes());
                gets.add(get);
            }

            // 执行批量查询
            Result[] resultArray = table.get(gets);

            for (Result result : resultArray) {
                if (!result.isEmpty()) {
                    Map<String, Object> rowData = new HashMap<>();

                    // 解析结果
                    Cell[] cells = result.rawCells();
                    for (Cell cell : cells) {
                        String family = new String(cell.getFamilyArray(), cell.getFamilyOffset(), cell.getFamilyLength());
                        String qualifier = new String(cell.getQualifierArray(), cell.getQualifierOffset(), cell.getQualifierLength());
                        String value = new String(cell.getValueArray(), cell.getValueOffset(), cell.getValueLength());

                        // 只保存指定列族的数据
                        if (columnFamily.equals(family)) {
                            rowData.put(qualifier, value);
                        }
                    }

                    // 添加行键
                    rowData.put("rowkey", new String(result.getRow()));
                    results.add(rowData);
                }
            }

            log.debug("[HBase] 单批次查询完成 | 表: {} | 列族: {} | 请求数: {} | 结果数: {}",
                     tableName, columnFamily, rowKeys.size(), results.size());

        } catch (IOException e) {
            log.error("[HBase] 单批次查询失败 | 表: {} | 列族: {} | 错误: {}", tableName, columnFamily, e.getMessage(), e);
            throw new RuntimeException("HBase单批次查询失败", e);
        }

        return results;
    }

    /**
     * 根据协议类型获取对应的HBase表名
     *
     * @param protocolType 协议类型
     * @param dateStr      日期字符串(YYYYMM格式)
     * @return HBase表名
     */
    public String getTableName(String protocolType, String dateStr) {
        // 根据协议类型映射到对应的表名前缀
        String tablePrefix;
        switch (protocolType.toLowerCase()) {
            case "email":
                tablePrefix = "dw:deye_v64_email_detail_";
                break;
            case "phone":
                tablePrefix = "dw:deye_v64_phone_detail_";
                break;
            case "radius":
                tablePrefix = "dw:deye_v64_radius_detail_";
                break;
            case "im":
                tablePrefix = "dw:deye_v64_im_detail_";
                break;
            default:
                tablePrefix = "dw:deye_v64_" + protocolType.toLowerCase() + "_detail_";
        }

        return tablePrefix + dateStr;
    }

    /**
     * 测试HBase连接
     */
    private void testConnection() {
        try {
            // 获取Admin对象来测试连接
            Admin admin = connection.getAdmin();

            // 尝试列出表来验证连接
            admin.listTableNames();
            admin.close();

            log.info("[HBase] 连接测试成功");
        } catch (Exception e) {
            log.warn("[HBase] 连接测试失败，但连接对象已创建: {}", e.getMessage());
            // 不抛出异常，因为有些环境可能权限受限但仍能正常查询
        }
    }

    /**
     * 检查表是否存在
     *
     * @param tableName 表名
     * @return 是否存在
     */
    public boolean tableExists(String tableName) {
        try (Admin admin = connection.getAdmin()) {
            boolean exists = admin.tableExists(TableName.valueOf(tableName));
            log.debug("[HBase] 表存在性检查 | 表: {} | 存在: {}", tableName, exists);
            return exists;
        } catch (IOException e) {
            log.error("[HBase] 检查表存在性失败 | 表: {} | 错误: {}", tableName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取当前配置的批次大小
     *
     * @return 批次大小
     */
    public int getBatchSize() {
        return batchSize;
    }
}

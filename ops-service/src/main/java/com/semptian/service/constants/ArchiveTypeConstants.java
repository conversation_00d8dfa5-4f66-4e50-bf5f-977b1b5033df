package com.semptian.service.constants;

import com.semptian.entity.BusinessAccountConfig.AccountType;
import java.util.HashMap;
import java.util.Map;

/**
 * 档案类型常量类
 * 定义了不同账号类型对应的档案类型值
 */
public class ArchiveTypeConstants {

    /**
     * 账号类型与档案类型的映射关系
     * EMAIL 对应 arcType: 2
     * PHONE 对应 arcType: 5
     * RADIUS 对应 arcType: 1
     * IM 对应 arcType: 6
     * VIRTUAL 对应 arcType: 0
     */
    private static final Map<AccountType, Integer> ACCOUNT_TYPE_TO_ARCHIVE_TYPE = new HashMap<>();

    static {
        ACCOUNT_TYPE_TO_ARCHIVE_TYPE.put(AccountType.EMAIL, 2);
        ACCOUNT_TYPE_TO_ARCHIVE_TYPE.put(AccountType.PHONE, 5);
        ACCOUNT_TYPE_TO_ARCHIVE_TYPE.put(AccountType.RADIUS, 1);
        ACCOUNT_TYPE_TO_ARCHIVE_TYPE.put(AccountType.IM, 6);
        ACCOUNT_TYPE_TO_ARCHIVE_TYPE.put(AccountType.VIRTUAL, 0);
    }

    /**
     * 根据账号类型获取对应的档案类型
     * 
     * @param accountType 账号类型
     * @return 档案类型值，如果账号类型为null或未找到对应关系，则返回0（全部类型）
     */
    public static int getArchiveTypeByAccountType(AccountType accountType) {
        if (accountType == null) {
            return 0;
        }
        return ACCOUNT_TYPE_TO_ARCHIVE_TYPE.getOrDefault(accountType, 0);
    }
}

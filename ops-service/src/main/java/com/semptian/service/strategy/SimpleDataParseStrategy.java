package com.semptian.service.strategy;

import com.alibaba.fastjson.JSONObject;
import com.semptian.model.vo.AdsOpsOpsApiAccessResult;
import lombok.extern.slf4j.Slf4j;

/**
 * 简单数据解析策略
 * 处理直接取值的情况（typeValue=0）
 */
@Slf4j
public class SimpleDataParseStrategy implements ResponseParseStrategy {
    
    @Override
    public void parseResponse(JSONObject apiResponseJSONObject, JSONObject responseMapping, String pathKey, AdsOpsOpsApiAccessResult resultData) {
        // 根据pathKey获取目标值
        Object targetValue = getValueByPath(apiResponseJSONObject, pathKey.split("\\."));
        
        // 如果没有值，设置默认值0
        if (targetValue == null) {
            setDefaultResult(resultData);
            return;
        }
        
        // 有值直接赋值
        resultData.setStatCount((Integer) targetValue);
        resultData.setKvContent("[]");
        //直接取值,没有所谓的kv
        resultData.setKv(false);
    }
    
    /**
     * 根据路径获取JSON对象中的值
     *
     * @param source JSON源对象
     * @param pathSegments 路径段数组
     * @return 获取到的值，如果路径无效则返回null
     */
    private Object getValueByPath(JSONObject source, String[] pathSegments) {
        Object currentValue = source;
        for (String segment : pathSegments) {
            if (!(currentValue instanceof JSONObject)) {
                log.warn("返回值JSON无法匹配结果解析格式");
                return null;
            }
            currentValue = ((JSONObject) currentValue).get(segment);
        }
        return currentValue;
    }
    
    /**
     * 设置默认结果
     *
     * @param resultData 结果数据对象
     */
    private void setDefaultResult(AdsOpsOpsApiAccessResult resultData) {
        log.warn("返回值JSON无法匹配结果解析格式,设置默认值为0");
        resultData.setStatCount(0);
        resultData.setKvContent("");
    }
}

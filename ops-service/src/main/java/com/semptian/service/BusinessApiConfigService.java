package com.semptian.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.entity.BusinessApiConfig;

import java.util.List;

public interface BusinessApiConfigService extends IService<BusinessApiConfig> {
    /**
     * Get the list of API configurations
     *
     * @param pageNum    Page number
     * @param pageSize   Number of records per page
     * @param systemName System name
     * @param apiPath    API path
     * @param authType   Authentication type
     * @return Page<BusinessApiConfig>
     */
    Page<BusinessApiConfig> getApiConfigList(int pageNum, int pageSize, String systemName, String apiPath, String authType);

    /**
     * Get the list of API configurations by metric ID
     *
     * @param metricId Metric ID
     * @return List<BusinessApiConfig>
     */
    List<BusinessApiConfig> getApiConfigByMetricId(Integer metricId);
}
package com.semptian.service;

import com.semptian.entity.SpamCallNumberInfo;

import java.util.List;
import java.util.Set;

/**
 * 骚扰号码信息服务接口
 */
public interface SpamCallNumberInfoService {
    
    /**
     * 获取所有活跃的骚扰号码
     *
     * @return 骚扰号码列表
     */
    List<SpamCallNumberInfo> getAllActiveSpamNumbers();
    
    /**
     * 获取所有活跃的骚扰号码集合
     * 
     * @return 骚扰号码集合
     */
    Set<String> getAllActiveSpamNumbersSet();
    
    /**
     * 检查号码是否为骚扰号码
     * 
     * @param phoneNumber 要检查的号码
     * @return 如果是骚扰号码返回true，否则返回false
     */
    boolean isSpamNumber(String phoneNumber);
}

package com.semptian.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.entity.OpsApiAccessResultDifference;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 系统间一致性统计结果差异
 * <AUTHOR>
 * @since 2025/5/20
 */
public interface OpsApiAccessResultDifferenceService extends IService<OpsApiAccessResultDifference> {

    void deleteByMetricIdAndTimeRange(Integer metricId, LocalDate startDate, LocalDate endDate, String account);

    List<OpsApiAccessResultDifference> selectDifferenceAccountsBySql(Integer metricId, Date startDate, Date endDate);
}

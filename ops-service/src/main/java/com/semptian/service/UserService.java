package com.semptian.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.entity.UserEntity;

/**
 * <AUTHOR>
 * @date 2019/3/12  8:44
 */
public interface UserService {

    /**
     * 查看用户的详细信息，包括用户所属角色具有哪些应用、菜单、功能的权限
     *
     * @param userId
     * @return
     */
    Object queryUserInfoById(Long userId, Integer isBasicInfo);

    /**
     * 根据账号查询用户信息
     *
     * @param account
     * @return
     */
    UserEntity selectByAccount(String account);
}

package com.semptian.service.impl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 协议导入结果
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProtocolImportResult {
    
    /**
     * 成功导入的记录数
     */
    private long successCount;
    
    /**
     * 失败的记录数
     */
    private long failedCount;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 获取总记录数
     */
    public long getTotalCount() {
        return successCount + failedCount;
    }
    
    /**
     * 是否全部成功
     */
    public boolean isAllSuccess() {
        return failedCount == 0 && successCount > 0;
    }
    
    /**
     * 是否全部失败
     */
    public boolean isAllFailed() {
        return successCount == 0 && failedCount > 0;
    }
    
    /**
     * 是否部分成功
     */
    public boolean isPartialSuccess() {
        return successCount > 0 && failedCount > 0;
    }
}

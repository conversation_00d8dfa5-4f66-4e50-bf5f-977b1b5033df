package com.semptian.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.annotation.OperateMock;
import com.semptian.entity.BusinessMetricConfig;
import com.semptian.mapper.BusinessMetricConfigMapper;
import com.semptian.service.BusinessMetricConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class BusinessMetricConfigServiceImpl extends ServiceImpl<BusinessMetricConfigMapper, BusinessMetricConfig> implements BusinessMetricConfigService {

    @Override
    public Page<BusinessMetricConfig> getMetricConfigList(int pageNum, int pageSize, String metricModelName, String metricName, String status) {
        Page<BusinessMetricConfig> page = new Page<>(pageNum, pageSize);
        QueryWrapper<BusinessMetricConfig> wrapper = new QueryWrapper<>();
        if (metricModelName != null) wrapper.like("metric_model_name", metricModelName);
        if (metricName != null) wrapper.like("metric_name", metricName);
        //这里的status是enum类型，需要使用eq
        if (status != null && status.equals(BusinessMetricConfig.Status.ACTIVE.toString()))
            wrapper.eq("status", BusinessMetricConfig.Status.ACTIVE);
        return baseMapper.selectPage(page, wrapper);
    }

    @OperateMock
    @Override
    public BusinessMetricConfig getByIdWithService(Integer metricId) {
        return this.getById(metricId);
    }

    @Override
    public List<Integer> getAllMetricIds() {
        // 查询所有启用状态的指标配置
        QueryWrapper<BusinessMetricConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("status", BusinessMetricConfig.Status.ACTIVE);
        wrapper.select("id"); // 只查询ID字段，提高性能

        List<BusinessMetricConfig> configs = baseMapper.selectList(wrapper);

        // 提取ID并转换为Integer类型
        return configs.stream()
                .map(config -> config.getId().intValue())
                .collect(Collectors.toList());
    }
}
package com.semptian.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.semptian.aspect.StaticMethodRecorderProxy;
import com.semptian.base.util.HttpUtil;
import com.semptian.base.util.JsonUtil;
import com.semptian.common.SystemEnum;
import com.semptian.component.DorisStreamLoadUtil;
import com.semptian.entity.*;
import com.semptian.model.vo.AdsOpsOpsApiAccessResult;
import com.semptian.service.*;
import com.semptian.service.constants.ArchiveTypeConstants;
import com.semptian.service.strategy.ArrayDataParseStrategy;
import com.semptian.service.strategy.ResponseParseStrategy;
import com.semptian.service.strategy.ResponseParseStrategyFactory;
import com.semptian.util.ThreadPoolUtil;
import com.semptian.util.SnowflakeIdGenerator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InterSystemDataProducerServiceImpl implements InterSystemDataProducerService {

    // API调用最大重试次数
    private static final int MAX_RETRY_ATTEMPTS = 3;

    // 新增token缓存（有效期30分钟）
    @Resource
    private RedisTemplate<String, TokenCacheEntry> redisTemplate;
    @Resource
    public BusinessMetricConfigService businessMetricConfigService;
    @Resource
    public BusinessSysConfigService businessSysConfigService;
    @Resource
    public BusinessApiConfigService apiConfigService;
    @Resource
    public BusinessAccountConfigService businessAccountConfigService;
    @Resource
    public BusinessSampleConfigService businessSampleConfigService;
    @Resource
    private ElasticsearchService elasticsearchService;
    @Resource
    private SpamCallNumberInfoService spamCallNumberInfoService;
    @Resource
    private SnowflakeIdGenerator snowflakeIdGenerator;
    //最大的样本执行数
    @Value("${inter_system.max_sample_size}")
    private int maxSampleSize;
    @Value("${inter_system.search_thread_num}")
    private int searchThreadNum;
    @Value("${inter_system.archive_thread_num}")
    private int archiveThreadNum;
    @Value("${inter_system.case_thread_num}")
    private int caseThreadNum;
    //获取档案ID的url
    @Value("${inter_system.archive_id_url}")
    private String archiveIdUrl = "http://192.168.80.158:8109/archives_web/arc_info/arc_info.json";
    @Value("${doris.table}")
    private String DORIS_TABLE = "ops_api_access_result";
    @Resource
    private DorisStreamLoadUtil dorisStreamLoad;

    @Value("${inter_system.is_use_mock:false}")
    private boolean isUseMock;

    @Value("${inter_system.request_delay:0}")
    private int requestDelay;

    @Value("${inter_system.account_process_timeout:300}")
    private int accountProcessTimeout;

    // 全局存储 ARCHIVE 系统结果的 Map，用于跨系统数据比对
    // key: 指标ID (metricId), value: 该指标下 ARCHIVE 系统的所有结果
    private static final ConcurrentHashMap<Long, Vector<AdsOpsOpsApiAccessResult>> ARCHIVE_RESULTS_MAP = new ConcurrentHashMap<>();



    /**
     * 获取档案ID的参数映射
     *
     * @param accountConfig 账号配置，包含账号类型和账号值
     * @param timeRange 时间范围
     * @return 参数映射
     */
    private static Map<String, Object> getArchiveIdParamMap(BusinessAccountConfig accountConfig, String[] timeRange) {
        Map<String, Object> getArchiveIdParams = new HashMap<>();
        getArchiveIdParams.put("lang", "zh-CN");
        getArchiveIdParams.put("size", 15);
        getArchiveIdParams.put("onPage", 1);
        getArchiveIdParams.put("sortField", "activeRate");
        getArchiveIdParams.put("sortType", 1);
        getArchiveIdParams.put("keyWord", accountConfig.getAccountValue());

        // 根据账号类型确定档案类型
        int arcType = 0; // 默认为0（全部类型）
        if (accountConfig.getAccountType() != null) {
            // 使用ArchiveTypeConstants获取档案类型
            arcType = ArchiveTypeConstants.getArchiveTypeByAccountType(accountConfig.getAccountType());
        }

        getArchiveIdParams.put("arcType", arcType);

        getArchiveIdParams.put("fileFlag", 99);
        getArchiveIdParams.put("hasFax", 99);
        getArchiveIdParams.put("latestRelationCountry", "all");
        getArchiveIdParams.put("latestRelationCity", "");
        getArchiveIdParams.put("isRadius", 99);
        getArchiveIdParams.put("appType", "");
        getArchiveIdParams.put("accountType", 99);
        getArchiveIdParams.put("dateOption", 5);
        getArchiveIdParams.put("isCare", 0);
        return getArchiveIdParams;
    }

    /**
     * 收集跨系统指标数据
     *
     * @param metricId         指标ID
     * @param collectTimeRange 采集时间范围，格式为"startDate,endDate"
     * @return 采集结果列表
     */
    @Override
    public List<AdsOpsOpsApiAccessResult> collectMetrics(Integer metricId, String collectTimeRange) {
        // 获取指标名称和初始化结果集
        BusinessMetricConfig metricConfig = businessMetricConfigService.getByIdWithService(metricId);
        String metricName = metricConfig != null ? metricConfig.getMetricName() : "未知指标";

        // 使用线程安全的Vector作为结果集合
        List<AdsOpsOpsApiAccessResult> results = new Vector<>();

        // 初始化骚扰号码过滤服务（使用双重检查锁，避免重复初始化）
        ArrayDataParseStrategy.initializeSpamCallNumberInfoService(spamCallNumberInfoService);

        // 记录开始日志
        logCollectionStart(metricId, metricName, collectTimeRange);

        try {
            // 1. 准备数据上下文
            MetricsDataContext dataContext = prepareMetricsData(metricId, collectTimeRange);

            // 2. 初始化并执行数据收集流程
            executeDataCollection(dataContext, collectTimeRange, results);

        } catch (InterruptedException e) {
            log.error("等待系统执行完成时发生异常: {}", e.getMessage(), e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("收集指标数据时发生异常: {}", e.getMessage(), e);
        }

        log.info("========== 指标数据收集完成 ==========\n");
        return results;
    }

    /**
     * 记录数据收集开始的日志信息
     *
     * @param metricId         指标ID
     * @param metricName       指标名称
     * @param collectTimeRange 采集时间范围
     */
    private void logCollectionStart(Integer metricId, String metricName, String collectTimeRange) {
        log.info("========== 开始收集指标数据 ==========");
        log.info("指标ID: {}, 指标名称: {}, 时间范围: {}, 请求延迟: {}ms, 超时时间: {}秒, 线程池配置: CASE={}, SEARCH={}, ARCHIVE={}",
                metricId, metricName, collectTimeRange, requestDelay, accountProcessTimeout,
                caseThreadNum, searchThreadNum, archiveThreadNum);
    }

    /**
     * 执行数据收集的主流程
     *
     * @param dataContext      数据上下文
     * @param collectTimeRange 采集时间范围
     * @param results          结果集合
     * @throws InterruptedException 如果线程被中断
     */
    private void executeDataCollection(MetricsDataContext dataContext, String collectTimeRange,
                                      List<AdsOpsOpsApiAccessResult> results) throws InterruptedException {
        // 1. 分类系统配置
        SystemConfigGroups systemGroups = categorizeSystemConfigs(dataContext.getBusinessSysConfigs());

        // 2. 初始化线程池和同步器
        SystemCountDownLatchs systemCountDownLatchs = initializeThreadPoolsAndCountDownLatchs(systemGroups);

        // 3. 按照指定顺序执行系统数据收集
        executeSystemsInOrder(systemGroups, dataContext, collectTimeRange, results, systemCountDownLatchs);
    }

    /**
     * 初始化线程池和同步器
     *
     * @param systemGroups 系统配置分组
     * @return 包含各系统同步器的对象
     */
    private SystemCountDownLatchs initializeThreadPoolsAndCountDownLatchs(SystemConfigGroups systemGroups) {
        // 初始化线程池
        ThreadPoolUtil.getSystemThreadPool("SEARCH", searchThreadNum);
        ThreadPoolUtil.getSystemThreadPool("ARCHIVE", archiveThreadNum);
        ThreadPoolUtil.getSystemThreadPool("CASE", caseThreadNum);

        // 创建CountDownLatch来跟踪系统执行状态
        CountDownLatch archiveCountDownLatch = new CountDownLatch(systemGroups.getArchiveSysConfigs().size());
        CountDownLatch caseCountDownLatch = new CountDownLatch(systemGroups.getCaseSysConfigs().size());
        CountDownLatch searchCountDownLatch = new CountDownLatch(systemGroups.getSearchSysConfigs().size());

        return new SystemCountDownLatchs(archiveCountDownLatch, caseCountDownLatch, searchCountDownLatch);
    }

    /**
     * 按照指定顺序执行系统数据收集
     *
     * @param systemGroups     系统配置分组
     * @param dataContext      数据上下文
     * @param collectTimeRange 采集时间范围
     * @param results          结果集合
     * @param systemCountDownLatchs    系统同步器
     * @throws InterruptedException 如果线程被中断
     */
    private void executeSystemsInOrder(SystemConfigGroups systemGroups, MetricsDataContext dataContext,
                                      String collectTimeRange, List<AdsOpsOpsApiAccessResult> results,
                                      SystemCountDownLatchs systemCountDownLatchs)
                                      throws InterruptedException {
        // 1. 先执行ARCHIVE和CASE系统
        executeArchiveSystems(systemGroups.getArchiveSysConfigs(), dataContext, collectTimeRange,
                             results, systemCountDownLatchs.getArchiveCountDownLatch());
        executeCaseSystems(systemGroups.getCaseSysConfigs(), dataContext, collectTimeRange,
                          results, systemCountDownLatchs.getCaseCountDownLatch());

        // 2. 等待ARCHIVE系统执行完成后再执行SEARCH系统
        waitForArchiveAndExecuteSearch(systemGroups.getArchiveSysConfigs(), systemGroups.getSearchSysConfigs(),
                                      dataContext, collectTimeRange, results,
                                      systemCountDownLatchs.getArchiveCountDownLatch(),
                                      systemCountDownLatchs.getSearchCountDownLatch());

        // 3. 等待所有系统执行完成
        waitForAllSystems(systemCountDownLatchs.getCaseCountDownLatch(), systemCountDownLatchs.getSearchCountDownLatch());
    }

    /**
     * 系统同步器，用于跟踪各系统执行状态
     */
    @Data
    @AllArgsConstructor
    private static class SystemCountDownLatchs {
        private CountDownLatch archiveCountDownLatch;
        private CountDownLatch caseCountDownLatch;
        private CountDownLatch searchCountDownLatch;
    }

    /**
     * 准备指标数据上下文
     *
     * @param metricId         指标ID
     * @param collectTimeRange 采集时间范围
     * @return 指标数据上下文
     */
    private MetricsDataContext prepareMetricsData(Integer metricId, String collectTimeRange) {
        // 根据id查询指标配置
        BusinessMetricConfig businessMetricConfig = businessMetricConfigService.getByIdWithService(metricId);

        // 根据指标配置查询对比系统配置
        String[] compareSystems = businessMetricConfig.getCompareSystems().split(",");

        // 根据sysName和metricId查询系统配置
        List<BusinessSysConfig> businessSysConfigs = businessSysConfigService.getSysConfigBySysNameAndMetricId(compareSystems, metricId);

        // 根据metricId获取需要采样的账号，最大不超过maxSampleSize值
        List<BusinessAccountConfig> businessAccountConfigs = businessAccountConfigService.getAccountConfigByMetricId(businessMetricConfig, maxSampleSize, collectTimeRange);

        // 仅获取采样方式为静态账号的账号
        List<BusinessAccountConfig> businessAccountConfigWithStaticAccount = businessAccountConfigService.getAccountConfigByMetricIdWithStaticAccount(businessMetricConfig, maxSampleSize, collectTimeRange);

        // 根据metricId获取API配置
        List<BusinessApiConfig> businessApiConfigs = apiConfigService.getApiConfigByMetricId(metricId);

        return new MetricsDataContext(businessMetricConfig, businessSysConfigs, businessAccountConfigs, businessAccountConfigWithStaticAccount, businessApiConfigs);
    }

    /**
     * 将系统配置按类型分类
     *
     * @param businessSysConfigs 系统配置列表
     * @return 分类后的系统配置组
     */
    private SystemConfigGroups categorizeSystemConfigs(List<BusinessSysConfig> businessSysConfigs) {
        List<BusinessSysConfig> archiveSysConfigs = new ArrayList<>();
        List<BusinessSysConfig> caseSysConfigs = new ArrayList<>();
        List<BusinessSysConfig> searchSysConfigs = new ArrayList<>();

        for (BusinessSysConfig config : businessSysConfigs) {
            switch (config.getSysType()) {
                case ARCHIVE:
                    archiveSysConfigs.add(config);
                    break;
                case CASE:
                    caseSysConfigs.add(config);
                    break;
                case SEARCH:
                    searchSysConfigs.add(config);
                    break;
            }
        }

        return new SystemConfigGroups(archiveSysConfigs, caseSysConfigs, searchSysConfigs);
    }

    /**
     * 执行ARCHIVE系统的数据采集
     *
     * @param archiveSysConfigs     ARCHIVE系统配置列表
     * @param dataContext           数据上下文
     * @param collectTimeRange      采集时间范围
     * @param results               结果列表
     * @param archiveCountDownLatch ARCHIVE系统计数器
     */
    private void executeArchiveSystems(List<BusinessSysConfig> archiveSysConfigs, MetricsDataContext dataContext, String collectTimeRange, List<AdsOpsOpsApiAccessResult> results,  CountDownLatch archiveCountDownLatch) throws InterruptedException {
        // 创建静态账号和动态账号各自的计数器
        CountDownLatch staticAccountCountCountDownLatch = new CountDownLatch(dataContext.getBusinessAccountConfigWithStaticAccount().size());
        CountDownLatch dynamicAccountCountCountDownLatch = new CountDownLatch(dataContext.getBusinessAccountConfigs().size());


        archiveSysConfigs.forEach(businessSysConfig -> {
            //TODO 传一个系统名字进去,构建线程池
            ThreadPoolUtil.getCommonExecutorInstance(businessSysConfig.getSysType().getCode()).execute(() -> {
                try {
                    log.info("[ARCHIVE] 系统开始采集 | 线程数: {} | 指标ID: {} | 指标名称: {}", archiveThreadNum, dataContext.getBusinessMetricConfig().getId(), dataContext.getBusinessMetricConfig().getMetricName());

                    // 先处理静态账号
                    List<AdsOpsOpsApiAccessResult> staticResults = handleInterSystemDataAnalysis(businessSysConfig, dataContext.getBusinessAccountConfigWithStaticAccount(), dataContext.getBusinessApiConfigs(), collectTimeRange, staticAccountCountCountDownLatch);
                    results.addAll(staticResults);

                    // 再处理动态账号
                    List<AdsOpsOpsApiAccessResult> dynamicResults = handleInterSystemDataAnalysis(businessSysConfig, dataContext.getBusinessAccountConfigs(), dataContext.getBusinessApiConfigs(), collectTimeRange, dynamicAccountCountCountDownLatch);
                    results.addAll(dynamicResults);

                    log.info("[ARCHIVE] 系统采集完成 | 指标ID: {} | 指标名称: {} | 采集数据: {} 条 | 静态账号: {} 条 | 动态账号: {} 条", dataContext.getBusinessMetricConfig().getId(), dataContext.getBusinessMetricConfig().getMetricName(), staticResults.size() + dynamicResults.size(), staticResults.size(), dynamicResults.size());
                } catch (Exception e) {
                    log.error("Archive系统执行异常: {}", e.getMessage(), e);
                } finally {
                    // 无论成功还是失败，都减少系统计数器
                    archiveCountDownLatch.countDown();
                    log.debug("Archive系统执行完成，计数器减1，当前值: {}", archiveCountDownLatch.getCount());
                }
            });
        });

        // 等待所有Archive系统处理完成
        log.info("[ARCHIVE] 等待所有Archive系统处理完成...");
        archiveCountDownLatch.await();
        log.info("[ARCHIVE] 所有Archive系统处理完成");

        // 将 ARCHIVE 系统的结果保存到全局变量中，用于后续 SEARCH 系统的数据比对
        // 每个指标有各自独立的 Vector，用于保存结果,不会有并发问题
        Long metricId = dataContext.getBusinessMetricConfig().getId();
        Vector<AdsOpsOpsApiAccessResult> archiveResults = new Vector<>(results);
        ARCHIVE_RESULTS_MAP.put(metricId, archiveResults);
        log.info("[ARCHIVE] 将 {} 条结果保存到全局变量，用于后续 SEARCH 系统的数据比对", archiveResults.size());
    }

    /**
     * 执行CASE系统的数据采集
     *
     * @param caseSysConfigs     CASE系统配置列表
     * @param dataContext        数据上下文
     * @param collectTimeRange   采集时间范围
     * @param results            结果列表

     * @param caseCountDownLatch CASE系统计数器
     */
    private void executeCaseSystems(List<BusinessSysConfig> caseSysConfigs, MetricsDataContext dataContext, String collectTimeRange, List<AdsOpsOpsApiAccessResult> results,  CountDownLatch caseCountDownLatch) {
        // 创建静态账号和动态账号各自的计数器
        CountDownLatch staticAccountCountCountDownLatch = new CountDownLatch(dataContext.getBusinessAccountConfigWithStaticAccount().size());

        // 创建一个计数器来跟踪系统处理完成
        //CountDownLatch caseSystemCompleteLatch = new CountDownLatch(caseSysConfigs.size());

        caseSysConfigs.forEach(businessSysConfig -> {
            try {
                ThreadPoolUtil.getCommonExecutorInstance(businessSysConfig.getSysType().getCode()).execute(() -> {
                    try {
                        log.info("[CASE] 系统开始采集 | 线程数: {} | 指标ID: {} | 指标名称: {}", caseThreadNum, dataContext.getBusinessMetricConfig().getId(), dataContext.getBusinessMetricConfig().getMetricName());

                        // 处理静态账号 - 传入null作为计数器，因为我们在方法内部等待处理完成
                        List<AdsOpsOpsApiAccessResult> staticResults = handleInterSystemDataAnalysis(businessSysConfig, dataContext.getBusinessAccountConfigWithStaticAccount(), dataContext.getBusinessApiConfigs(), collectTimeRange, staticAccountCountCountDownLatch);
                        results.addAll(staticResults);

                        log.info("[CASE] 系统采集完成 | 指标ID: {} | 指标名称: {} | 采集数据: {} 条", dataContext.getBusinessMetricConfig().getId(), dataContext.getBusinessMetricConfig().getMetricName(), staticResults.size());
                    } catch (Exception e) {
                        log.error("CASE系统执行异常: {}", e.getMessage(), e);
                    } finally {
                        // 无论成功还是失败，都减少系统计数器
                        caseCountDownLatch.countDown();
                        log.debug("CASE系统执行完成，计数器减1，当前值: {}", caseCountDownLatch.getCount());
                    }
                });
            } catch (Exception e) {
                log.error("启动CASE系统线程异常: {}", e.getMessage(), e);
                // 即使启动失败也减少计数器
                caseCountDownLatch.countDown();
            }
        });

        // 等待所有CASE系统处理完成
        if (!caseSysConfigs.isEmpty()) {
            try {
                log.info("[CASE] 等待所有CASE系统处理完成...");
                caseCountDownLatch.await();
                log.info("[CASE] 所有CASE系统处理完成");
            } catch (InterruptedException e) {
                log.error("[CASE] 等待CASE系统处理完成时被中断: {}", e.getMessage());
                Thread.currentThread().interrupt();
            }
        }

    }

    /**
     * 等待ARCHIVE系统执行完成后执行SEARCH系统
     *
     * @param archiveSysConfigs     ARCHIVE系统配置列表
     * @param searchSysConfigs      SEARCH系统配置列表
     * @param dataContext           数据上下文
     * @param collectTimeRange      采集时间范围
     * @param results               结果列表
     * @param archiveCountDownLatch ARCHIVE系统计数器
     * @param searchCountDownLatch  SEARCH系统计数器
     * @throws InterruptedException 等待过程中可能发生的中断异常
     */
    private void waitForArchiveAndExecuteSearch(List<BusinessSysConfig> archiveSysConfigs,
                                                List<BusinessSysConfig> searchSysConfigs, MetricsDataContext dataContext,
                                                String collectTimeRange, List<AdsOpsOpsApiAccessResult> results,
                                                CountDownLatch archiveCountDownLatch, CountDownLatch searchCountDownLatch) throws InterruptedException {

        CountDownLatch staticAccountCountCountDownLatch = new CountDownLatch(dataContext.getBusinessAccountConfigWithStaticAccount().size());
        CountDownLatch dynamicAccountCountCountDownLatch = new CountDownLatch(dataContext.getBusinessAccountConfigs().size());
        if (!archiveSysConfigs.isEmpty()) {
            // 等待ARCHIVE系统执行完成,才执行SEARCH系统
            log.info("[系统依赖] 等待 ARCHIVE 系统执行完成...");
            archiveCountDownLatch.await();
            log.info("[系统依赖] ARCHIVE 系统执行完成，开始执行 SEARCH 系统");
        }

        // 创建一个计数器来跟踪系统处理完成
        //CountDownLatch searchSystemCompleteLatch = new CountDownLatch(searchSysConfigs.size());

        // 启动SEARCH系统的执行
        searchSysConfigs.forEach(businessSysConfig -> {
            try {
                ThreadPoolUtil.getCommonExecutorInstance(businessSysConfig.getSysType().getCode()).execute(() -> {
                    try {
                        log.info("[SEARCH] 系统开始采集 | 线程数: {} | 指标ID: {} | 指标名称: {}", searchThreadNum, dataContext.getBusinessMetricConfig().getId(), dataContext.getBusinessMetricConfig().getMetricName());

                        // 先处理静态账号
                        List<AdsOpsOpsApiAccessResult> staticResults = handleInterSystemDataAnalysis(
                                businessSysConfig,
                                dataContext.getBusinessAccountConfigWithStaticAccount(),
                                dataContext.getBusinessApiConfigs(),
                                collectTimeRange,
                                staticAccountCountCountDownLatch);
                        results.addAll(staticResults);

                        // 再处理动态账号
                        List<AdsOpsOpsApiAccessResult> dynamicResults = handleInterSystemDataAnalysis(
                                businessSysConfig,
                                dataContext.getBusinessAccountConfigs(),
                                dataContext.getBusinessApiConfigs(),
                                collectTimeRange,
                                dynamicAccountCountCountDownLatch);
                        results.addAll(dynamicResults);

                        log.info("[SEARCH] 系统采集完成 | 指标ID: {} | 指标名称: {} | 采集数据: {} 条 | 静态账号: {} 条 | 动态账号: {} 条", dataContext.getBusinessMetricConfig().getId(), dataContext.getBusinessMetricConfig().getMetricName(), staticResults.size() + dynamicResults.size(), staticResults.size(), dynamicResults.size());
                    } catch (Exception e) {
                        log.error("SEARCH系统执行异常: {}", e.getMessage(), e);
                    } finally {
                        // 无论成功还是失败，都减少系统计数器
                        searchCountDownLatch.countDown();
                        log.debug("SEARCH系统执行完成，计数器减1，当前值: {}", searchCountDownLatch.getCount());
                    }
                });
            } catch (Exception e) {
                log.error("启动SEARCH系统线程异常: {}", e.getMessage(), e);
                // 即使启动失败也减少计数器
                searchCountDownLatch.countDown();
            }
        });

        // 等待所有SEARCH系统处理完成
        if (!searchSysConfigs.isEmpty()) {
            log.info("[SEARCH] 等待所有SEARCH系统处理完成...");
            searchCountDownLatch.await();
            log.info("[SEARCH] 所有SEARCH系统处理完成");
        }

        // 清理全局变量中的数据，避免内存泄漏
        Long metricId = dataContext.getBusinessMetricConfig().getId();
        if (ARCHIVE_RESULTS_MAP.containsKey(metricId)) {
            ARCHIVE_RESULTS_MAP.remove(metricId);
            log.info("[跨系统数据比对] 清理指标 {} 的 ARCHIVE 系统结果", metricId);
        }
    }

    /**
     * 等待所有系统执行完成
     *
     * @param caseCountDownLatch   CASE系统计数器
     * @param searchCountDownLatch SEARCH系统计数器
     * @throws InterruptedException 等待过程中可能发生的中断异常
     */
    private void waitForAllSystems(CountDownLatch caseCountDownLatch, CountDownLatch searchCountDownLatch) throws InterruptedException {
        // 等待CASE和SEARCH系统都执行完成
        log.debug("[系统依赖] 等待 CASE 和 SEARCH 系统执行完成...");
        caseCountDownLatch.await();
        searchCountDownLatch.await();
        log.info("[系统依赖] CASE 和 SEARCH 系统执行完成");
    }

    /**
     * 指标数据上下文类，用于存储指标相关的数据
     */
    @Data
    @AllArgsConstructor
    private static class MetricsDataContext {
        //1.指标对象,定义采集哪个指标
        private BusinessMetricConfig businessMetricConfig;
        //2.系统配置配置list,一个指标可能要去调用搜索,全息,案件的接口,配置了baseUrl和访问账号
        private List<BusinessSysConfig> businessSysConfigs;
        //3.动态账号list
        private List<BusinessAccountConfig> businessAccountConfigs;
        //4.静态账号list
        private List<BusinessAccountConfig> businessAccountConfigWithStaticAccount;
        //5.某指标其应该调用的搜索,全息,案件接口的api路径,请求参数,结果解析方式配置
        private List<BusinessApiConfig> businessApiConfigs;
    }

    /**
     * 系统配置分组类，按系统类型分组
     */
    @Data
    @AllArgsConstructor
    private static class SystemConfigGroups {
        private List<BusinessSysConfig> archiveSysConfigs;
        private List<BusinessSysConfig> caseSysConfigs;
        private List<BusinessSysConfig> searchSysConfigs;
    }

    @Override
    public void produceSampleAccount(Integer maxSampleCount) {
        // 使用昨天的日期调用updateSampleAccountByDate方法
        updateSampleAccountByDate(maxSampleCount, LocalDate.now().minusDays(1).toString());
    }

    @Override
    public boolean updateSampleAccountByDate(Integer maxSampleCount, String sampleDate) {
        final AtomicBoolean success = new AtomicBoolean(true);

        ThreadPoolUtil.getConfigExecutorInstance(1).execute(() -> {
            try {
                // 1. 获取当前指标ID（示例需要根据业务调整）,这里遍历所有指标，获取所有的指标ID依次执行
                List<BusinessMetricConfig> metricConfigs = businessMetricConfigService.list();
                for (BusinessMetricConfig metricConfig : metricConfigs) {
                    Long metricId = metricConfig.getId();
                    // 2. 获取有效采样配置
                    List<BusinessSampleConfig> configs = businessSampleConfigService.getValidConfigsByMetricId(metricId);
                    if (configs.isEmpty()) {
                        log.debug("指标ID: {}, 名称: {} 没有有效的采样配置", metricId, metricConfig.getMetricName());
                        continue;
                    }

                    log.info("开始处理指标ID: {}, 名称: {}, 采样日期: {}", metricId, metricConfig.getMetricName(), sampleDate);

                    // 3. 权重分配计算
                    int totalWeight = configs.stream().mapToInt(BusinessSampleConfig::getSampleWeight).sum();
                    for (BusinessSampleConfig config : configs) {
                        List<String> allAccounts = new ArrayList<>();
                        // 计算当前配置应采样的数量
                        int sampleCount = (int) Math.ceil(config.getSampleWeight() * maxSampleCount / (double) totalWeight);
                        // 4. 执行动态SQL
                        if (StringUtils.isNotBlank(config.getDynamicSql())) {
                            // 替换SQL中的日期表达式
                            String processedSql = processDynamicSqlWithDate(config.getDynamicSql(), sampleDate);
                            log.info("指标ID: {}, 配置ID: {}, 原始SQL: \n{}\n", metricId, config.getId(), config.getDynamicSql());
                            log.info("指标ID: {}, 配置ID: {}, 处理后SQL: \n{}\n", metricId, config.getId(), processedSql);

                            List<String> accounts = businessSampleConfigService.executeDynamicSampling(processedSql);
                            if (accounts != null && !accounts.isEmpty()) {
                                // 随机采样指定数量（保留原始顺序）
                                int actualCount = Math.min(sampleCount, accounts.size());
                                allAccounts.addAll(accounts.subList(0, actualCount));
                                log.info("指标ID: {}, 配置ID: {}, 采样SQL执行成功, 获取账号数: {}, 实际采样数: {}",
                                        metricId, config.getId(), accounts.size(), actualCount);
                            } else {
                                log.warn("指标ID: {}, 配置ID: {}, 采样SQL执行失败或返回空结果", metricId, config.getId());
                            }
                        }

                        // 5. 更新最后采样时间
                        config.setLastSampleTime(new Date());

                        // 6. 删除该配置之前的动态采样账号，只删除采样日期时间范围内的
                        // 计算时间范围
                        LocalDate deleteRangeDate = LocalDate.parse(sampleDate);
                        // 当日开始时间戳
                        long deleteRangeStart = deleteRangeDate.atStartOfDay().toEpochSecond(ZoneOffset.systemDefault().getRules().getOffset(deleteRangeDate.atStartOfDay())) * 1000;
                        // 当日结束时间戳（下一天开始前一毫秒）
                        long deleteRangeEnd = deleteRangeStart + 24 * 60 * 60 * 1000L - 1;
                        // 加上一天，与创建时间保持一致
                        deleteRangeStart += 24 * 60 * 60 * 1000L;
                        deleteRangeEnd += 24 * 60 * 60 * 1000L;

                        log.info("指标ID: {}, 配置ID: {}, 删除时间范围内的动态采样账号, 时间范围: {} - {}",
                                metricId, config.getId(), new Date(deleteRangeStart), new Date(deleteRangeEnd));

                        businessAccountConfigService.lambdaUpdate()
                                .eq(BusinessAccountConfig::getMetricId, metricId)
                                .eq(BusinessAccountConfig::getSampleStrategy, BusinessAccountConfig.SampleStrategy.DYNAMIC)
                                .eq(BusinessAccountConfig::getDynamic_condition_id, config.getId().toString())
                                .between(BusinessAccountConfig::getCreateTime, deleteRangeStart, deleteRangeEnd)
                                .remove();

                        // 7. 将采样账号批量写入tb_ops_business_account_config
                        if (!allAccounts.isEmpty()) {
                            // 使用雪花算法生成唯一ID
                            LocalDate currentDate = LocalDate.parse(sampleDate);

                            // 当日开始时间戳，用于生成随机时间
                            long dayStartTimestamp = currentDate.atStartOfDay().toEpochSecond(ZoneOffset.systemDefault().getRules().getOffset(currentDate.atStartOfDay())) * 1000;

                            log.debug("使用雪花算法生成ID，指标ID: {}, 配置ID: {}, 账号数量: {}",
                                    metricId, config.getId(), allAccounts.size());

                            List<BusinessAccountConfig> accountConfigs = allAccounts.stream().map(account -> {
                                BusinessAccountConfig accountConfig = BusinessAccountConfig.builder().build();

                                // 使用雪花算法生成20位唯一ID
                                long snowflakeId = snowflakeIdGenerator.nextId();
                                accountConfig.setId(snowflakeId);

                                accountConfig.setAccountValue(account);
                                accountConfig.setAccountType(config.getAccountType());
                                accountConfig.setMetricId(metricId);
                                accountConfig.setSampleWeight(config.getSampleWeight());
                                accountConfig.setSampleStrategy(BusinessAccountConfig.SampleStrategy.DYNAMIC);
                                accountConfig.setEnable(config.getEnable());
                                accountConfig.setDynamic_condition_id(config.getId().toString());

                                // 生成当日的随机时间戳，并主动加一天
                                long randomSeconds = (long) (Math.random() * 86400); // 随机秒数，最多一天
                                long oneDayMillis = 24 * 60 * 60 * 1000L; // 一天的毫秒数
                                // 当日时间戳 + 随机秒数 + 一天
                                long createTimestamp = dayStartTimestamp + randomSeconds * 1000 + oneDayMillis; // 毫秒时间戳
                                accountConfig.setCreateTime(createTimestamp);
                                accountConfig.setModifyTime(createTimestamp);
                                return accountConfig;
                            }).collect(Collectors.toList());
                            businessAccountConfigService.saveBatch(accountConfigs);
                            log.info("指标ID: {}, 配置ID: {}, 成功保存 {} 个采样账号", metricId, config.getId(), accountConfigs.size());
                        } else {
                            log.warn("指标ID: {}, 配置ID: {}, 没有获取到有效账号，跳过保存", metricId, config.getId());
                        }

                        // 8. 更新采样配置
                        businessSampleConfigService.updateById(config);
                    }
                }
            } catch (Exception e) {
                log.error("根据日期更新采样账号时发生异常: {}", e.getMessage(), e);
                success.set(false);
            }
        });

        return success.get();
    }

    /**
     * 处理动态SQL中的日期表达式，将CURDATE()等MySQL日期函数替换为实际日期
     *
     * @param sql        原始SQL
     * @param sampleDate 采样日期，格式为yyyy-MM-dd
     * @return 处理后的SQL
     */
    private String processDynamicSqlWithDate(String sql, String sampleDate) {
        if (StringUtils.isBlank(sql)) {
            log.info("原始SQL为空，跳过处理");
            return sql;
        }

        log.info("开始处理动态SQL中的日期表达式，原始采样日期: {}", sampleDate);

        // 将采样日期加1天，因为配置SQL时采样的是昨天的日期
        LocalDate date = LocalDate.parse(sampleDate).plusDays(1);
        String adjustedSampleDate = date.toString();
        log.info("调整后的采样日期(加1天): {}", adjustedSampleDate);

        // 替换CURDATE()
        sql = sql.replaceAll("(?i)CURDATE\\(\\)", "DATE('" + adjustedSampleDate + "')");

        // 替换UNIX_TIMESTAMP(CURDATE())
        long timestampSeconds = date.atStartOfDay().toEpochSecond(ZoneOffset.systemDefault().getRules().getOffset(date.atStartOfDay()));
        sql = sql.replaceAll("(?i)UNIX_TIMESTAMP\\(CURDATE\\(\\)\\)", String.valueOf(timestampSeconds));

        // 使用正则表达式查找并替换CURDATE() - INTERVAL n DAY
        java.util.regex.Pattern minusDayPattern = java.util.regex.Pattern.compile("(?i)CURDATE\\(\\)\\s*-\\s*INTERVAL\\s*(\\d+)\\s*DAY");
        java.util.regex.Matcher minusDayMatcher = minusDayPattern.matcher(sql);
        StringBuffer minusDaySb = new StringBuffer();
        while (minusDayMatcher.find()) {
            String dayStr = minusDayMatcher.group(1);
            int days = Integer.parseInt(dayStr);
            LocalDate resultDate = date.minusDays(days);
            minusDayMatcher.appendReplacement(minusDaySb, "DATE('" + resultDate + "')");
        }
        minusDayMatcher.appendTail(minusDaySb);
        sql = minusDaySb.toString();

        // 使用正则表达式查找并替换CURDATE() + INTERVAL n DAY
        java.util.regex.Pattern plusDayPattern = java.util.regex.Pattern.compile("(?i)CURDATE\\(\\)\\s*\\+\\s*INTERVAL\\s*(\\d+)\\s*DAY");
        java.util.regex.Matcher plusDayMatcher = plusDayPattern.matcher(sql);
        StringBuffer plusDaySb = new StringBuffer();
        while (plusDayMatcher.find()) {
            String dayStr = plusDayMatcher.group(1);
            int days = Integer.parseInt(dayStr);
            LocalDate resultDate = date.plusDays(days);
            plusDayMatcher.appendReplacement(plusDaySb, "DATE('" + resultDate + "')");
        }
        plusDayMatcher.appendTail(plusDaySb);
        sql = plusDaySb.toString();

        // 使用正则表达式查找并替换UNIX_TIMESTAMP(CURDATE() - INTERVAL n DAY)
        java.util.regex.Pattern tsMinusDayPattern = java.util.regex.Pattern.compile("(?i)UNIX_TIMESTAMP\\(CURDATE\\(\\)\\s*-\\s*INTERVAL\\s*(\\d+)\\s*DAY\\)");
        java.util.regex.Matcher tsMinusDayMatcher = tsMinusDayPattern.matcher(sql);
        StringBuffer tsMinusDaySb = new StringBuffer();
        while (tsMinusDayMatcher.find()) {
            String dayStr = tsMinusDayMatcher.group(1);
            int days = Integer.parseInt(dayStr);
            LocalDate resultDate = date.minusDays(days);
            long ts = resultDate.atStartOfDay().toEpochSecond(ZoneOffset.systemDefault().getRules().getOffset(resultDate.atStartOfDay()));
            tsMinusDayMatcher.appendReplacement(tsMinusDaySb, String.valueOf(ts));
        }
        tsMinusDayMatcher.appendTail(tsMinusDaySb);
        sql = tsMinusDaySb.toString();

        // 使用正则表达式查找并替换UNIX_TIMESTAMP(CURDATE() + INTERVAL n DAY)
        java.util.regex.Pattern tsPlusDayPattern = java.util.regex.Pattern.compile("(?i)UNIX_TIMESTAMP\\(CURDATE\\(\\)\\s*\\+\\s*INTERVAL\\s*(\\d+)\\s*DAY\\)");
        java.util.regex.Matcher tsPlusDayMatcher = tsPlusDayPattern.matcher(sql);
        StringBuffer tsPlusDaySb = new StringBuffer();
        while (tsPlusDayMatcher.find()) {
            String dayStr = tsPlusDayMatcher.group(1);
            int days = Integer.parseInt(dayStr);
            LocalDate resultDate = date.plusDays(days);
            long ts = resultDate.atStartOfDay().toEpochSecond(ZoneOffset.systemDefault().getRules().getOffset(resultDate.atStartOfDay()));
            tsPlusDayMatcher.appendReplacement(tsPlusDaySb, String.valueOf(ts));
        }
        tsPlusDayMatcher.appendTail(tsPlusDaySb);
        sql = tsPlusDaySb.toString();

        // 处理毫秒时间戳，乘以1000
        java.util.regex.Pattern msPattern = java.util.regex.Pattern.compile("(?i)UNIX_TIMESTAMP\\(([^\\)]+)\\)\\s*\\*\\s*1000");
        java.util.regex.Matcher msMatcher = msPattern.matcher(sql);
        StringBuffer msSb = new StringBuffer();
        while (msMatcher.find()) {
            // 如果已经替换了时间戳，直接乘以1000
            String tsExpr = msMatcher.group(1);
            if (tsExpr.matches("\\d+")) {
                long ts = Long.parseLong(tsExpr) * 1000;
                msMatcher.appendReplacement(msSb, String.valueOf(ts));
            } else {
                // 如果还是表达式，保持原样
                msMatcher.appendReplacement(msSb, msMatcher.group(0));
            }
        }
        msMatcher.appendTail(msSb);
        sql = msSb.toString();

        log.info("日期表达式处理完成，最终SQL: \n{}\n", sql);
        return sql;
    }

    /**
     * 处理跨系统数据分析
     *
     * @param businessSysConfig      业务系统配置
     * @param businessAccountConfigs 业务账号配置列表
     * @param businessApiConfigs     业务API配置列表
     * @param collectTimeRange       采集时间范围
     * @return API访问结果列表
     * @throws Exception 处理过程中可能发生的异常
     */
    private List<AdsOpsOpsApiAccessResult> handleInterSystemDataAnalysis(BusinessSysConfig businessSysConfig, List<BusinessAccountConfig> businessAccountConfigs, List<BusinessApiConfig> businessApiConfigs, String collectTimeRange, CountDownLatch accountCountDownLatch) throws Exception {
        // 1. 获取认证Token
        TokenCacheEntry cacheEntry = getAuthToken(businessSysConfig);
        String authToken = cacheEntry.getToken();
        Integer userId = cacheEntry.getToken().hashCode(); // 示例值，实际应根据认证返回

        // 2. 初始化结果集
        List<AdsOpsOpsApiAccessResult> adsOpsOpsApiAccessResults = new Vector<>();

        // 3. 获取匹配的API配置
        List<BusinessApiConfig> apiConfigs = findMatchingApiConfig(businessApiConfigs, businessSysConfig.getSysName());

        // 4. 处理不同采样账号
        log.info("[开始处理账号] 系统: {} | 账号数量: {}", businessSysConfig.getSysName(), businessAccountConfigs.size());
        //根据系统类型,获取各系统对应的线程池
        ExecutorService executor = ThreadPoolUtil.getSystemThreadPool(businessSysConfig.getSysType().getCode());

        final int[] processedCount = {0};
        for (BusinessAccountConfig accountConfig : businessAccountConfigs) {
            executor.execute(() -> {
                try {
                    // 记录当前处理的账号
                    log.debug("[开始处理] 系统: {} | 账号: {} | 进度: {}/{}", businessSysConfig.getSysName(), accountConfig.getAccountValue(), processedCount[0] + 1, businessAccountConfigs.size());

                    // 处理单个账号
                    List<AdsOpsOpsApiAccessResult> accountResults = processAccount(businessSysConfig, accountConfig, apiConfigs, authToken, userId, collectTimeRange);

                    // 将结果添加到总结果集合中
                    if (!accountResults.isEmpty()) {
                        adsOpsOpsApiAccessResults.addAll(accountResults);
                    }

                    // 每处理100个账号输出一次日志
                    processedCount[0]++;
                    if (processedCount[0] % 100 == 0) {
                        log.info(" 系统: {} | 已处理: {}/{}", businessSysConfig.getSysName(), processedCount[0], businessAccountConfigs.size());
                    }

                    // 应用请求延迟，降低系统并发压力
                    if (requestDelay > 0) {
                        try {
                            Thread.sleep(requestDelay);
                            log.debug("[账号处理延迟] 系统: {} | 应用请求延迟: {}ms", businessSysConfig.getSysName(), requestDelay);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            log.warn("[账号处理延迟] 被中断", e);
                        }
                    }
                } catch (Exception e) {
                    log.error("[采集失败] 系统: {} | 账号: {} | 错误: {}", businessSysConfig.getSysName(), accountConfig.getAccountValue(), e.getMessage());
                } finally {

                    accountCountDownLatch.countDown();
                }
            });
        }
        log.info("[处理完成] 系统: {} | 总计: {}/{} | 结果数: {}", businessSysConfig.getSysName(), processedCount[0], businessAccountConfigs.size(), adsOpsOpsApiAccessResults.size());
        accountCountDownLatch.await();
        // 5. 将结果写入Doris - 现在只有在所有账号处理完成后才执行
        if (!adsOpsOpsApiAccessResults.isEmpty()) {

            log.info("[准备写入Doris] 系统: {} | 数据条数: {}", businessSysConfig.getSysName(), adsOpsOpsApiAccessResults.size());
            //写入Doris方法入口
            //insertDorisForQueue(adsOpsOpsApiAccessResults);
        }
        return adsOpsOpsApiAccessResults;
    }

    /**
     * 处理responseMapping中的getKey数组，替换#{account}为实际值
     *
     * @param responseMapping     响应映射配置
     * @param apiAccessResultData API访问结果数据
     */
    /**
     * 处理单个账号的数据采集
     *
     * @param businessSysConfig 业务系统配置
     * @param accountConfig     账号配置
     * @param apiConfigs        API配置列表
     * @param authToken         认证Token
     * @param userId            用户ID
     * @param collectTimeRange  采集时间范围
     * @return 该账号的API访问结果列表
     * @throws Exception 处理过程中可能发生的异常
     */
    private List<AdsOpsOpsApiAccessResult> processAccount(BusinessSysConfig businessSysConfig, BusinessAccountConfig accountConfig, List<BusinessApiConfig> apiConfigs, String authToken, Integer userId, String collectTimeRange) throws Exception {
        List<AdsOpsOpsApiAccessResult> apiAccessResultDatas = new Vector<>();

        // 处理每个API配置
        apiConfigs.forEach(apiConfig -> {
            try {
                // 处理单个API调用
                AdsOpsOpsApiAccessResult result = processApiCall(businessSysConfig, apiConfig, authToken, userId, accountConfig, collectTimeRange);

                if (result != null) {
                    apiAccessResultDatas.add(result);
                }
            } catch (Exception e) {
                log.error("[API处理失败] 系统: {} | 指标: {} | 账号: {} | API: {} | 错误: {}", businessSysConfig.getSysName(), apiConfig.getMetricId(), accountConfig.getAccountValue(), apiConfig.getApiPath(), e.getMessage(), e);
            }
        });

        // 合并多个API的结果（如果有多个API）
        if (apiAccessResultDatas.size() > 1) {
            return Collections.singletonList(mergeApiResults(apiAccessResultDatas));
        } else {
            return apiAccessResultDatas;
        }
    }

    /**
     * 处理单个API调用
     *
     * @param businessSysConfig 业务系统配置
     * @param apiConfig         API配置
     * @param authToken         认证Token
     * @param userId            用户ID
     * @param accountConfig     账号配置
     * @param collectTimeRange  采集时间范围
     * @return API访问结果，如果处理失败返回null
     */
    private AdsOpsOpsApiAccessResult processApiCall(BusinessSysConfig businessSysConfig, BusinessApiConfig apiConfig, String authToken, Integer userId, BusinessAccountConfig accountConfig, String collectTimeRange) {

        // 深拷贝apiConfig对象
        BusinessApiConfig apiConfigTmp = JSON.parseObject(JSON.toJSONString(apiConfig), BusinessApiConfig.class);
        AdsOpsOpsApiAccessResult apiAccessResultData = AdsOpsOpsApiAccessResult.builder().build();

        // 执行API调用，最多重试MAX_RETRY_ATTEMPTS次
        long startTime = System.currentTimeMillis();
        Map<String, JSONObject> apiResponseTemp = new ConcurrentHashMap<>();
        String errorReason = "API调用返回空数据";

        // 重试逻辑
        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                // 执行API调用
                apiResponseTemp = executeApiCall(businessSysConfig, apiConfigTmp, authToken, userId, accountConfig, collectTimeRange, apiAccessResultData);

                // 如果获取到了非空结果，跳出重试循环
                if (!apiResponseTemp.isEmpty()) {
                    break;
                }

                // 记录重试日志
                if (attempt < MAX_RETRY_ATTEMPTS) {
                    log.error("[重试采集] 系统: {} | 指标: {} | 账号: {} | 第 {} 次采集结果为空 | 原因: {} | 即将进行第 {} 次尝试", businessSysConfig.getSysName(), apiConfigTmp.getMetricId(), accountConfig.getAccountValue(), attempt, errorReason, attempt + 1);
                }
            } catch (UnsupportedEncodingException e) {
                // 记录异常信息
                errorReason = "编码异常: " + e.getMessage();
                log.error("[重试采集] 系统: {} | 指标: {} | 账号: {} | 第 {} 次采集失败 | 原因: {}", businessSysConfig.getSysName(), apiConfigTmp.getMetricId(), accountConfig.getAccountValue(), attempt, errorReason, e);

                // 最后一次尝试失败时返回null
                if (attempt == MAX_RETRY_ATTEMPTS) {
                    return null;
                }
            }
        }

        // 所有重试都失败后的处理
        if (apiResponseTemp.isEmpty()) {
            log.error("[采集失败] 系统: {} | 指标: {} | 账号: {} | 经过 {} 次尝试后数据采集结果仍为空 | 原因: {}", businessSysConfig.getSysName(), apiConfigTmp.getMetricId(), accountConfig.getAccountValue(), MAX_RETRY_ATTEMPTS, errorReason);
            return null;
        }

        // 处理API响应结果
        JSONObject apiResponse = apiResponseTemp.values().iterator().next();
        String params = apiResponseTemp.keySet().iterator().next();

        // 设置基本属性
        apiAccessResultData.setParams(params);
        apiAccessResultData.setApiPath(apiConfigTmp.getApiPath());
        apiAccessResultData.setResponseData(apiResponse.toJSONString());
        apiAccessResultData.setAccount(accountConfig.getAccountValue());
        apiAccessResultData.setDynamic(accountConfig.getSampleStrategy() == BusinessAccountConfig.SampleStrategy.DYNAMIC);
        apiAccessResultData.setSystemName(businessSysConfig.getSysName());
        apiAccessResultData.setCostTime(Math.toIntExact(System.currentTimeMillis() - startTime));
        apiAccessResultData.setMetricId(apiConfigTmp.getMetricId().toString());

        // 统计日期设置为采集时间范围的最大值
        apiAccessResultData.setStatDate(LocalDate.parse(collectTimeRange.split(",")[1]));
        apiAccessResultData.setRequestTime(LocalDateTime.now());

        // 根据系统类型进行差异化处理
        try {
            switch (businessSysConfig.getSysType()) {
                case CASE:
                    processCaseSystem(apiResponse, accountConfig, apiConfigTmp.getResponseMapping(), apiAccessResultData);
                    break;
                case SEARCH:
                    processSearchSystem(apiResponse, accountConfig, apiConfigTmp.getResponseMapping(), apiAccessResultData);
                    break;
                case ARCHIVE:
                    processArchiveSystem(apiResponse, accountConfig, apiConfigTmp.getResponseMapping(), apiAccessResultData);
                    break;
            }
        } catch (Exception e) {
            log.error("[数据处理失败] 系统: {} | 指标: {} | 账号: {} | 错误: {}", businessSysConfig.getSysName(), apiConfigTmp.getMetricId(), accountConfig.getAccountValue(), e.getMessage(), e);
            return null;
        }

        return apiAccessResultData;
    }

    /**
     * 合并多个API的结果
     *
     * @param apiAccessResultDatas API访问结果列表
     * @return 合并后的结果
     */
    private AdsOpsOpsApiAccessResult mergeApiResults(List<AdsOpsOpsApiAccessResult> apiAccessResultDatas) {
        AdsOpsOpsApiAccessResult apiAccessResultData = apiAccessResultDatas.get(0);

        // 合并统计数量
        apiAccessResultData.setStatCount(apiAccessResultDatas.stream().mapToLong(AdsOpsOpsApiAccessResult::getStatCount).sum());

        // 合并kv_content，忽略空数组，只合并非空数组中的JSON对象
        JSONArray mergedArray = new JSONArray();

        // 遍历所有结果的kvContent
        for (AdsOpsOpsApiAccessResult result : apiAccessResultDatas) {
            String kvContent = result.getKvContent();
            // 跳过空值或空数组
            if (kvContent == null || kvContent.isEmpty() || kvContent.equals("[]")) {
                continue;
            }

            try {
                // 解析JSON数组
                JSONArray jsonArray = JSON.parseArray(kvContent);
                // 跳过空数组
                if (jsonArray == null || jsonArray.isEmpty()) {
                    continue;
                }

                // 将数组中的每个对象添加到合并数组中
                for (int i = 0; i < jsonArray.size(); i++) {
                    Object item = jsonArray.get(i);
                    // 只添加非null的项
                    if (item != null) {
                        mergedArray.add(item);
                    }
                }
            } catch (Exception e) {
                log.warn("合并kvContent时解析JSON失败: {}", kvContent, e);
            }
        }

        // 设置合并后的kvContent
        apiAccessResultData.setKvContent(mergedArray.toJSONString());

        // 合并params，并保持json结构
        String params = apiAccessResultDatas.stream().map(AdsOpsOpsApiAccessResult::getParams).collect(Collectors.joining(",", "[", "]"));
        apiAccessResultData.setParams(params);
        // 设置合并后的responseData, 并保持JSON格式
        String responseData = apiAccessResultDatas.stream().map(AdsOpsOpsApiAccessResult::getResponseData).collect(Collectors.joining(",", "[", "]"));
        apiAccessResultData.setResponseData(responseData);

        return apiAccessResultData;
    }

    /**
     * 处理响应映射中的getKey数组
     * 此方法旨在解析和处理从API响应中提取的getKey数组，根据特定条件替换键值
     *
     * @param responseMapping     存放对于响应结果的解析方式的对象
     * @param apiAccessResultData API响应结果数据对象，包含参数信息
     */
    private void processCountKeyArray(JSONObject responseMapping, AdsOpsOpsApiAccessResult apiAccessResultData) {
        //如果没有getKey,jsonArray就是null
        JSONArray countKeyArr = responseMapping.getJSONArray("countKey");
        if (countKeyArr == null || countKeyArr.isEmpty()) {
            return;
        }
        //getKeyArr例如:["#{account}","","aaa123"]
        for (int i = 0; i < countKeyArr.size(); i++) {
            String getKey = countKeyArr.getString(i);
            if ("#{account}".equals(getKey)) {
                String value = apiAccessResultData.getAccount();
                //把#{account}替换成真实的账号
                countKeyArr.set(i, value);
                //把替换后的jsonArray重新设置回responseMapping
                responseMapping.put("countKey", countKeyArr);
            }
        }
    }

    /**
     * 处理排除键数组
     * 该方法用于遍历和处理responseMapping中的"excludeKey"数组，将特定的占位符替换为实际的账户信息
     *
     * @param responseMapping 包含"excludeKey"数组的JSON对象，用于API响应的映射配置
     * @param apiAccessResultData 包含账户信息的API访问结果对象，用于替换占位符
     */
    private void processExcludeKeyArray(JSONObject responseMapping, AdsOpsOpsApiAccessResult apiAccessResultData) {
        JSONArray excludeKeyArr = responseMapping.getJSONArray("excludeKey");
        if (excludeKeyArr == null || excludeKeyArr.isEmpty()) {
            return;
        }
        //excludeKeyArr 例如: ["#{account}","","aaa"]
        for (int i = 0; i < excludeKeyArr.size(); i++) {
            String excludeKey = excludeKeyArr.getString(i);
            if ("#{account}".equals(excludeKey)) {
                excludeKeyArr.set(i, apiAccessResultData.getAccount());
                responseMapping.put("excludeKey", excludeKeyArr);
            }
        }
    }

    /**
     * 将结果列表写入Doris数据库
     *
     * @param resultList API访问结果列表
     */
    private void insertDorisForQueue(List<AdsOpsOpsApiAccessResult> resultList) {
        if (resultList == null || resultList.isEmpty()) {
            log.warn("没有数据需要写入Doris");
            return;
        }

        try {
            // 将对象列表转换为表格格式的文本
            String tableData = convertResultsToTableFormat(resultList);

            // 写入Doris数据库
            dorisStreamLoad.sendData(tableData, DORIS_TABLE);
            log.info("[数据写入] 成功将 {} 条数据写入 Doris 表 {}", resultList.size(), DORIS_TABLE);
        } catch (Exception e) {
            log.error("将数据写入Doris时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 将结果对象列表转换为表格格式的文本
     *
     * @param resultList API访问结果列表
     * @return 表格格式的文本数据
     */
    private String convertResultsToTableFormat(List<AdsOpsOpsApiAccessResult> resultList) {
        StringBuilder sb = new StringBuilder();

        resultList.forEach(data -> {
            // 获取对象的所有字段
            Field[] fields = data.getClass().getDeclaredFields();

            // 处理每个字段
            for (Field field : fields) {
                // 跳过serialVersionUID字段
                if (field.getName().equals("serialVersionUID")) {
                    continue;
                }

                // 设置字段可访问
                field.setAccessible(true);

                try {
                    // 获取字段值并添加到结果中，使用制表符分隔
                    sb.append(field.get(data)).append("\t");
                } catch (IllegalAccessException e) {
                    log.error("获取字段{}的值时发生错误: {}", field.getName(), e.getMessage(), e);
                    // 当发生错误时，添加空值
                    sb.append("").append("\t");
                }
            }

            // 删除最后一个多余的制表符并添加换行符
            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1);
            }
            sb.append("\n");
        });

        // 删除最后一个多余的换行符
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }

        return sb.toString();
    }

    private List<BusinessApiConfig> findMatchingApiConfig(List<BusinessApiConfig> businessApiConfigs, String sysName) {
        // 根据系统类型查找对应的API配置
        return businessApiConfigs.stream().filter(apiConfig -> apiConfig.getSystemName().equals(sysName)).collect(Collectors.toList());
    }

    private Map<String, Object> performPortalAuth(String authUrl, String username, String password) {
        // 实现门户认证逻辑
        Map<String, String> authRequest = new HashMap<>();
        authRequest.put("account", username);
        authRequest.put("password", password);
        JSONObject response = HttpUtil.postForEntity(authUrl, authRequest, JSONObject.class);

        if (response == null){
            throw new RuntimeException("门户认证失败，可能是门户挂了");
        }

        String token = response.getJSONObject("data").getString("token");
        Integer userId = response.getJSONObject("data").getInteger("userId");
        Map<String, Object> authResult = new HashMap<>();
        authResult.put("userId", userId);
        authResult.put("token", token);
        return authResult;
    }

    /**
     * 执行API调用的通用方法
     *
     * @param businessSysConfig   业务系统配置
     * @param apiConfig           API配置
     * @param token               认证令牌
     * @param userId              用户ID
     * @param accountConfig       账号配置
     * @param collectTimeRange    采集时间范围
     * @param apiAccessResultData API访问结果数据
     * @return 包含API响应的Map
     * @throws UnsupportedEncodingException 编码异常
     */
    private Map<String, JSONObject> executeApiCall(BusinessSysConfig businessSysConfig, BusinessApiConfig apiConfig, String token, Integer userId, BusinessAccountConfig accountConfig, String collectTimeRange, AdsOpsOpsApiAccessResult apiAccessResultData) throws UnsupportedEncodingException {
        // 创建结果Map
        Map<String, JSONObject> resultMap = new ConcurrentHashMap<>();

        try {
            // 检查是否是DSL查询
            if (apiConfig.getIsDsl() != null && apiConfig.getIsDsl()) {
                // 执行DSL查询
                executeDslQuery(apiConfig, accountConfig, collectTimeRange, apiAccessResultData, resultMap);
            } else {
                // 执行普通API调用
                // 1. 准备请求头
                HttpHeaders headers = prepareHeaders(token, businessSysConfig.getAppid(), userId);

                // 2. 准备请求参数
                String paramsStr = prepareRequestParams(token, businessSysConfig, apiConfig, accountConfig, collectTimeRange);
                if(StringUtils.isEmpty(paramsStr)){//如果档案没查到，则返回空
                    return resultMap;
                }

                // 3. 执行请求并处理响应
                JSONObject params = JSONObject.parseObject(paramsStr);
                String apiUrl = businessSysConfig.getBaseUrl() + apiConfig.getApiPath();

                // 根据请求方法执行不同的请求
                if (apiConfig.getMethod() == BusinessApiConfig.Method.POST) {
                    executePostRequest(apiUrl, headers, params, paramsStr, apiConfig, apiAccessResultData, resultMap);
                } else if (apiConfig.getMethod() == BusinessApiConfig.Method.GET) {
                    executeGetRequest(apiUrl, headers, params, paramsStr, apiConfig, apiAccessResultData, resultMap);
                } else {
                    throw new RuntimeException("不支持的API请求方法: " + apiConfig.getMethod());
                }
            }

            return resultMap;
        } catch (Exception e) {
            log.error("[API调用错误] 系统: {} | 指标: {} | 账号: {} | API: {} | 错误: {}", businessSysConfig.getSysName(), apiConfig.getMetricId(), accountConfig.getAccountValue(), apiConfig.getApiPath(), e.getMessage(), e);
            apiAccessResultData.setIsSuccess(false);
            apiAccessResultData.setErrorCode("500");
            return resultMap;
        }
    }

    /**
     * 准备HTTP请求头
     */
    private HttpHeaders prepareHeaders(String token, String appid, Integer userId) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Token", token);
        headers.add("Appid", appid);
        headers.add("Userid", userId.toString());
        headers.add("Content-Type", "application/json");
        return headers;
    }

    /**
     * 准备请求参数
     * 如果查询不到档案不会返回空字符串
     */
    private String prepareRequestParams(String token, BusinessSysConfig businessSysConfig, BusinessApiConfig apiConfig, BusinessAccountConfig accountConfig, String collectTimeRange) throws UnsupportedEncodingException {
        // 获取参数模板
        String paramsStr = apiConfig.getParamsTemplate().toString();
        String[] timeRange = collectTimeRange.split(",");

        // 1. 替换时间信息
        paramsStr = replaceTimeInfo(paramsStr, apiConfig, timeRange);

        // 2. 替换账号信息
        paramsStr = replaceAccountInfo(paramsStr, businessSysConfig, accountConfig);

        // 3. 处理特定系统的参数
        if (SystemEnum.ARCHIVE.getChineseName().equals(businessSysConfig.getSysName())) {
            paramsStr = processArchiveSystem(token, paramsStr, businessSysConfig, accountConfig, timeRange);
        }

        // 4. 处理案件系统的参数
        if (SystemEnum.CASE.getChineseName().equals(businessSysConfig.getSysName()) && accountConfig.getCaseParam() != null) {
            paramsStr = processCaseSystem(paramsStr, accountConfig);
        }

        return paramsStr;
    }

    /**
     * 替换账号信息
     */
    private String replaceAccountInfo(String paramsStr, BusinessSysConfig businessSysConfig, BusinessAccountConfig accountConfig) {
        // 全息系统需要将档案名称转为小写
        if (SystemEnum.ARCHIVE.getChineseName().equals(businessSysConfig.getSysName()) && accountConfig.getAccountType() == BusinessAccountConfig.AccountType.EMAIL) {
            String accountValue = accountConfig.getAccountValue().toLowerCase();
            return paramsStr.replace("#{account}", accountValue);
        } else {
            return paramsStr.replace("#{account}", accountConfig.getAccountValue());
        }
    }

    /**
     * 替换时间信息
     */
    private String replaceTimeInfo(String paramsStr, BusinessApiConfig apiConfig, String[] timeRange) {
        String dataFormat = apiConfig.getParamsTemplate().getString("dateFormat");

        if ("timestamp".equals(dataFormat)) {
            // 时间戳格式，将时间转化为13位时间戳，使用系统默认时区
            long startTimestamp = LocalDate.parse(timeRange[0]).atStartOfDay().atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
            long endTimestamp = LocalDate.parse(timeRange[1]).atStartOfDay().atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli() + 24 * 60 * 60 * 1000 - 1;

            paramsStr = paramsStr.replace("112233", String.valueOf(startTimestamp));
            paramsStr = paramsStr.replace("445566", String.valueOf(endTimestamp));
        } else {
            // 字符串日期格式
            paramsStr = paramsStr.replace("#{startDay}", timeRange[0]);
            paramsStr = paramsStr.replace("#{endDay}", timeRange[1]);
        }

        return paramsStr;
    }

    /**
     * 处理全息系统特定参数
     * 如果查询不到档案，会返回空字符串
     */
    private String processArchiveSystem(String token, String paramsStr, BusinessSysConfig businessSysConfig, BusinessAccountConfig accountConfig, String[] timeRange) throws UnsupportedEncodingException {
        try {
            // 构建获取档案ID的URL
            StringBuilder url = new StringBuilder(archiveIdUrl + "?");
            Map<String, Object> getArchiveIdParams = getArchiveIdParamMap(accountConfig, timeRange);
            getWholeUrl(url, getArchiveIdParams);

            // 调用API获取档案ID
            HttpHeaders headers = prepareHeaders(token, businessSysConfig.getAppid(), 0); // 使用默认token
            HttpRequest httpRequest = StaticMethodRecorderProxy.record(isUseMock, cn.hutool.http.HttpUtil.class, "createGet", params1 -> cn.hutool.http.HttpUtil.createGet((String) params1[0]), url.toString());
            httpRequest.header(headers);
            HttpResponse httpResponse = StaticMethodRecorderProxy.record(isUseMock, HttpRequest.class, "execute", params1 -> httpRequest.execute(), getArchiveIdParams);
            String body = StaticMethodRecorderProxy.record(isUseMock, HttpResponse.class, "body", params1 -> httpResponse.body(), getArchiveIdParams);

            // 解析响应获取档案ID
            JSONObject responseJson = JSONObject.parseObject(body);
            JSONObject dataJson = responseJson.getJSONObject("data");
            JSONArray listArray = dataJson.getJSONArray("list");

            if (listArray.isEmpty()) {
                log.info("未获取到档案ID");
                return "";
            }

            // 如果有多个档案ID，则优先取指定档案类型的档案，其次取lastRelationTime最大的一条档案
            JSONObject selectedItem;
            if (listArray.size() > 1) {
                // 根据账号类型确定档案类型
                Integer configArchiveType;

                // 根据账号类型推断档案类型
                if (accountConfig.getAccountType() != null) {
                    configArchiveType = ArchiveTypeConstants.getArchiveTypeByAccountType(accountConfig.getAccountType());
                } else {
                    configArchiveType = null;
                }

                if (configArchiveType != null) {
                    // 先尝试按指定的档案类型筛选
                    List<JSONObject> filteredByType = listArray.stream()
                            .map(item -> (JSONObject) item)
                            .filter(item -> {
                                Object arcType = item.get("arcType");
                                Object archivesType = item.get("archivesType");
                                return (arcType != null && configArchiveType.equals(Integer.valueOf(arcType.toString()))) ||
                                       (archivesType != null && configArchiveType.equals(Integer.valueOf(archivesType.toString())));
                            })
                            .collect(Collectors.toList());

                    if (!filteredByType.isEmpty()) {
                        // 如果按类型筛选后还有多个，则取lastRelationTime最大的一条
                        selectedItem = filteredByType.stream().max(Comparator.comparingLong(item -> {
                            Object lastRelationTime = item.get("lastRelationTime");
                            return lastRelationTime != null ? Long.parseLong(lastRelationTime.toString()) : 0;
                        })).orElse(filteredByType.get(0));
                        log.info("找到多个档案ID，按指定档案类型{}筛选后选择lastRelationTime最大的一条", configArchiveType);
                    } else {
                        // 如果按类型筛选后没有结果，则回退到原来的逻辑
                        selectedItem = listArray.stream().map(item -> (JSONObject) item).max(Comparator.comparingLong(item -> {
                            Object lastRelationTime = item.get("lastRelationTime");
                            Object arcAccount = item.get("arcAccount");
                            if (arcAccount != null && !arcAccount.equals(accountConfig.getAccountValue())) {
                                return 0L;
                            }
                            return lastRelationTime != null ? Long.parseLong(lastRelationTime.toString()) : 0;
                        })).orElse(listArray.getJSONObject(0));
                        log.info("找到多个档案ID，但没有指定类型{}的档案，选择lastRelationTime最大的一条", configArchiveType);
                    }
                } else {
                    // 如果没有指定档案类型，则使用原来的逻辑
                    selectedItem = listArray.stream().map(item -> (JSONObject) item).max(Comparator.comparingLong(item -> {
                        Object lastRelationTime = item.get("lastRelationTime");
                        Object arcAccount = item.get("arcAccount");
                        if (arcAccount != null && !arcAccount.equals(accountConfig.getAccountValue())) {
                            return 0L;
                        }
                        return lastRelationTime != null ? Long.parseLong(lastRelationTime.toString()) : 0;
                    })).orElse(listArray.getJSONObject(0));
                    log.info("找到多个档案ID，未指定档案类型，选择lastRelationTime最大的一条");
                }
            } else {
                selectedItem = listArray.getJSONObject(0);
            }

            String archiveId = selectedItem.getString("arcId");
            Object arcAccountType = selectedItem.get("arcAccountType");
            Object arcType = selectedItem.get("arcType");
            Object archivesType = selectedItem.get("archivesType");

            // 记录选中的档案类型信息
            log.info("选中的档案ID: {}, 档案类型: {}/{}, 账号类型: {}",
                    archiveId,
                    arcType != null ? arcType : "未知",
                    archivesType != null ? archivesType : "未知",
                    arcAccountType != null ? arcAccountType : "未知");

            // 替换参数中的档案ID和账号类型
            if (arcAccountType != null) {
                paramsStr = paramsStr.replace("444444", arcAccountType.toString());
            }

            // 如果参数中有arcType或archiveType占位符，且档案有对应的类型值，则替换
            if (arcType != null) {
                paramsStr = paramsStr.replace("#{arcType}", arcType.toString());
                paramsStr = paramsStr.replace("#{archiveType}", arcType.toString());
            } else if (archivesType != null) {
                paramsStr = paramsStr.replace("#{arcType}", archivesType.toString());
                paramsStr = paramsStr.replace("#{archiveType}", archivesType.toString());
            }

            log.info("获取到档案ID:{}", archiveId);
            log.info("获取档案ID时,响应结果:{}", body);

            return paramsStr.replace("#{arcId}", archiveId);
        } catch (Exception e) {
            log.error("获取档案ID时发生错误: {}", e.getMessage(), e);
            return paramsStr;
        }
    }

    /**
     * 处理案件系统特定参数
     */
    private String processCaseSystem(String paramsStr, BusinessAccountConfig accountConfig) {
        JSONObject caseParam = accountConfig.getCaseParam();

        // 替换案件相关参数
        paramsStr = paramsStr.replace("111111", StringUtils.defaultIfEmpty(caseParam.getString("caseId"), "0"));
        paramsStr = paramsStr.replace("333333", StringUtils.defaultIfEmpty(caseParam.getString("clueId"), "0"));
        paramsStr = paramsStr.replace("#{clueId}", StringUtils.defaultIfEmpty(caseParam.getString("clueId"), ""));
        paramsStr = paramsStr.replace("#{caseName}", StringUtils.defaultIfEmpty(caseParam.getString("caseName"), ""));
        paramsStr = paramsStr.replace("#{clueName}", StringUtils.defaultIfEmpty(caseParam.getString("clueName"), ""));
        paramsStr = paramsStr.replace("222222", StringUtils.defaultIfEmpty(caseParam.getString("objectId"), "0"));
        paramsStr = paramsStr.replace("#{objectName}", StringUtils.defaultIfEmpty(caseParam.getString("objectName"), ""));

        return paramsStr;
    }

    /**
     * 执行POST请求
     */
    private void executePostRequest(String apiUrl, HttpHeaders headers, JSONObject params, String paramsStr, BusinessApiConfig apiConfig, AdsOpsOpsApiAccessResult apiAccessResultData, Map<String, JSONObject> resultMap) {
        try {
            // 创建POST请求
            HttpRequest httpRequest = createHttpRequestPost(apiUrl);
            httpRequest.header(headers);
            httpRequest.body(JsonUtil.toJsonString(params));

            // 执行请求并获取响应
            HttpResponse httpResponse = StaticMethodRecorderProxy.record(isUseMock, HttpRequest.class, "execute", params1 -> httpRequest.execute(), params);
            String body = StaticMethodRecorderProxy.record(isUseMock, HttpResponse.class, "body", params1 -> httpResponse.body(), params);

            // 处理响应结果
            processApiResponse(httpResponse, body, apiConfig, apiAccessResultData, paramsStr, resultMap);

            // 应用请求延迟，降低系统并发压力
            applyRequestDelay();

        } catch (Exception e) {
            // 确保账号不为null
            if (apiAccessResultData.getAccount() == null) {
                apiAccessResultData.setAccount("unknown");
            }

            // 确保耗时不为null
            if (apiAccessResultData.getCostTime() == null) {
                apiAccessResultData.setCostTime(0);
            }

            log.error("[POST请求错误] 系统: {} | 指标: {} | 账号: {} | API: {} | 错误: {}", apiConfig.getSystemName(), apiConfig.getMetricId(), apiAccessResultData.getAccount(), apiConfig.getApiPath(), e.getMessage(), e);
            apiAccessResultData.setIsSuccess(false);
            apiAccessResultData.setErrorCode("500");
        }
    }

    /**
     * 执行GET请求
     */
    private void executeGetRequest(String apiUrl, HttpHeaders headers, JSONObject params, String paramsStr, BusinessApiConfig apiConfig, AdsOpsOpsApiAccessResult apiAccessResultData, Map<String, JSONObject> resultMap) throws UnsupportedEncodingException {
        try {
            // 构建GET请求URL
            StringBuilder url = new StringBuilder(apiUrl + "?");
            getWholeUrl(url, params);

            // 创建GET请求
            HttpRequest httpRequest = StaticMethodRecorderProxy.record(isUseMock, cn.hutool.http.HttpUtil.class, "createGet", params1 -> cn.hutool.http.HttpUtil.createGet((String) params1[0]), url.toString());
            httpRequest.header(headers);

            // 执行请求并获取响应
            HttpResponse httpResponse = StaticMethodRecorderProxy.record(isUseMock, HttpRequest.class, "execute", params1 -> httpRequest.execute(), params);
            String body = StaticMethodRecorderProxy.record(isUseMock, HttpResponse.class, "body", params1 -> httpResponse.body(), params);

            // 处理响应结果
            processApiResponse(httpResponse, body, apiConfig, apiAccessResultData, paramsStr, resultMap);

            // 应用请求延迟，降低系统并发压力
            applyRequestDelay();

        } catch (Exception e) {
            // 确保账号不为null
            if (apiAccessResultData.getAccount() == null) {
                apiAccessResultData.setAccount("unknown");
            }

            // 确保耗时不为null
            if (apiAccessResultData.getCostTime() == null) {
                apiAccessResultData.setCostTime(0);
            }

            log.error("[GET请求错误] 系统: {} | 指标: {} | 账号: {} | API: {} | 错误: {}", apiConfig.getSystemName(), apiConfig.getMetricId(), apiAccessResultData.getAccount(), apiConfig.getApiPath(), e.getMessage(), e);
            apiAccessResultData.setIsSuccess(false);
            apiAccessResultData.setErrorCode("500");
        }
    }

    /**
     * 处理API响应
     */
    private void processApiResponse(HttpResponse httpResponse, String body, BusinessApiConfig apiConfig, AdsOpsOpsApiAccessResult apiAccessResultData, String paramsStr, Map<String, JSONObject> resultMap) {
        // 设置响应状态
        apiAccessResultData.setResponseStatus((short) httpResponse.getStatus());
        apiAccessResultData.setIsSuccess(httpResponse.isOk());
        apiAccessResultData.setErrorCode(String.valueOf(httpResponse.getStatus()));

        // 确保账号不为null
        if (apiAccessResultData.getAccount() == null) {
            apiAccessResultData.setAccount("unknown");
        }


        // 记录响应日志
        //log.info("[API调用] 系统: {} | 方法: {} | 接口: {} | 指标: {} | 账号: {} | 状态: {} | 结果: {} | api_config表主键: {}", apiConfig.getSystemName(), apiConfig.getMethod(), apiConfig.getApiPath(), apiConfig.getMetricId(), apiAccessResultData.getAccount(), httpResponse.getStatus(), body.length() > 500 ? body.substring(0, 500) + "..." : body, apiConfig.getId());
        log.info("[API调用] 系统: {} | 方法: {} | 接口: {} | 指标: {} | api_config表主键: {} | 状态: {} | 结果: {} ", apiConfig.getSystemName(), apiConfig.getMethod(), apiConfig.getApiPath(), apiConfig.getMetricId(), apiConfig.getId(), httpResponse.getStatus(), body.length() > 500 ? body.substring(0, 500) + "..." : body);

        // 解析响应JSON并存入结果Map
        try {
            resultMap.put(paramsStr, JSONObject.parseObject(body));
        } catch (Exception e) {
            //log.error("[JSON解析错误] 系统: {} | 指标: {} | 账号: {} | API: {} | 错误: {} | api_config表主键: {}", apiConfig.getSystemName(), apiConfig.getMetricId(), apiAccessResultData.getAccount(), apiConfig.getApiPath(), e.getMessage(), apiConfig.getId());
            log.error("[JSON解析错误] 系统: {} | 指标: {}  | API: {} | 错误: {} | api_config表主键: {}", apiConfig.getSystemName(), apiConfig.getMetricId(), apiConfig.getApiPath(), e.getMessage(), apiConfig.getId());
            resultMap.put(paramsStr, new JSONObject());
        }
    }

    /**
     * 执行DSL查询
     *
     * @param apiConfig           API配置
     * @param accountConfig       账号配置
     * @param collectTimeRange    采集时间范围
     * @param apiAccessResultData API访问结果数据
     * @param resultMap           结果Map
     */
    private void executeDslQuery(BusinessApiConfig apiConfig, BusinessAccountConfig accountConfig, String collectTimeRange, AdsOpsOpsApiAccessResult apiAccessResultData, Map<String, JSONObject> resultMap) {
        try {
            long startTime = System.currentTimeMillis();
           /* log.info("[开始执行DSL查询] 指标ID: {} | 账号: {} | 系统: {} | 索引: {}",
                    apiConfig.getMetricId(), accountConfig.getAccountValue(), apiConfig.getSystemName(), apiConfig.getApiPath());*/

            // 1. 准备DSL查询参数
            JSONObject paramsTemplateObj = apiConfig.getParamsTemplate();
            // 移除dateFormat字段，避免Elasticsearch报错
            paramsTemplateObj.remove("dateFormat");
            String dslTemplate = paramsTemplateObj.toJSONString();
            // 尝试格式化JSON，如果是有效的JSON则美化输出
            String formattedDsl = formatJsonIfPossible(dslTemplate);
            /*log.info("[原始DSL模板] 指标ID: {} | 账号: {} | DSL模板: \n{}\n",
                    apiConfig.getMetricId(), accountConfig.getAccountValue(), formattedDsl);*/
            String[] timeRange = collectTimeRange.split(",");

            // 2. 替换账号信息
            String dsl = dslTemplate.replace("#{account}", accountConfig.getAccountValue());
            /*log.info("[替换账号后的DSL] 指标ID: {} | 账号: {} | 采集时间范围: {}",
                    apiConfig.getMetricId(), accountConfig.getAccountValue(), collectTimeRange);*/

            // 3. 替换时间信息
            if (dsl.contains("112233") || dsl.contains("445566")) {
                // 时间戳格式
                long startTimestamp = LocalDate.parse(timeRange[0]).atStartOfDay().atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                long endTimestamp = LocalDate.parse(timeRange[1]).atStartOfDay().atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli() + 24 * 60 * 60 * 1000 - 1;

                dsl = dsl.replace("112233", String.valueOf(startTimestamp));
                dsl = dsl.replace("445566", String.valueOf(endTimestamp));
                /*log.info("[替换时间戳] 指标ID: {} | 账号: {} | 开始时间戳: {} | 结束时间戳: {}",
                        apiConfig.getMetricId(), accountConfig.getAccountValue(), startTimestamp, endTimestamp);*/
            } else {
                // 字符串日期格式
                dsl = dsl.replace("#{startDay}", timeRange[0]);
                dsl = dsl.replace("#{endDay}", timeRange[1]);
                /*log.info("[替换日期字符串] 指标ID: {} | 账号: {} | 开始日期: {} | 结束日期: {}",
                        apiConfig.getMetricId(), accountConfig.getAccountValue(), timeRange[0], timeRange[1]);*/
            }

            // 尝试格式化JSON，如果是有效的JSON则美化输出
            String formattedFinalDsl = formatJsonIfPossible(dsl);
            /*log.debug("[最终执行的DSL查询] 指标ID: {} | 账号: {} | DSL: \n{}\n",
                    apiConfig.getMetricId(), accountConfig.getAccountValue(), formattedFinalDsl);*/

            // 4. 执行DSL查询
            String indices = apiConfig.getApiPath(); // 对于DSL查询，apiPath字段存储的是索引列表
            JSONObject response = elasticsearchService.executeDslQuery(indices, dsl);

            // 5. 设置结果
            apiAccessResultData.setApiPath(indices);
            apiAccessResultData.setParams(dsl);
            apiAccessResultData.setResponseData(response.toJSONString());
            apiAccessResultData.setAccount(accountConfig.getAccountValue());
            apiAccessResultData.setDynamic(accountConfig.getSampleStrategy() == BusinessAccountConfig.SampleStrategy.DYNAMIC);
            apiAccessResultData.setSystemName(apiConfig.getSystemName());
            apiAccessResultData.setCostTime(Math.toIntExact(System.currentTimeMillis() - startTime));
            apiAccessResultData.setMetricId(apiConfig.getMetricId().toString());
            apiAccessResultData.setStatDate(LocalDate.parse(timeRange[1]));
            apiAccessResultData.setRequestTime(LocalDateTime.now());
            apiAccessResultData.setIsSuccess(true);
            apiAccessResultData.setResponseStatus((short) 200);

            // 6. 将结果添加到结果Map
            resultMap.put(dsl, response);
            // TODO 结果数量删掉
            //log.info("[DSL查询] 指标: {} | 账号: {} | 索引: {} | 耗时: {}ms ", apiConfig.getMetricId(), accountConfig.getAccountValue(), indices, apiAccessResultData.getCostTime());

            // 应用请求延迟，降低系统并发压力
            applyRequestDelay();
        } catch (Exception e) {
            // 确保账号不为null
            if (apiAccessResultData.getAccount() == null) {
                apiAccessResultData.setAccount(accountConfig.getAccountValue());
            }

            // 确保耗时不为null
            if (apiAccessResultData.getCostTime() == null) {
                apiAccessResultData.setCostTime(0);
            }

            log.error("[DSL查询错误] 指标: {} | 账号: {} | 索引: {} | 错误: {}", apiConfig.getMetricId(), accountConfig.getAccountValue(), apiConfig.getApiPath(), e.getMessage(), e);
            apiAccessResultData.setIsSuccess(false);
            apiAccessResultData.setErrorCode("500");
            apiAccessResultData.setResponseData(e.getMessage());
        }
    }

    private void getWholeUrl(StringBuilder url, Map<String, Object> getArchiveIdParams) throws UnsupportedEncodingException {
        //最后一个参数不加&
        //这里要考虑#这种路径上的特殊字符，不能放到请求的URL后
        for (Map.Entry<String, Object> entry : getArchiveIdParams.entrySet()) {
            url.append(entry.getKey()).append("=").append(URLEncoder.encode(entry.getValue().toString(), String.valueOf(StandardCharsets.UTF_8))).append("&");
        }
        url.deleteCharAt(url.length() - 1);
    }
    //结果解析入口
    private void processCaseSystem(JSONObject apiResponse, BusinessAccountConfig accountConfig, JSONObject responseMapping, AdsOpsOpsApiAccessResult apiAccessResultData) throws Exception {
        parseApiResponse(apiResponse, responseMapping, apiAccessResultData);
        log.info("[CASE系统] 指标: {} | 账号: {} | API: {} | 采集完成 | 案件数: {}", apiAccessResultData.getMetricId(), accountConfig.getAccountValue(), apiAccessResultData.getApiPath(), apiAccessResultData.getStatCount());
    }

    private void processSearchSystem(JSONObject apiResponse, BusinessAccountConfig accountConfig, JSONObject responseMapping, AdsOpsOpsApiAccessResult apiAccessResultData) throws Exception {
        parseApiResponse(apiResponse, responseMapping, apiAccessResultData);
        log.info("[SEARCH系统] 指标: {} | 账号: {} | API: {} | 采集完成 | 搜索查询数: {}", apiAccessResultData.getMetricId(), accountConfig.getAccountValue(), apiAccessResultData.getApiPath(), apiAccessResultData.getStatCount());
    }

    private void processArchiveSystem(JSONObject apiResponse, BusinessAccountConfig accountConfig, JSONObject responseMapping, AdsOpsOpsApiAccessResult apiAccessResultData) throws Exception {
        parseApiResponse(apiResponse, responseMapping, apiAccessResultData);
        log.info("[ARCHIVE系统] 指标: {} | 账号: {} | API: {} | 采集完成 | 全息查询数: {}", apiAccessResultData.getMetricId(), accountConfig.getAccountValue(), apiAccessResultData.getApiPath(), apiAccessResultData.getStatCount());
    }

    /**
     * 根据实际API响应格式进行解析
     *
     * @param apiResponseJSONObject API响应JSON对象
     * @param responseMapping 响应映射配置
     * @param apiAccessResultData API访问结果数据对象
     */
    private void parseApiResponse(JSONObject apiResponseJSONObject, JSONObject responseMapping, AdsOpsOpsApiAccessResult apiAccessResultData) {
        // 1. 处理responseMapping中的特殊变量替换
        // 1.1 替换excludeKey中的#{account}为实际账号值
        processExcludeKeyArray(responseMapping, apiAccessResultData);
        //1.2 替换excludeValue中的#{account}为实际账号值
        processExcludeValueArray(responseMapping, apiAccessResultData);
        // TODO 1.3 将来可能有 替换conditionKeyValue中的#{account}为实际账号值 需求,预留位置
        // 1.4 替换countKey中的#{account}为实际账号值
        processCountKeyArray(responseMapping, apiAccessResultData);
        // 1.5 替换ObjectExcludeKey中的#{account}为实际账号值
        processObjectExcludeKeyArray(responseMapping, apiAccessResultData);
        // 1.6 替换ObjectCountKey中的#{account}为实际账号值
        processObjectCountKeyArray(responseMapping, apiAccessResultData);


        // 2. 查找数据类型键（改成direct开头）typekey，即说明是直接取值，还是JSON对象，还是JSON数组
        String typeKey = findTypeKey(responseMapping);
        //TODO 直接在业务代码里抛异常,不优雅,待修改
        if (typeKey == null) {
            throw new RuntimeException("没有找到direct开头的key,无法找到对应解析方式,请排查response_mapping字段的配置!");
        }

        // 3. 获取解析类型值
        // 三种情况 0: 直接取值, 1: JSON数组, 2: JSON对象
        Integer typeValue = responseMapping.getInteger("direct." + typeKey);

        // 4. 使用策略模式处理不同类型的数据
        try {
            // 根据类型值获取相应的解析策略
            ResponseParseStrategy strategy = ResponseParseStrategyFactory.getStrategy(typeValue);
            log.info("[指标]:{}, responseMapping:{}", apiAccessResultData.getMetricId(), responseMapping.toJSONString());
            // 执行解析策略
            strategy.parseResponse(apiResponseJSONObject, responseMapping, typeKey, apiAccessResultData);
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("未知的解析类型: " + typeValue, e);
        }
    }

    private void processObjectCountKeyArray(JSONObject responseMapping, AdsOpsOpsApiAccessResult apiAccessResultData) {
        //如果没有getKey,jsonArray就是null
        JSONArray countKeyArr = responseMapping.getJSONArray("objectCountKey");
        if (countKeyArr == null || countKeyArr.isEmpty()) {
            return;
        }
        //getKeyArr例如:["#{account}","","aaa123"]
        for (int i = 0; i < countKeyArr.size(); i++) {
            String getKey = countKeyArr.getString(i);
            if ("#{account}".equals(getKey)) {
                String value = apiAccessResultData.getAccount();
                //把#{account}替换成真实的账号
                countKeyArr.set(i, value);
                //把替换后的jsonArray重新设置回responseMapping
                responseMapping.put("objectCountKey", countKeyArr);
            }
        }
    }

    private void processObjectExcludeKeyArray(JSONObject responseMapping, AdsOpsOpsApiAccessResult apiAccessResultData) {
        //如果没有getKey,jsonArray就是null
        JSONArray objectExcludeKeyArr = responseMapping.getJSONArray("ObjectExcludeKey");
        if (objectExcludeKeyArr == null || objectExcludeKeyArr.isEmpty()) {
            return;
        }
        //getKeyArr例如:["#{account}","","aaa123"]
        for (int i = 0; i < objectExcludeKeyArr.size(); i++) {
            String getKey = objectExcludeKeyArr.getString(i);
            if ("#{account}".equals(getKey)) {
                String value = apiAccessResultData.getAccount();
                //把#{account}替换成真实的账号
                objectExcludeKeyArr.set(i, value);
                //把替换后的jsonArray重新设置回responseMapping
                responseMapping.put("ObjectExcludeKey", objectExcludeKeyArr);
            }
        }
    }

    /**
     * 处理排除值数组
     * 该方法用于遍历响应映射中的"excludeValue"数组，并替换其中的特定值
     * 主要目的是替换数组中的占位符（如"#{account}"）为实际的API访问结果数据中的值
     *
     * @param responseMapping 响应映射对象，包含需要处理的"excludeValue"数组
     * @param apiAccessResultData API访问结果数据对象，提供替换占位符所需的实际值
     */
    private void processExcludeValueArray(JSONObject responseMapping, AdsOpsOpsApiAccessResult apiAccessResultData) {
        JSONArray excludeValueArr = responseMapping.getJSONArray("excludeValue");
        if (excludeValueArr == null || excludeValueArr.isEmpty()) {
            return;
        }
        //excludeValueArr 例如: ["#{account}","","123"]
        for (int i = 0; i < excludeValueArr.size(); i++) {
            String excludeValue = excludeValueArr.getString(i);
            if ("#{account}".equals(excludeValue)) {
                excludeValueArr.set(i, apiAccessResultData.getAccount());
                responseMapping.put("excludeValue", excludeValueArr);
            }
        }
    }

    // 公共方法提取部分
    /**
     * 查找数据类型键
     * 优先查找以"direct."开头的键，如果找到则截取"direct."之后的部分返回
     * 如果没有找到以"direct."开头的键，则查找以"data"、"hits"或"aggregations"开头的键
     *
     * @param responseMapping 响应映射配置
     * @return 找到的数据类型键，如果没有找到则返回null
     */
    /**
     * 预处理响应映射，为以data、hits或aggregations开头的键添加direct.前缀
     *
     * @param responseMapping 响应映射配置
     */
    private void preprocessResponseMapping(JSONObject responseMapping) {
        // 创建一个新的Map来存储需要添加的键值对
        Map<String, Object> keysToAdd = new HashMap<>();
        // 创建一个列表来存储需要移除的键
        List<String> keysToRemove = new ArrayList<>();

        // 遍历responseMapping中的所有键
        for (String key : responseMapping.keySet()) {
            // 检查是否有以data、hits或aggregations开头的键
            if (key.startsWith("data") || key.startsWith("hits") || key.startsWith("aggregations")) {
                // 保存原始键的值
                Object value = responseMapping.get(key);
                // 创建新键，添加direct.前缀
                String newKey = "direct." + key;
                // 将原始键的值赋给新键
                keysToAdd.put(newKey, value);
                // 将原始键添加到待移除列表
                keysToRemove.add(key);

                log.debug("预处理: 为键 '{}' 添加direct.前缀，新键为 '{}'", key, newKey);
            }
        }

        // 添加新键到responseMapping
        for (Map.Entry<String, Object> entry : keysToAdd.entrySet()) {
            responseMapping.put(entry.getKey(), entry.getValue());
        }

        // 从responseMapping中移除原始键
        for (String key : keysToRemove) {
            responseMapping.remove(key);
            log.debug("预处理: 移除原始键 '{}'", key);
        }
    }

    /**
     * 查找数据类型键
     * 优先查找以"direct."开头的键，如果找到则截取"direct."之后的部分返回
     * 如果没有找到以"direct."开头的键，则查找以"data"、"hits"或"aggregations"开头的键
     *
     * @param responseMapping 响应映射配置
     * @return 找到的数据类型键，如果没有找到则返回null
     */
    private String findTypeKey(JSONObject responseMapping) {
        // TODO 暂且如此,将来删除 预处理responseMapping，为以data、hits或aggregations开头的键添加direct.前缀
        //preprocessResponseMapping(responseMapping);

        // 1. 优先查找以"direct."开头的键
        for (String key : responseMapping.keySet()) {
            if (key.startsWith("direct.")) {
                // 截取"direct."之后的部分
                String actualKey = key.substring("direct.".length());
                //log.info("找到direct开头的键: {}, 截取后的键: {}", key, actualKey);
                return actualKey;
            }
        }

        // 2. 如果没有找到以"direct."开头的键，则查找以"data"、"hits"或"aggregations"开头的键,先保留老版本的兼容
        /*for (String key : responseMapping.keySet()) {
            if (key.startsWith("data") || key.startsWith("hits") || key.startsWith("aggregations")) {
                log.debug("找到标准数据键: {}", key);
                return key;
            }
        }*/

        // 3. 如果都没找到，记录错误日志并返回null
        log.error("没有找到direct.开头、data开头、hits开头或aggregations开头的key，请排查response_mapping字段的配置!");
        return null;
    }







    // 公共工具方法
    private Object getValueByPath(JSONObject source, String[] pathSegments) {
        //source是接口响应结果JSON
        Object currentValue = source;
        //循环get出最终值，pathSegments示例值["data","total"]
        for (String segment : pathSegments) {
            if (!(currentValue instanceof JSONObject)) {
                log.warn("返回值JSON无法匹配结果解析格式");
                return null;
            }
            currentValue = ((JSONObject) currentValue).get(segment);
        }
        //取出最终value
        return currentValue;
    }

    private JSONArray safeCastToJsonArray(Object obj) {
        if (!(obj instanceof JSONArray)) {
            log.warn("类型转换失败，不是JSON数组");
            return null;
        }
        return (JSONArray) obj;
    }

    private JSONObject safeCastToJsonObject(Object obj) {
        if (!(obj instanceof JSONObject)) {
            log.warn("类型转换失败，不是JSON对象");
            return null;
        }
        return (JSONObject) obj;
    }

    private void setDefaultResult(AdsOpsOpsApiAccessResult resultData) {
        log.warn("返回值JSON无法匹配结果解析格式,设置默认值为0");
        resultData.setStatCount(0);
        resultData.setKvContent("");
    }

    private List<Map<String, Object>> parseConditions(JSONObject responseMapping) {
        List<Map<String, Object>> conditions = new ArrayList<>();
        if (responseMapping.containsKey("conditionKey")) {
            JSONArray conditionArray = responseMapping.getJSONArray("conditionKey");
            for (int i = 0; i < conditionArray.size(); i++) {
                JSONObject conditionObj = conditionArray.getJSONObject(i);
                if (conditionObj != null && !conditionObj.isEmpty()) {
                    String key = conditionObj.keySet().iterator().next();
                    Map<String, Object> condition = new HashMap<>();
                    condition.put("key", key);
                    condition.put("value", conditionObj.get(key));
                    conditions.add(condition);
                }
            }
        }
        return conditions;
    }

    private String[] parseGetKey(JSONObject responseMapping) {
        if (!responseMapping.containsKey("getKey")) {
            return new String[0];
        }
        String rawKey = responseMapping.getString("getKey").replace(" ", "");
        return "[]".equals(rawKey) ? new String[0] : rawKey.replaceAll("\\[|\\]|\"", "").split(",");
    }

    // 数据计算相关方法
    private int calculateArraySum(JSONArray dataArray, List<Map<String, Object>> conditions, String[] targetKeys) {
        return calculateArraySum(dataArray, conditions, targetKeys, null);
    }

    /**
     * 计算JSON数组中符合条件的对象的数值总和（排除指定的键）
     *
     * @param dataArray   要计算的JSON数组
     * @param conditions  过滤条件列表
     * @param targetKeys  要计算的目标键数组，为空时计算所有数值类型字段
     * @param excludeKeys 要排除的键数组，可为null
     * @return 计算得到的总和
     */
    private int calculateArraySum(JSONArray dataArray, List<Map<String, Object>> conditions, String[] targetKeys, JSONArray excludeKeys) {
        //初始化0值
        int sum = 0;
        for (int i = 0; i < dataArray.size(); i++) {
            //逐个取出JSON对象
            JSONObject item = dataArray.getJSONObject(i);
            //判断该item这个JSON对象是否满足条件,如果满足条件就参与累加
            if (isItemValid(item, conditions)) {
                //参与累加前还需判断是否只添加targetKey的value，并排除excludeKeys中的键
                sum += sumItemValues(item, targetKeys, excludeKeys);
            }
        }
        return sum;
    }

    private boolean isItemValid(JSONObject item, List<Map<String, Object>> conditions) {
        //假设不满足条件
        boolean flag = false;
        //遍历conditions中的各个条件,看是否有一个条件满足
        for (Map<String, Object> condition : conditions) {
            String key = (String) condition.get("key");
            String expectedValue = (String) condition.get("value");
            //如果JSON中不包含该key 或者 该key的值不等于expectedValue，则跳过,看下一个条件是否满足
            if (!item.containsKey(key) || !item.getString(key).equals(expectedValue)) {
                continue;
            }
            //运行到这,说明当前条件满足,把flag设置为true
            flag = true;
        }
        if (conditions.isEmpty()) {
            flag = true;
        }
        //如果所有条件都满足，或者根本没有配置条件，则返回true
        return flag;
    }

    private int sumItemValues(JSONObject item, String[] targetKeys) {
        return sumItemValues(item, targetKeys, null);
    }

    /**
     * 计算JSON对象中指定键的数值总和（排除指定的键）
     *
     * @param item        要计算的JSON对象
     * @param targetKeys  要计算的目标键数组，为空时计算所有数值类型字段
     * @param excludeKeys 要排除的键数组，可为null
     * @return 计算得到的总和
     */
    private int sumItemValues(JSONObject item, String[] targetKeys, JSONArray excludeKeys) {
        int sum = 0;
        // 如果没有targetKeys，说明需要把该JSON对象中所有的数值类型的key的value都累加
        if (targetKeys.length == 0) {
            for (String key : item.keySet()) {
                // 检查当前键是否应该被排除
                if (shouldExcludeKey(key, excludeKeys)) {
                    //如果JSON对象中某key,匹配上了任意一个excludeKey,则直接返回0
                    return 0;
                }
            }

            if (excludeKeys.toJavaList(String.class).contains("")) {
                for (String key : item.keySet()) {
                    String valueStr = item.getString(key);
                    for (int i = 0; i < excludeKeys.size(); i++) {
                        if (valueStr.equalsIgnoreCase(excludeKeys.getString(i))) {
                            return 0;
                        }
                    }
                }
            }

            //通过排除,做累加
            for (String key : item.keySet()) {
                // 判断是否是数值，数值才做累加，排除字符串之类的
                Object value = item.get(key);
                if (value instanceof Number) {
                    sum += ((Number) value).intValue();
                }
            }

        } else {
            // 否则遍历每个targetKey，找到忽略大小写匹配的key
            for (String targetKey : targetKeys) {
                // 检查当前键是否应该被排除
                if (shouldExcludeKey(targetKey, excludeKeys)) {
                    continue;
                }

                // 遍历item所有key，寻找忽略大小写匹配的key
                for (String itemKey : item.keySet()) {
                    // 如果找到忽略大小写匹配的key,忽略大小写
                    if (itemKey.equalsIgnoreCase(targetKey.trim())) {
                        Object value = item.get(itemKey);
                        // 检查value是否为数值类型
                        if (value instanceof Number) {
                            sum += ((Number) value).intValue();
                        }
                        break; // 找到第一个匹配的key后退出循环（JSON key唯一）
                    }
                }
            }
        }
        return sum;
    }

    // JSON构建相关方法
    private void buildJsonArrayContent(JSONArray dataArray, List<Map<String, Object>> conditions, String[] targetKeys, AdsOpsOpsApiAccessResult resultData, JSONObject responseMapping) {
        //构建空的JSON数组，通过过滤条件的JSON对象将被放入该list
        List<JSONObject> filteredItems = new ArrayList<>();
        for (int i = 0; i < dataArray.size(); i++) {
            //取出每一个对象，判断是否满足条件，满足条件则加入
            JSONObject item = dataArray.getJSONObject(i);
            if (isItemValid(item, conditions)) {
                filteredItems.add(item);
            }
        }
        //拿已经经过conditionKey过滤出来的JSON对象, 构建JSON字符串
        String jsonContent = buildJsonArrayString(filteredItems, targetKeys, responseMapping);
        resultData.setKvContent(jsonContent);
        //打印日志
        logFormattedJson(jsonContent);
    }

    private String buildJsonArrayString(List<JSONObject> items, String[] targetKeys, JSONObject responseMapping) {
        //StringBuilder做字符串拼接
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < items.size(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            //根据targetKeys来构建JSON单个JSON对象
            sb.append(buildItemString(items.get(i), targetKeys, responseMapping));
        }
        sb.append("]");
        //返回拼接后的完整字符串
        return sb.toString();
    }

    /**
     * 构建JSON对象字符串
     *
     * @param item JSON对象
     * @param targetKeys 目标键数组
     * @param responseMapping 响应映射配置
     * @return 构建JSON对象字符串
     */
    private String buildItemString(JSONObject item, String[] targetKeys, JSONObject responseMapping) {
        StringBuilder sb = new StringBuilder("{");

        // 根据targetKeys是否为空选择不同的处理方式
        if (targetKeys.length == 0) {
            // 添加所有键值对
            appendAllKeys(item, sb, responseMapping);
        } else {
            // 只添加指定键值对
            appendSpecificKeys(item, targetKeys, sb, responseMapping);
        }

        sb.append("}");
        return sb.toString();
    }

    /**
     * 添加所有键值对（排除excludeKey中的键）
     *
     * @param item JSON对象
     * @param sb 字符串构建器
     * @param responseMapping 响应映射配置
     */
    private void appendAllKeys(JSONObject item, StringBuilder sb, JSONObject responseMapping) {
        // 获取excludeKey数组
        JSONArray excludeKeyJsonArr = responseMapping.getJSONArray("excludeKey");
        int count = 0;

        // 遍历JSON对象的所有键
        for (String key : item.keySet()) {
            // 检查当前键是否应该被排除
            if (excludeKeyJsonArr != null) {
                AtomicBoolean ignore = new AtomicBoolean(false);
                excludeKeyJsonArr.forEach(excludeObj -> {
                    String excludeStr = excludeObj != null ? excludeObj.toString() : "";
                    if (!"".equals(excludeStr) && key.equalsIgnoreCase(excludeStr)) {
                        ignore.set(true);
                    }
                });
                if (ignore.get()) {
                    continue;
                }
            }

            // 添加逗号分隔符（除了第一个键值对）
            if (count++ > 0) {
                sb.append(",");
            }

            // 添加键值对（key转小写）
            sb.append(String.format("\"%s\":\"%s\"", key.toLowerCase(), item.get(key)));
        }
    }

    //暂时遗弃
    @Deprecated
    private void appendSpecificKeys2(JSONObject item, String[] targetKeys, StringBuilder sb, JSONObject responseMapping) {
        //获取excludeKey数组
        JSONArray excludeKeyJsonArr = responseMapping.getJSONArray("excludeKey") == null ? null : responseMapping.getJSONArray("excludeKey");
        int count = 0;
        //遍历targetKey数组，如果targetKey数组中包含该key,那么将要进入拼接
        for (String key : targetKeys) {
            //判断item中是否包含该key
            if (item.containsKey(key.trim())) {
                //如果excludeKey不为空,就把该key转小写,跟excludeKey比较,如果相等,就跳过该key,不做相加
                if (excludeKeyJsonArr != null) {
                    //假设不需要跳过
                    AtomicBoolean ignore = new AtomicBoolean(false);
                    excludeKeyJsonArr.forEach(excludeObj -> {
                        String excludeStr = excludeObj != null ? excludeObj.toString() : "";
                        if (!"".equals(excludeStr) && key.equalsIgnoreCase(excludeStr)) {
                            ignore.set(true);
                        }
                    });
                    if (ignore.get()) {
                        continue;
                    }
                }

                if (count++ > 0) {
                    sb.append(",");
                }
                //key转小写，拼接key和value
                sb.append(String.format("\"%s\":\"%s\"", key.toLowerCase(), item.get(key)));
            }
        }
    }

    /**
     * 添加指定键值对（排除excludeKey中的键）
     *
     * @param item JSON对象
     * @param targetKeys 目标键数组
     * @param sb 字符串构建器
     * @param responseMapping 响应映射配置
     */
    private void appendSpecificKeys(JSONObject item, String[] targetKeys, StringBuilder sb, JSONObject responseMapping) {
        // 获取excludeKey数组
        JSONArray excludeKeyJsonArr = responseMapping.getJSONArray("excludeKey");
        int count = 0;

        // 遍历目标键数组
        for (String key : targetKeys) {
            // 查找实际存在的键（忽略大小写）
            String actualKey = null;
            String trimmedKey = key.trim();

            // 遍历所有键进行忽略大小写匹配
            for (String itemKey : item.keySet()) {
                if (itemKey.equalsIgnoreCase(trimmedKey)) {
                    actualKey = itemKey;
                    break;
                }
            }

            // 如果找到了匹配的键
            if (actualKey != null) {
                // 检查当前键是否应该被排除
                if (excludeKeyJsonArr != null) {
                    AtomicBoolean ignore = new AtomicBoolean(false);
                    excludeKeyJsonArr.forEach(excludeObj -> {
                        String excludeStr = excludeObj != null ? excludeObj.toString() : "";
                        if (!"".equals(excludeStr) && key.equalsIgnoreCase(excludeStr)) {
                            ignore.set(true);
                        }
                    });
                    if (ignore.get()) {
                        continue;
                    }
                }

                // 添加逗号分隔符（除了第一个键值对）
                if (count++ > 0) {
                    sb.append(",");
                }

                // 添加键值对（key转小写，使用实际匹配的key获取value）
                sb.append(String.format("\"%s\":\"%s\"", key.toLowerCase(), item.get(actualKey)));
            }
        }
    }

    private void logFormattedJson(String jsonString) {
        JSONArray jsonArray = JSON.parseArray(jsonString);
        if (log.isDebugEnabled()) {
            log.debug("[KV内容] JSON数组: \n{}", JSON.toJSONString(jsonArray, true));
        }
    }

    // 对象类型数据处理方法
    private boolean validateConditions(JSONObject targetObject, List<Map<String, Object>> conditions) {
        for (Map<String, Object> condition : conditions) {
            String key = (String) condition.get("key");
            Object expectedValue = condition.get("value");
            if (!targetObject.containsKey(key) || !targetObject.get(key).equals(expectedValue)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 计算JSON对象中特定键的数值总和
     *
     * @param targetObject    要计算的JSON对象
     * @param targetKeys      要计算的目标键数组，为空时计算所有数值类型字段
     * @param responseMapping 响应映射配置，包含excludeKey等信息
     * @return 计算得到的总和
     */
    private int calculateObjectSum(JSONObject targetObject, String[] targetKeys, JSONObject responseMapping) {
        // 获取需要排除的键数组
        JSONArray excludeKeys = responseMapping.getJSONArray("excludeKey");
        int total = 0;

        // 根据targetKeys是否为空，采用不同的计算策略
        if (targetKeys.length == 0) {
            // 计算所有数值类型字段的总和（排除excludeKeys中的字段）
            total = calculateAllNumericValues(targetObject, excludeKeys);
        } else {
            // 只计算指定键的数值总和（排除excludeKeys中的字段）
            total = calculateSpecificNumericValues(targetObject, targetKeys, excludeKeys);
        }

        return total;
    }

    /**
     * 计算JSON对象中所有数值类型字段的总和（排除指定的键）
     *
     * @param jsonObject  要计算的JSON对象
     * @param excludeKeys 要排除的键数组，可为null
     * @return 计算得到的总和
     */
    private int calculateAllNumericValues(JSONObject jsonObject, JSONArray excludeKeys) {
        int sum = 0;
        for (String key : jsonObject.keySet()) {
            // 检查当前键是否应该被排除
            if (shouldExcludeKey(key, excludeKeys)) {
                continue;
            }

            // 获取值并检查是否为数值类型
            Object value = jsonObject.get(key);
            if (value instanceof Number) {
                sum += ((Number) value).intValue();
            }
        }

        return sum;
    }

    /**
     * 计算JSON对象中指定键的数值总和（排除指定的键）
     *
     * @param jsonObject  要计算的JSON对象
     * @param targetKeys  要计算的目标键数组
     * @param excludeKeys 要排除的键数组，可为null
     * @return 计算得到的总和
     */
    private int calculateSpecificNumericValues(JSONObject jsonObject, String[] targetKeys, JSONArray excludeKeys) {
        int sum = 0;

        for (String targetKey : targetKeys) {
            // 忽略大小写查找实际存在的键
            String actualKey = findKeyIgnoreCase(jsonObject, targetKey.trim());

            // 如果找到了键且不应该被排除，并且值是数值类型，则累加到总和中
            if (actualKey != null && !shouldExcludeKey(targetKey, excludeKeys)) {
                Object value = jsonObject.get(actualKey);
                if (value instanceof Number) {
                    sum += ((Number) value).intValue();
                }
            }
        }

        return sum;
    }

    /**
     * 在JSON对象中查找忽略大小写匹配的键
     *
     * @param jsonObject 要查找的JSON对象
     * @param targetKey  目标键名
     * @return 找到的实际键名，未找到返回null
     */
    private String findKeyIgnoreCase(JSONObject jsonObject, String targetKey) {
        for (String key : jsonObject.keySet()) {
            if (key.equalsIgnoreCase(targetKey)) {
                return key;
            }
        }
        return null;
    }

    /**
     * 判断键是否应该被排除
     *
     * @param key         要检查的键
     * @param excludeKeys 排除键数组，可为null
     * @return 如果应该排除返回true，否则返回false
     */
    private boolean shouldExcludeKey(String key, JSONArray excludeKeys) {
        // 如果排除键数组为空，则不排除任何键
        if (excludeKeys == null || excludeKeys.isEmpty()) {
            return false;
        }

        // 遍历排除键数组，忽略大小写比较
        for (int i = 0; i < excludeKeys.size(); i++) {
            Object excludeObj = excludeKeys.get(i);
            String excludeStr = excludeObj != null ? excludeObj.toString() : "";

            // 如果找到匹配的键，立即返回true
            if (key.equalsIgnoreCase(excludeStr)) {
                return true;
            }
        }

        // 没有找到匹配的键，返回false
        return false;
    }

    /**
     * 构建JSON对象内容
     *
     * @param targetObject JSON对象
     * @param targetKeys 目标键数组
     * @param resultData 结果数据对象
     * @param responseMapping 响应映射配置
     */
    private void buildJsonObjectContent(JSONObject targetObject, String[] targetKeys, AdsOpsOpsApiAccessResult resultData, JSONObject responseMapping) {
        // 构建JSON字符串
        String jsonContent = buildJsonObjectString(targetObject, targetKeys, responseMapping);

        // 设置kv_content
        resultData.setKvContent(jsonContent);

        // 记录日志
        logFormattedJsonObject(jsonContent);
    }

    /**
     * 构建JSON对象字符串
     *
     * @param obj JSON对象
     * @param targetKeys 目标键数组
     * @param responseMapping 响应映射配置
     * @return 构建JSON对象字符串
     */
    private String buildJsonObjectString(JSONObject obj, String[] targetKeys, JSONObject responseMapping) {
        StringBuilder sb = new StringBuilder("{");

        // 根据targetKeys是否为空选择不同的处理方式
        if (targetKeys.length == 0) {
            // 添加所有键值对（排除excludeKey中的键）
            appendAllKeys(obj, sb, responseMapping);
        } else {
            // 只添加指定键值对（排除excludeKey中的键）
            appendSpecificKeys(obj, targetKeys, sb, responseMapping);
        }

        sb.append("}");
        return sb.toString();
    }

    private void logFormattedJsonObject(String jsonString) {
        JSONObject jsonObject = JSON.parseObject(jsonString);
        if (log.isDebugEnabled()) {
            log.debug("[KV内容] JSON对象: \n{}", JSON.toJSONString(jsonObject, true));
        }
    }

    /**
     * 尝试格式化JSON字符串，如果是有效的JSON则返回格式化后的字符串，否则返回原始字符串
     *
     * @param jsonStr 要格式化的JSON字符串
     * @return 格式化后的字符串
     */
    private String formatJsonIfPossible(String jsonStr) {
        try {
            // 尝试解析JSON
            Object jsonObj = JSON.parse(jsonStr);
            // 如果解析成功，返回格式化后的字符串
            return JSON.toJSONString(jsonObj, true);
        } catch (Exception e) {
            // 如果解析失败，返回原始字符串
            return jsonStr;
        }
    }


    /**
     * 获取认证Token，包含缓存逻辑
     *
     * @param businessSysConfig 业务系统配置
     * @return Token缓存条目
     */
    private TokenCacheEntry getAuthToken(BusinessSysConfig businessSysConfig) {
        JSONObject authConfig = JSONObject.parseObject(businessSysConfig.getAuthConfig());
        String authAccount = authConfig.getString("account");

        // 检查缓存有效性,使用静态桩
        TokenCacheEntry cacheEntry = StaticMethodRecorderProxy.record(isUseMock, RedisTemplate.class, "opsForValue", params -> redisTemplate.opsForValue().get((String) params[0]), authAccount);

        if (cacheEntry != null) {
            // 如果命中了缓存，给缓存进行10分钟的续期操作，使得token的有效期又变为10分钟
            redisTemplate.opsForValue().set(authAccount, cacheEntry, 10, java.util.concurrent.TimeUnit.MINUTES);
            log.debug("[认证] Redis缓存命中有效token | 系统: {} | 账号: {}", businessSysConfig.getSysName(), authAccount);
            return cacheEntry;
        }

        // 缓存未命中，需要重新获取token
        // TODO 多实例情况下，该代码有并发问题
        // 多个实例可以获取各自的synchronized锁，去获取门户的token，会导致Redis中token覆盖，导致某些实例无法调用搜索、全息、案件子系统
        synchronized (this) {
            // 双重检查，避免多线程问题
            cacheEntry = StaticMethodRecorderProxy.record(isUseMock, RedisTemplate.class, "opsForValue", params -> redisTemplate.opsForValue().get((String) params[0]), authAccount);

            if (cacheEntry == null) {
                Map<String, Object> authResult = performPortalAuth(authConfig.getString("auth_url"), authAccount, authConfig.getString("password"));

                // 更新Redis缓存（有效期10分钟） 注意token会自动续期，故实际不会过期
                cacheEntry = new TokenCacheEntry(authResult.get("token").toString(), System.currentTimeMillis() + 10 * 60 * 1000);

                redisTemplate.opsForValue().set(authAccount, cacheEntry, 10, java.util.concurrent.TimeUnit.MINUTES);
                log.info("[认证] 生成新token并缓存 | 系统: {} | 账号: {}", businessSysConfig.getSysName(), authAccount);
            }
        }

        return cacheEntry;
    }

    protected HttpRequest createHttpRequestPost(String url) {
        //进行静态打桩
        return StaticMethodRecorderProxy.record(isUseMock, cn.hutool.http.HttpUtil.class, "createPost", params -> cn.hutool.http.HttpUtil.createPost((String) params[0]), url);
    }

    /**
     * 应用请求延迟，控制请求频率，降低系统压力
     */
    private void applyRequestDelay() {
        if (requestDelay > 0) {
            try {
                Thread.sleep(requestDelay);
                log.debug("应用请求延迟: {}ms", requestDelay);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("请求延迟被中断", e);
            }
        }
    }

    // 新增缓存条目内部类
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class TokenCacheEntry implements Serializable {
        private String token;
        private long expireTime;
    }
}

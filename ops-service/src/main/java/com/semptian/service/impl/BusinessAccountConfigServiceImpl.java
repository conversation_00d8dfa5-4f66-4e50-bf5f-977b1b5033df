package com.semptian.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.annotation.OperateMock;
import com.semptian.entity.BusinessAccountConfig;
import com.semptian.entity.BusinessMetricConfig;
import com.semptian.mapper.BusinessAccountConfigMapper;
import com.semptian.service.BusinessAccountConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
@Transactional
public class BusinessAccountConfigServiceImpl extends ServiceImpl<BusinessAccountConfigMapper, BusinessAccountConfig> implements BusinessAccountConfigService {

    @Override
    public Page<BusinessAccountConfig> getAccountConfigList(int pageNum, int pageSize, Integer sysId, String accountType, Boolean enable) {
        Page<BusinessAccountConfig> page = new Page<>(pageNum, pageSize);
        QueryWrapper<BusinessAccountConfig> wrapper = new QueryWrapper<>();
        if (sysId != null) wrapper.eq("sys_id", sysId);
        if (accountType != null) wrapper.eq("account_type", accountType);
        if (enable != null) wrapper.eq("enable", enable);
        return baseMapper.selectPage(page, wrapper);
    }

    //根据metricId获取需要采样的账号，最大不超过maxSampleSize值。根据权重获进行排序，优先取权重较高的账号
    @OperateMock
    @Override
    public List<BusinessAccountConfig> getAccountConfigByMetricId(BusinessMetricConfig businessMetricConfig, Integer maxSampleSize, String collectTimeRange) {
        //根据metricId对应的metricModelName，获取对应的采样配置 Email数据差异分析 对应AccountType.EMAIL  号码数据差异分析 对应AccountType.PHONE   Fixed RADIUS数据差异分析 对应AccountType.RADIUS  IM账号数据差异分析 对应AccountType.IM
        QueryWrapper<BusinessAccountConfig> wrapper = new QueryWrapper<>();
        switch (businessMetricConfig.getMetricModelName()) {
            case "Email数据差异分析":
                wrapper.eq("account_type", BusinessAccountConfig.AccountType.EMAIL);
                break;
            case "号码数据差异分析":
                wrapper.eq("account_type", BusinessAccountConfig.AccountType.PHONE);
                break;
            case "Fixed RADIUS数据差异分析":
                wrapper.eq("account_type", BusinessAccountConfig.AccountType.RADIUS);
                break;
            case "IM账号数据差异分析":
                wrapper.eq("account_type", BusinessAccountConfig.AccountType.IM);
                break;
        }
        //根据collectTimeRange对采样账号进行过滤，取collectTimeRange+1Day时间范围内的账号（格式为2025-03-01,2025-03-07），对比BusinessAccountConfig的createTime字段，注意createTime字段为13位时间戳
        String[] timeRange = collectTimeRange.split(",");
        if (timeRange.length == 2) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Date startDate = sdf.parse(timeRange[0]);
                Date endDate = sdf.parse(timeRange[1]);
                // 加一天
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(endDate);
                calendar.add(Calendar.DAY_OF_YEAR, 1);
                endDate = calendar.getTime();

                long startTime = startDate.getTime() + 24 * 60 * 60 * 1000;
                long endTime = endDate.getTime() + 24 * 60 * 60 * 1000;

                wrapper.between("create_time", startTime, endTime);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
        wrapper.eq("metric_id", businessMetricConfig.getId());
        wrapper.eq("enable", true);
        wrapper.orderByDesc("sample_weight");
        wrapper.last("limit " + maxSampleSize);
        return baseMapper.selectList(wrapper);
    }

    @OperateMock
    @Override
    public List<BusinessAccountConfig> getAccountConfigByMetricIdWithStaticAccount(BusinessMetricConfig businessMetricConfig, Integer maxSampleSize, String collectTimeRange) {
        QueryWrapper<BusinessAccountConfig> wrapper = new QueryWrapper<>();
        switch (businessMetricConfig.getMetricModelName()) {
            case "Email数据差异分析":
                wrapper.eq("account_type", BusinessAccountConfig.AccountType.EMAIL);
                break;
            case "号码数据差异分析":
                wrapper.eq("account_type", BusinessAccountConfig.AccountType.PHONE);
                break;
            case "Fixed RADIUS数据差异分析":
                wrapper.eq("account_type", BusinessAccountConfig.AccountType.RADIUS);
                break;
            case "IM账号数据差异分析":
                wrapper.eq("account_type", BusinessAccountConfig.AccountType.IM);
                break;
        }
        wrapper.eq("metric_id", businessMetricConfig.getId());
        wrapper.eq("enable", true);
        wrapper.eq("sample_strategy", BusinessAccountConfig.SampleStrategy.FIXED);
        wrapper.orderByDesc("sample_weight");
        wrapper.last("limit " + maxSampleSize);
        return baseMapper.selectList(wrapper);
    }
}
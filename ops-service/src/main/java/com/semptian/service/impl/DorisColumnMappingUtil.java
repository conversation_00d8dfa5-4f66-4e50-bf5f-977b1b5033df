package com.semptian.service.impl;

import com.semptian.entity.DetailFieldConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Doris列映射工具类
 * 用于处理CSV数据到Doris表的列映射和格式转换
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Slf4j
@Component
public class DorisColumnMappingUtil {

    /**
     * 获取协议表的列定义（按正确顺序）
     */
    public List<String> getProtocolTableColumns(String protocolType, List<DetailFieldConfig> fieldConfigs) {
        List<String> columns = new ArrayList<>();
        
        // 1. 通用字段（固定顺序）
        // 注意：id字段是自增主键，在Stream Load时不需要提供，Doris会自动生成
        columns.add("metric_id");
        columns.add("system_name");
        columns.add("data_level");
        columns.add("table_name");
        columns.add("protocol_type");
        columns.add("account");
        
        // 2. 协议特定字段（按id字段排序）
        List<DetailFieldConfig> sortedConfigs = fieldConfigs.stream()
                .filter(config -> config.getIsExport() == 1) // 只包含导出字段
                .sorted((a, b) -> Integer.compare(
                        a.getId() != null ? a.getId() : 999,
                        b.getId() != null ? b.getId() : 999))
                .collect(Collectors.toList());
        
        for (DetailFieldConfig config : sortedConfigs) {
            columns.add(config.getFieldName());
        }
        
        // 3. 系统字段
        columns.add("import_time");
        // create_time 使用DEFAULT，不需要在Stream Load中指定
        
        log.info("[列映射] 协议[{}]表列定义 | 总列数: {} | 协议字段数: {} | 通用字段数: 6 | 系统字段数: 1 | 列列表: {}",
                protocolType, columns.size(), sortedConfigs.size(), String.join(", ", columns));
        
        return columns;
    }

    /**
     * 将CSV行数据转换为Doris Stream Load格式
     */
    public String convertRowToStreamLoadFormat(Map<String, Object> csvRow, List<String> tableColumns) {
        List<String> values = new ArrayList<>();
        
        for (String column : tableColumns) {
            String value = getStringValue(csvRow, column);
            
            // 特殊处理系统字段
            if ("import_time".equals(column)) {
                value = String.valueOf(System.currentTimeMillis());
            }
            
            // 转义特殊字符
            value = escapeSpecialChars(value);
            values.add(value);
        }
        
        return String.join("\t", values);
    }

    /**
     * 验证CSV数据与表结构的兼容性
     */
    public ValidationResult validateDataCompatibility(Map<String, Object> csvRow, List<String> tableColumns) {
        List<String> missingColumns = new ArrayList<>();
        List<String> extraColumns = new ArrayList<>();
        
        Set<String> csvColumns = csvRow.keySet();
        Set<String> requiredColumns = new HashSet<>(tableColumns);
        
        // 移除系统字段，这些不需要在CSV中存在
        requiredColumns.remove("import_time");
        requiredColumns.remove("create_time");
        
        // 检查缺失的列
        for (String required : requiredColumns) {
            if (!csvColumns.contains(required)) {
                missingColumns.add(required);
            }
        }
        
        // 检查多余的列
        for (String csv : csvColumns) {
            if (!requiredColumns.contains(csv) && 
                !"import_time".equals(csv) && 
                !"create_time".equals(csv)) {
                extraColumns.add(csv);
            }
        }
        
        boolean isValid = missingColumns.isEmpty();
        
        return new ValidationResult(isValid, missingColumns, extraColumns);
    }

    /**
     * 安全获取字符串值
     */
    private String getStringValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        return value != null ? value.toString().trim() : "";
    }

    /**
     * 转义特殊字符
     */
    private String escapeSpecialChars(String value) {
        if (value == null || value.isEmpty()) {
            return "";
        }
        return value.replace("\\", "\\\\")  // 反斜杠必须最先处理
                   .replace("\t", "\\t")
                   .replace("\n", "\\n")
                   .replace("\r", "\\r")
                   .replace("\"", "\\\"");
    }

    /**
     * 生成Stream Load的列映射字符串
     *
     * 对于有自增主键的表，需要明确指定列映射，跳过id字段
     * 格式：column1,column2,column3,...,import_time,create_time=now()
     */
    public String generateColumnMapping(List<String> tableColumns) {
        List<String> mappingColumns = new ArrayList<>(tableColumns);

        // 添加create_time的默认值映射
        mappingColumns.add("create_time=now()");

        String mapping = String.join(",", mappingColumns);
        log.info("[列映射] 生成列映射字符串 | 数据列数: {} | 映射列数: {} | 映射: {}",
                tableColumns.size(), mappingColumns.size(), mapping);

        return mapping;
    }

    /**
     * 生成完整的列映射字符串（包含表的所有字段）
     * 用于明确指定哪些字段需要数据，哪些字段使用默认值
     */
    public String generateFullColumnMapping(List<String> tableColumns) {
        List<String> mappingColumns = new ArrayList<>();

        // 跳过id字段（自增主键）
        mappingColumns.add("id=null");

        // 添加数据字段
        mappingColumns.addAll(tableColumns);

        // 添加create_time的默认值映射
        mappingColumns.add("create_time=now()");

        String mapping = String.join(",", mappingColumns);
        log.info("[完整列映射] 生成完整列映射字符串 | 数据列数: {} | 总映射列数: {} | 映射: {}",
                tableColumns.size(), mappingColumns.size(), mapping);

        return mapping;
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final List<String> missingColumns;
        private final List<String> extraColumns;

        public ValidationResult(boolean valid, List<String> missingColumns, List<String> extraColumns) {
            this.valid = valid;
            this.missingColumns = missingColumns;
            this.extraColumns = extraColumns;
        }

        public boolean isValid() {
            return valid;
        }

        public List<String> getMissingColumns() {
            return missingColumns;
        }

        public List<String> getExtraColumns() {
            return extraColumns;
        }

        public String getErrorMessage() {
            if (valid) {
                return null;
            }
            
            StringBuilder sb = new StringBuilder();
            if (!missingColumns.isEmpty()) {
                sb.append("缺失字段: ").append(String.join(", ", missingColumns));
            }
            if (!extraColumns.isEmpty()) {
                if (sb.length() > 0) {
                    sb.append("; ");
                }
                sb.append("多余字段: ").append(String.join(", ", extraColumns));
            }
            return sb.toString();
        }
    }
}

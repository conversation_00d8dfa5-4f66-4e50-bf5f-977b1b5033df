package com.semptian.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.semptian.component.FileSystemUtil;
import com.semptian.component.HBaseQueryUtil;
import com.semptian.component.RedisLockUtil;
import com.semptian.entity.DetailFieldConfig;
import com.semptian.entity.DetailQueryConfig;
import com.semptian.entity.OpsApiAccessResultDifference;
import com.semptian.mapper.DetailFieldConfigMapper;
import com.semptian.mapper.DetailQueryConfigMapper;
import com.semptian.service.DetailExportService;
import com.semptian.service.ElasticsearchService;
import com.semptian.service.OpsApiAccessResultDifferenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 明细导出服务实现类
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Slf4j
@Service
public class DetailExportServiceImpl implements DetailExportService {

    @Resource
    private RedisLockUtil redisLockUtil;

    @Resource
    private OpsApiAccessResultDifferenceService opsApiAccessResultDifferenceService;

    @Resource
    private DetailQueryConfigMapper detailQueryConfigMapper;

    @Resource
    private DetailFieldConfigMapper detailFieldConfigMapper;

    @Resource
    private ElasticsearchService elasticsearchService;

    @Resource
    private HBaseQueryUtil hBaseQueryUtil;

    @Resource
    private FileSystemUtil fileSystemUtil;

    // ES查询配置参数
    @Value("${detail.export.es.batch.size:50}")
    private int esBatchSize;

    @Value("${detail.export.es.concurrent.size:5}")
    private int esConcurrentSize;

    // 并发查询线程池
    private final ExecutorService concurrentQueryExecutor = Executors.newCachedThreadPool();

    @Override
    public String exportHBaseDetail(Integer metricId, LocalDate startDate, LocalDate endDate) {
        // 如果metricId为空，则导出所有配置的指标
        if (metricId == null) {
            return exportAllMetrics(startDate, endDate);
        } else {
            return exportSingleMetric(metricId, startDate, endDate);
        }
    }

    /**
     * 导出所有配置的指标
     */
    private String exportAllMetrics(LocalDate startDate, LocalDate endDate) {
        String lockKey = "detail_export_all_" + startDate + "_" + endDate;

        // 1. 获取分布式锁
        if (!redisLockUtil.tryLockByValidityTime(lockKey)) {
            String message = String.format("全量导出任务在时间范围[%s-%s]正在执行中", startDate, endDate);
            log.warn(message);
            return message;
        }

        try {
            // 2. 检查磁盘空间
            if (!fileSystemUtil.checkDiskUsage()) {
                throw new RuntimeException("磁盘使用率超过70%，无法执行导出任务");
            }

            // 3. 获取所有配置的指标ID
            List<Integer> metricIds = detailQueryConfigMapper.getAllMetricIds();
            if (metricIds.isEmpty()) {
                String message = "未找到任何配置的指标ID";
                log.info(message);
                return message;
            }

            log.info("[全量导出] 开始导出所有指标 | 指标数量: {} | 时间范围: {}-{}", metricIds.size(), startDate, endDate);

            // 4. 全量导出前，统一清理指定日期下的所有文件（更高效的清理策略）
            log.info("[全量导出] 开始清理指定日期[{}]下的所有历史文件", startDate);
            try {
                // 清理7天前的过期文件（只需要执行一次）
                fileSystemUtil.cleanupExpiredFiles();
                // 清理指定日期下的所有指标文件
                fileSystemUtil.cleanupAllMetricFiles(startDate);
                log.info("[全量导出] 文件清理完成");
            } catch (Exception e) {
                log.warn("[全量导出] 文件清理失败: {}", e.getMessage());
            }

            int successCount = 0;
            int failCount = 0;
            StringBuilder resultBuilder = new StringBuilder();
            resultBuilder.append("全量导出结果:\n");

            // 5. 逐个导出每个指标（不再进行文件清理）
            for (Integer currentMetricId : metricIds) {
                try {
                    // 执行单个指标的导出（不清理文件，因为已经在上面统一清理过了）
                    String singleResult = executeExportProcess(currentMetricId, startDate, endDate);
                    resultBuilder.append(String.format("指标[%d]: %s\n", currentMetricId, singleResult));
                    successCount++;

                    log.info("[全量导出] 指标[{}]导出完成: {}", currentMetricId, singleResult);

                } catch (Exception e) {
                    String errorMsg = String.format("指标[%d]导出失败: %s", currentMetricId, e.getMessage());
                    resultBuilder.append(errorMsg).append("\n");
                    failCount++;
                    log.error("[全量导出] 指标[{}]导出失败: {}", currentMetricId, e.getMessage(), e);
                }
            }

            String finalMessage = String.format("全量导出完成 | 总指标数: %d | 成功: %d | 失败: %d",
                    metricIds.size(), successCount, failCount);
            resultBuilder.append(finalMessage);

            log.info("[全量导出] {}", finalMessage);
            return resultBuilder.toString();

        } catch (Exception e) {
            log.error("[全量导出] 导出失败 | 时间范围: {}-{} | 错误: {}",
                     startDate, endDate, e.getMessage(), e);
            throw new RuntimeException("全量导出失败: " + e.getMessage(), e);
        } finally {
            // 5. 释放锁
            redisLockUtil.unlock(lockKey);
        }
    }

    /**
     * 导出单个指标
     */
    private String exportSingleMetric(Integer metricId, LocalDate startDate, LocalDate endDate) {
        String lockKey = "detail_export_" + metricId + "_" + startDate + "_" + endDate;

        // 1. 获取分布式锁
        if (!redisLockUtil.tryLockByValidityTime(lockKey)) {
            String message = String.format("指标[%d]在时间范围[%s-%s]的导出任务正在执行中", metricId, startDate, endDate);
            log.warn(message);
            return message;
        }

        try {
            // 2. 检查磁盘空间
            if (!fileSystemUtil.checkDiskUsage()) {
                throw new RuntimeException("磁盘使用率超过70%，无法执行导出任务");
            }

            // 3. 清理文件
            fileSystemUtil.cleanupFiles(metricId, startDate);

            // 4. 执行导出流程
            return executeExportProcess(metricId, startDate, endDate);

        } catch (Exception e) {
            log.error("[明细导出] 导出失败 | 指标ID: {} | 时间范围: {}-{} | 错误: {}",
                     metricId, startDate, endDate, e.getMessage(), e);
            throw new RuntimeException("导出失败: " + e.getMessage(), e);
        } finally {
            // 5. 释放锁
            redisLockUtil.unlock(lockKey);
        }
    }

    /**
     * 执行导出流程
     */
    private String executeExportProcess(Integer metricId, LocalDate startDate, LocalDate endDate) {
        log.info("[明细导出] 开始执行导出流程 | 指标ID: {} | 时间范围: {}-{}", metricId, startDate, endDate);

        // 1. 读取差异账号
        List<OpsApiAccessResultDifference> differenceList = getDifferenceAccounts(metricId, startDate, endDate);
        if (differenceList.isEmpty()) {
            String message = String.format("指标[%d]在时间范围[%s-%s]没有找到差异账号", metricId, startDate, endDate);
            log.info(message);
            return message;
        }

        log.info("[明细导出] 找到差异账号数量: {}", differenceList.size());

        // 2. 读取DSL配置
        DetailQueryConfig queryConfig = detailQueryConfigMapper.getByMetricId(metricId);
        if (queryConfig == null) {
            throw new RuntimeException("未找到指标[" + metricId + "]的DSL查询配置");
        }

        // 3. 提取账号列表
        List<String> accounts = differenceList.stream()
                .map(OpsApiAccessResultDifference::getAccount)
                .distinct()
                .collect(Collectors.toList());

        // 4. 执行ES查询获取data_id和协议信息
        Map<String, List<Map<String, Object>>> dataByProtocol = executeEsQueryAndGroupByProtocol(accounts, queryConfig, startDate, endDate);

        int totalExportedFiles = 0;
        for (Map.Entry<String, List<Map<String, Object>>> entry : dataByProtocol.entrySet()) {
            String protocolType = entry.getKey();
            List<Map<String, Object>> esData = entry.getValue();

            try {
                // 5. 执行单个协议的导出
                boolean exported = exportProtocolData(metricId, protocolType, esData, startDate, endDate);
                if (exported) {
                    totalExportedFiles++;
                }
            } catch (Exception e) {
                log.error("[明细导出] 协议[{}]导出失败: {}", protocolType, e.getMessage(), e);
            }
        }

        String message = String.format("导出完成 | 指标ID: %d | 协议数: %d | 成功导出文件数: %d",
                                      metricId, dataByProtocol.size(), totalExportedFiles);
        log.info(message);
        return message;
    }

    /**
     * 获取差异账号 - 使用原生SQL查询
     */
    private List<OpsApiAccessResultDifference> getDifferenceAccounts(Integer metricId, LocalDate startDate, LocalDate endDate) {
        try {
            log.debug("[差异账号查询] 开始查询 | 指标ID: {} | 时间范围: {}-{}", metricId, startDate, endDate);

            List<OpsApiAccessResultDifference> results = opsApiAccessResultDifferenceService.selectDifferenceAccountsBySql(
                    metricId,
                    Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()),
                    Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant())
            );

            log.debug("[差异账号查询] 查询完成 | 指标ID: {} | 结果数量: {}", metricId, results.size());
            return results;

        } catch (Exception e) {
            log.error("[差异账号查询] 查询失败 | 指标ID: {} | 时间范围: {}-{} | 错误: {}",
                     metricId, startDate, endDate, e.getMessage(), e);
            throw new RuntimeException("查询差异账号失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行ES查询并按协议类型分组 - 智能查询策略
     */
    private Map<String, List<Map<String, Object>>> executeEsQueryAndGroupByProtocol(List<String> accounts,
                                                                                   DetailQueryConfig queryConfig,
                                                                                   LocalDate startDate,
                                                                                   LocalDate endDate) {
        Map<String, List<Map<String, Object>>> result;

        try {
            String dslTemplate = queryConfig.getParamsTemplate();
            String indices = queryConfig.getReqPath();

            // 判断查询策略：检查DSL模板中的fields字段数量
            boolean useBatchQuery = shouldUseBatchQuery(dslTemplate);

            if (useBatchQuery) {
                log.info("[ES查询策略] 使用批量查询模式 | 账号数: {}", accounts.size());
                result = executeBatchQuery(accounts, dslTemplate, indices, queryConfig.getResponseMapping(), startDate, endDate);
            } else {
                log.info("[ES查询策略] 使用单账号并发查询模式 | 账号数: {} | 并发数: {}", accounts.size(), esConcurrentSize);
                result = executeConcurrentSingleQuery(accounts, dslTemplate, indices, queryConfig.getResponseMapping(), startDate, endDate);
            }

            return result;

        } catch (Exception e) {
            log.error("[ES查询] 查询失败: {}", e.getMessage(), e);
            throw new RuntimeException("ES查询失败", e);
        }
    }

    /**
     * 判断是否应该使用批量查询
     * 当DSL模板中的query_string的fields只有一个字段时，使用批量查询
     */
    private boolean shouldUseBatchQuery(String dslTemplate) {
        try {
            JSONObject dslObject = JSON.parseObject(dslTemplate);

            // 查找query_string中的fields字段
            JSONArray fields = findQueryStringFields(dslObject);

            if (fields != null && fields.size() == 1) {
                String fieldName = fields.getString(0);
                log.debug("[查询策略判断] 发现单个查询字段: {} | 使用批量查询", fieldName);
                return true;
            } else {
                log.debug("[查询策略判断] 发现多个查询字段或无query_string | 使用单账号并发查询");
                return false;
            }

        } catch (Exception e) {
            log.warn("[查询策略判断] 解析DSL模板失败，默认使用单账号查询: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 递归查找DSL中的query_string的fields字段
     */
    private JSONArray findQueryStringFields(Object obj) {
        if (obj instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) obj;

            // 检查当前对象是否包含query_string
            if (jsonObj.containsKey("query_string")) {
                JSONObject queryString = jsonObj.getJSONObject("query_string");
                if (queryString.containsKey("fields")) {
                    return queryString.getJSONArray("fields");
                }
            }

            // 递归检查所有子对象
            for (String key : jsonObj.keySet()) {
                JSONArray result = findQueryStringFields(jsonObj.get(key));
                if (result != null) {
                    return result;
                }
            }
        } else if (obj instanceof JSONArray) {
            JSONArray jsonArray = (JSONArray) obj;
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONArray result = findQueryStringFields(jsonArray.get(i));
                if (result != null) {
                    return result;
                }
            }
        }

        return null;
    }

    /**
     * 执行批量查询
     */
    private Map<String, List<Map<String, Object>>> executeBatchQuery(List<String> accounts, String dslTemplate,
                                                                    String indices, String responseMapping,
                                                                    LocalDate startDate, LocalDate endDate) {
        Map<String, List<Map<String, Object>>> result = new HashMap<>();

        // 提取账号字段名，用于后续从HBase数据中提取账号
        String accountFieldName = extractAccountFieldNameFromDsl(dslTemplate);
        log.debug("[批量查询] 提取到账号字段名: {}", accountFieldName);

        // 分批处理账号
        List<List<String>> accountBatches = partitionList(accounts, esBatchSize);
        log.info("[批量查询] 账号总数: {} | 批次数: {} | 每批大小: {}", accounts.size(), accountBatches.size(), esBatchSize);

        for (int i = 0; i < accountBatches.size(); i++) {
            List<String> accountBatch = accountBatches.get(i);
            try {
                log.debug("[批量查询] 执行第{}批查询 | 账号数: {}", i + 1, accountBatch.size());

                // 构建批量查询DSL
                String dsl = replaceDslParametersForBatch(dslTemplate, accountBatch, startDate, endDate);

                // 执行查询
                JSONObject response = elasticsearchService.executeDslQuery(indices, dsl);

                // 解析响应并按协议类型分组
                parseEsResponseAndGroupByProtocolBatch(response, responseMapping, result);

            } catch (Exception e) {
                log.error("[批量查询] 第{}批查询失败: {}", i + 1, e.getMessage(), e);
                // 继续处理下一批，不中断整个流程
            }
        }

        return result;
    }

    /**
     * 从DSL模板中提取账号字段名
     * 从query_string的fields字段中提取第一个字段作为账号字段
     */
    private String extractAccountFieldNameFromDsl(String dslTemplate) {
        try {
            JSONArray fields = findQueryStringFields(JSON.parseObject(dslTemplate));
            if (fields != null && fields.size() > 0) {
                String fieldName = fields.getString(0);
                // 移除.keyword后缀，转换为HBase字段名
                if (fieldName.endsWith(".keyword")) {
                    fieldName = fieldName.substring(0, fieldName.length() - ".keyword".length());
                }
                // 转换为HBase字段名格式
                return "_data_" + fieldName;
            }
        } catch (Exception e) {
            log.warn("[字段提取] 从DSL模板中提取账号字段名失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 执行单账号并发查询
     */
    private Map<String, List<Map<String, Object>>> executeConcurrentSingleQuery(List<String> accounts, String dslTemplate,
                                                                               String indices, String responseMapping,
                                                                               LocalDate startDate, LocalDate endDate) {
        Map<String, List<Map<String, Object>>> result = new HashMap<>();

        // 分批处理账号以控制并发数
        List<List<String>> accountBatches = partitionList(accounts, esConcurrentSize);
        log.info("[并发查询] 账号总数: {} | 并发批次数: {} | 每批并发数: {}", accounts.size(), accountBatches.size(), esConcurrentSize);

        for (int batchIndex = 0; batchIndex < accountBatches.size(); batchIndex++) {
            List<String> accountBatch = accountBatches.get(batchIndex);

            // 创建并发任务
            List<CompletableFuture<Map<String, List<Map<String, Object>>>>> futures = new ArrayList<>();

            for (String account : accountBatch) {
                CompletableFuture<Map<String, List<Map<String, Object>>>> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        // 构建单账号查询DSL
                        String dsl = replaceDslParameters(dslTemplate, Lists.newArrayList(account), startDate, endDate);

                        // 执行查询
                        JSONObject response = elasticsearchService.executeDslQuery(indices, dsl);

                        // 解析响应
                        Map<String, List<Map<String, Object>>> accountResult = new HashMap<>();
                        parseEsResponseAndGroupByProtocol(response, responseMapping, account, accountResult);

                        return accountResult;

                    } catch (Exception e) {
                        log.error("[并发查询] 账号[{}]查询失败: {}", account, e.getMessage(), e);
                        return new HashMap<>();
                    }
                }, concurrentQueryExecutor);

                futures.add(future);
            }

            // 等待当前批次所有任务完成并合并结果
            try {
                log.debug("[并发查询] 等待第{}批并发任务完成 | 任务数: {}", batchIndex + 1, futures.size());

                CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                allOf.get(); // 等待所有任务完成

                // 合并结果
                for (CompletableFuture<Map<String, List<Map<String, Object>>>> future : futures) {
                    Map<String, List<Map<String, Object>>> batchResult = future.get();
                    mergeQueryResults(result, batchResult);
                }

                log.debug("[并发查询] 第{}批并发任务完成", batchIndex + 1);

            } catch (Exception e) {
                log.error("[并发查询] 第{}批并发任务执行失败: {}", batchIndex + 1, e.getMessage(), e);
            }
        }

        return result;
    }

    /**
     * 将列表分割成指定大小的批次
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }

    /**
     * 合并查询结果
     */
    private void mergeQueryResults(Map<String, List<Map<String, Object>>> target,
                                 Map<String, List<Map<String, Object>>> source) {
        for (Map.Entry<String, List<Map<String, Object>>> entry : source.entrySet()) {
            target.computeIfAbsent(entry.getKey(), k -> new ArrayList<>()).addAll(entry.getValue());
        }
    }

    /**
     * 解析ES响应并按协议类型分组 - 批量查询版本
     */
    private void parseEsResponseAndGroupByProtocolBatch(JSONObject response, String responseMapping,
                                                       Map<String, List<Map<String, Object>>> result) {
        try {
            JSONObject hits = response.getJSONObject("hits");
            JSONArray hitsArray = hits.getJSONArray("hits");

            // 解析响应映射配置
            JSONObject mapping = JSON.parseObject(responseMapping);
            String dataIdField = mapping.getString("data_id");

            for (int i = 0; i < hitsArray.size(); i++) {
                JSONObject hit = hitsArray.getJSONObject(i);
                String index = hit.getString("_index");

                // 从索引名称中提取协议类型
                String[] protocolTypeArr = extractProtocolTypeFromIndex(index);
                String protocolType = protocolTypeArr[0];
                String protocolTypeChild = protocolTypeArr.length > 1 ? protocolTypeArr[1] : "";

                // 构建数据对象 - 批量查询时不从ES响应中提取账号，账号信息将在HBase查询后从HBase结果中提取
                Map<String, Object> dataItem = new HashMap<>();
                // 暂时不设置account字段，将在合并HBase数据时从HBase结果中提取
                dataItem.put("data_id", extractDataIdFromHit(hit, dataIdField));
                dataItem.put("index", index);
                dataItem.put("protocol_type", protocolType);
                dataItem.put("protocol_type_child", protocolTypeChild);

                result.computeIfAbsent(protocolType, k -> new ArrayList<>()).add(dataItem);
            }

        } catch (Exception e) {
            log.error("[响应解析] 解析ES批量查询响应失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从ES hit中提取data_id
     */
    private String extractDataIdFromHit(JSONObject hit, String dataIdField) {
        try {
            // 如果dataIdField包含_source前缀，从_source中提取
            if (dataIdField.startsWith("_source.")) {
                String fieldName = dataIdField.substring("_source.".length());
                JSONObject source = hit.getJSONObject("_source");
                return source != null ? source.getString(fieldName) : null;
            } else {
                // 直接从hit中提取
                return hit.getString(dataIdField);
            }
        } catch (Exception e) {
            log.warn("[数据提取] 无法从ES hit中提取data_id: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析ES响应并按协议类型分组 - 单账号查询版本
     */
    private void parseEsResponseAndGroupByProtocol(JSONObject response, String responseMapping, String account, Map<String, List<Map<String, Object>>> result) {
        try {
            JSONObject hits = response.getJSONObject("hits");
            JSONArray hitsArray = hits.getJSONArray("hits");

            // 解析响应映射配置
            JSONObject mapping = JSON.parseObject(responseMapping);
            String dataIdField = mapping.getString("data_id");

            for (int i = 0; i < hitsArray.size(); i++) {
                JSONObject hit = hitsArray.getJSONObject(i);
                String index = hit.getString("_index");

                // 从索引名称中提取协议类型
                String[] protocolTypeArr = extractProtocolTypeFromIndex(index);

                String protocolType = protocolTypeArr[0];
                String protocolTypeChild = protocolTypeArr.length > 1 ? protocolTypeArr[1] : "";

                // 构建数据对象
                Map<String, Object> dataItem = new HashMap<>();
                dataItem.put("account", account);
                dataItem.put("data_id", extractDataIdFromHit(hit, dataIdField));
                dataItem.put("index", index);
                dataItem.put("protocol_type", protocolType);
                dataItem.put("protocol_type_child", protocolTypeChild);

                result.computeIfAbsent(protocolType, k -> new ArrayList<>()).add(dataItem);
            }

        } catch (Exception e) {
            log.error("[响应解析] 解析ES响应失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从索引名称中提取协议类型
     * 例如：deye_v64_email_202505-000001 -> email
     */
    private String[] extractProtocolTypeFromIndex(String index) {
        try {
            // 索引格式：deye_v64_{protocol}_{date}-{suffix}
            String[] parts = index.split("_");
            if (parts.length >= 3) {
                // 第三部分是协议类型

                //mobilenetradius和location根据运营商进行拆分需要特殊处理.例如:deye_v64_mobilenetradius_djezzy_30d -> mobilenetradius_djezzy
                if ("mobilenetradius".equals(parts[2]) || "location".equals(parts[2])) {
                    return new String[]{parts[2], parts[3]};
                }else {
                    return new String[]{parts[2], ""};
                }
            }
        } catch (Exception e) {
            log.warn("[协议解析] 无法从索引名称解析协议类型: {}", index);
        }

        // 默认返回unknown
        return new String[]{"unknown", ""};
    }

    /**
     * 导出单个协议的数据
     */
    private boolean exportProtocolData(Integer metricId, String protocolType, List<Map<String, Object>> esData,
                                     LocalDate startDate, LocalDate endDate) {
        try {

            log.info("[明细导出] 开始导出协议[{}]数据 | ES记录数: {}", protocolType, esData.size());

            // 1. 提取data_id列表
            List<String> dataIds = esData.stream()
                    .map(item -> (String) item.get("data_id"))
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (dataIds.isEmpty()) {
                log.warn("[明细导出] 协议[{}]未找到有效的data_id", protocolType);
                return false;
            }

            // 2. 获取HBase数据
            List<Map<String, Object>> hbaseData = getHbaseData(protocolType, esData, startDate, endDate);
            if (hbaseData.isEmpty()) {
                log.warn("[明细导出] 协议[{}]未查询到HBase数据", protocolType);
                return false;
            }

            // 3. 获取字段配置
            List<DetailFieldConfig> fieldConfigs = detailFieldConfigMapper.getExportFieldsByDataType(protocolType);

            // 4. 合并ES数据和HBase数据
            List<Map<String, Object>> mergedData = mergeEsAndHBaseData(esData, hbaseData);

            // 5. 生成CSV文件
            generateCsvFile(metricId, protocolType, mergedData, fieldConfigs, startDate);

            log.info("[明细导出] 协议[{}]导出成功 | data_id数: {} | HBase记录数: {} | 合并记录数: {}",
                    protocolType, dataIds.size(), hbaseData.size(), mergedData.size());
            return true;

        } catch (Exception e) {
            log.error("[明细导出] 协议[{}]导出失败: {}", protocolType, e.getMessage(), e);
            return false;
        }
    }

    private List<Map<String, Object>> getHbaseData(String protocolType, List<Map<String, Object>> esData, LocalDate startDate, LocalDate endDate) {
        List<Map<String, Object>> hbaseData = new ArrayList<>();

        //1 location和mobilenetradius根据运营商进行拆分后查询HBase表需要特殊处理
        //把数据按照protocol_type_child值进行分组
        Map<String, List<Map<String, Object>>> groupedData = esData.stream().collect(Collectors.groupingBy(item -> {
            String protocolTypeChild = (String) item.get("protocol_type_child");
            return protocolTypeChild != null ? protocolTypeChild : "";
        }));

        //2. 批量查询HBase获取明细数据
        for (Map.Entry<String, List<Map<String, Object>>> entry : groupedData.entrySet()) {
            String protocolTypeChild = entry.getKey();
            List<Map<String, Object>> value = entry.getValue();

            List<String> ids = value.stream()
                    .map(item -> (String) item.get("data_id"))
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            StringBuilder protocolTypeBuilder = new StringBuilder(protocolType);
            if (StrUtil.isNotEmpty(protocolTypeChild)) {
                protocolTypeBuilder.append("_").append(protocolTypeChild);
            }

            List<Map<String, Object>> list = queryHBaseData(protocolTypeBuilder.toString(), ids, startDate, endDate);
            if (CollUtil.isNotEmpty(list)) {
                hbaseData.addAll(list);
            }
        }
        return hbaseData;
    }

    /**
     * 合并ES数据和HBase数据
     */
    private List<Map<String, Object>> mergeEsAndHBaseData(List<Map<String, Object>> esData,
                                                         List<Map<String, Object>> hbaseData) {
        List<Map<String, Object>> mergedData = new ArrayList<>();

        // 创建HBase数据的索引，以data_id为key
        Map<String, Map<String, Object>> hbaseIndex = new HashMap<>();
        for (Map<String, Object> hbaseRow : hbaseData) {
            String dataId = extractDataIdFromRowKey((String) hbaseRow.get("rowkey"));
            if (dataId != null) {
                hbaseIndex.put(dataId, hbaseRow);
            }
        }

        // 合并数据
        for (Map<String, Object> esRow : esData) {
            String dataId = (String) esRow.get("data_id");
            Map<String, Object> hbaseRow = hbaseIndex.get(dataId);

            if (hbaseRow != null) {
                Map<String, Object> mergedRow = new HashMap<>();
                // 添加ES数据
                mergedRow.putAll(esRow);
                // 添加HBase数据
                mergedRow.putAll(hbaseRow);

                // 如果ES数据中没有account字段（批量查询模式），从HBase数据中提取账号字段
                if (!mergedRow.containsKey("account") || mergedRow.get("account") == null) {
                    String account = extractAccountFromHBaseData(hbaseRow);
                    if (account != null) {
                        mergedRow.put("account", account);
                    }
                }

                mergedData.add(mergedRow);
            }
        }

        return mergedData;
    }

    /**
     * 从HBase数据中提取账号字段值
     * 优先使用指定的账号字段名，如果没有则使用常见的账号字段名
     */
    private String extractAccountFromHBaseData(Map<String, Object> hbaseRow, String preferredAccountField) {
        // 如果指定了账号字段名，优先使用
        if (preferredAccountField != null) {
            Object value = hbaseRow.get(preferredAccountField);
            if (value != null && !value.toString().trim().isEmpty()) {
                log.debug("[账号提取] 使用指定字段[{}]提取到账号: {}", preferredAccountField, value);
                return value.toString();
            }
        }

        // 按优先级顺序查找账号字段
        String[] accountFields = {
            "_data_main_account", "main_account",
            "_data_auth_account", "auth_account",
            "_data_account", "account",
            "_data_user_id", "user_id",
            "_data_username", "username"
        };

        for (String fieldName : accountFields) {
            Object value = hbaseRow.get(fieldName);
            if (value != null && !value.toString().trim().isEmpty()) {
                log.debug("[账号提取] 使用默认字段[{}]提取到账号: {}", fieldName, value);
                return value.toString();
            }
        }

        // 如果没有找到标准账号字段，查找包含"account"关键字的字段
        for (Map.Entry<String, Object> entry : hbaseRow.entrySet()) {
            String key = entry.getKey().toLowerCase();
            if (key.contains("account") && entry.getValue() != null && !entry.getValue().toString().trim().isEmpty()) {
                log.debug("[账号提取] 使用模糊匹配字段[{}]提取到账号: {}", entry.getKey(), entry.getValue());
                return entry.getValue().toString();
            }
        }

        log.warn("[账号提取] 未能从HBase数据中找到账号字段 | 指定字段: {} | 可用字段: {}",
                preferredAccountField, hbaseRow.keySet());
        return null;
    }

    /**
     * 从HBase数据中提取账号字段值（重载方法，兼容性）
     */
    private String extractAccountFromHBaseData(Map<String, Object> hbaseRow) {
        return extractAccountFromHBaseData(hbaseRow, null);
    }

    /**
     * 从HBase行键中提取data_id
     */
    private String extractDataIdFromRowKey(String rowKey) {
        try {
            // 根据实际的HBase行键格式来解析data_id
            // 这里假设行键格式包含data_id信息
            return rowKey;
        } catch (Exception e) {
            log.warn("[数据合并] 无法从行键解析data_id: {}", rowKey);
            return null;
        }
    }

    /**
     * 替换DSL模板参数 - 批量查询版本
     */
    private String replaceDslParametersForBatch(String dslTemplate, List<String> accounts,
                                               LocalDate startDate, LocalDate endDate) {
        try {
            // 1. 解析DSL模板为JSONObject
            JSONObject dslObject = JSON.parseObject(dslTemplate);

            // 2. 获取dateFormat字段，判断时间格式
            String dateFormat = dslObject.getString("dateFormat");

            // 3. 移除dateFormat字段，避免Elasticsearch报错
            dslObject.remove("dateFormat");

            // 4. 构建批量账号查询条件 - 使用OR连接多个账号，增加转义处理
            String accountsQueryString = accounts.stream()
                    .map(this::escapeAccountForQuery)
                    .collect(Collectors.joining(" OR "));

            // 5. 将JSONObject转回字符串并替换参数
            String dsl = dslObject.toJSONString();

            // 6. 替换账号参数 - 支持多种格式，增加转义处理
            String firstAccount = accounts.isEmpty() ? "" : escapeAccountForReplacement(accounts.get(0));
            dsl = dsl.replace("#{account}", firstAccount)
                    .replace("{accounts}", escapeForJsonReplacement(accountsQueryString))
                    .replace("{accountsQueryString}", escapeForJsonReplacement(accountsQueryString));

            // 7. 根据dateFormat替换时间参数
            if ("timestamp".equals(dateFormat)) {
                // 时间戳格式，将时间转化为13位时间戳，使用系统默认时区
                long startTimestamp = startDate.atStartOfDay().atZone(java.time.ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                long endTimestamp = endDate.atStartOfDay().atZone(java.time.ZoneOffset.systemDefault()).toInstant().toEpochMilli() + 24 * 60 * 60 * 1000 - 1;

                dsl = dsl.replace("112233", String.valueOf(startTimestamp))
                        .replace("445566", String.valueOf(endTimestamp));

                log.debug("[DSL参数替换-批量] 使用时间戳格式 | 开始时间戳: {} | 结束时间戳: {}", startTimestamp, endTimestamp);
            } else {
                // 字符串日期格式
                dsl = dsl.replace("#{startDay}", startDate.toString())
                        .replace("#{endDay}", endDate.toString());

                log.debug("[DSL参数替换-批量] 使用字符串日期格式 | 开始日期: {} | 结束日期: {}", startDate, endDate);
            }

            // 8. 替换其他通用时间参数
            dsl = dsl.replace("{startDate}", startDate.toString())
                    .replace("{endDate}", endDate.toString())
                    .replace("{startTime}", startDate.atStartOfDay().toString())
                    .replace("{endTime}", endDate.plusDays(1).atStartOfDay().toString());

            log.debug("[DSL参数替换-批量] 账号数: {} | 账号查询字符串: {} | 处理后DSL: {}",
                     accounts.size(), accountsQueryString, dsl);

            return dsl;

        } catch (Exception e) {
            log.error("[DSL参数替换-批量] 解析DSL模板失败: {}", e.getMessage(), e);
            // 如果解析失败，使用原始方式处理（向后兼容），但仍然使用转义处理
            String accountsQueryString = accounts.stream()
                    .map(this::escapeAccountForQuery)
                    .collect(Collectors.joining(" OR "));

            return dslTemplate
                    .replace("{accounts}", accountsQueryString)
                    .replace("{accountsQueryString}", accountsQueryString)
                    .replace("{startDate}", startDate.toString())
                    .replace("{endDate}", endDate.toString())
                    .replace("{startTime}", startDate.atStartOfDay().toString())
                    .replace("{endTime}", endDate.plusDays(1).atStartOfDay().toString());
        }
    }

    /**
     * 替换DSL模板参数 - 单账号查询版本
     */
    private String replaceDslParameters(String dslTemplate, List<String> accounts,
                                      LocalDate startDate, LocalDate endDate) {
        try {
            // 1. 解析DSL模板为JSONObject
            JSONObject dslObject = JSON.parseObject(dslTemplate);

            // 2. 获取dateFormat字段，判断时间格式
            String dateFormat = dslObject.getString("dateFormat");

            // 3. 移除dateFormat字段，避免Elasticsearch报错
            dslObject.remove("dateFormat");

            // 4. 构建账号查询条件，增加转义处理
            String accountsJson = accounts.stream()
                    .map(account -> "\\\"" + escapeAccountForReplacement(account) + "\\\"")
                    .collect(Collectors.joining(","));

            // 5. 将JSONObject转回字符串并替换参数
            String dsl = dslObject.toJSONString();

            // 6. 根据dateFormat替换时间参数
            if ("timestamp".equals(dateFormat)) {
                // 时间戳格式，将时间转化为13位时间戳，使用系统默认时区
                long startTimestamp = startDate.atStartOfDay().atZone(java.time.ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                long endTimestamp = endDate.atStartOfDay().atZone(java.time.ZoneOffset.systemDefault()).toInstant().toEpochMilli() + 24 * 60 * 60 * 1000 - 1;

                dsl = dsl.replace("112233", String.valueOf(startTimestamp))
                        .replace("445566", String.valueOf(endTimestamp));

                log.debug("[DSL参数替换] 使用时间戳格式 | 开始时间戳: {} | 结束时间戳: {}", startTimestamp, endTimestamp);
            } else {
                // 字符串日期格式
                dsl = dsl.replace("#{startDay}", startDate.toString())
                        .replace("#{endDay}", endDate.toString());

                log.debug("[DSL参数替换] 使用字符串日期格式 | 开始日期: {} | 结束日期: {}", startDate, endDate);
            }

            // 7. 替换账号参数，增加转义处理
            String firstAccount = accounts.isEmpty() ? "" : escapeAccountForReplacement(accounts.get(0));
            dsl = dsl.replace("#{account}", firstAccount)
                    .replace("{accounts}", accountsJson);

            // 8. 替换其他通用时间参数
            dsl = dsl.replace("{startDate}", startDate.toString())
                    .replace("{endDate}", endDate.toString())
                    .replace("{startTime}", startDate.atStartOfDay().toString())
                    .replace("{endTime}", endDate.plusDays(1).atStartOfDay().toString());

            log.debug("[DSL参数替换] 原始模板: {}", dslTemplate);
            log.debug("[DSL参数替换] 处理后DSL: {}", dsl);

            return dsl;

        } catch (Exception e) {
            log.error("[DSL参数替换] 解析DSL模板失败: {}", e.getMessage(), e);
            // 如果解析失败，使用原始方式处理（向后兼容），但仍然使用转义处理
            String accountsJson = accounts.stream()
                    .map(account -> "\"" + escapeAccountForReplacement(account) + "\"")
                    .collect(Collectors.joining(","));

            return dslTemplate
                    .replace("{accounts}", accountsJson)
                    .replace("{startDate}", startDate.toString())
                    .replace("{endDate}", endDate.toString())
                    .replace("{startTime}", startDate.atStartOfDay().toString())
                    .replace("{endTime}", endDate.plusDays(1).atStartOfDay().toString());
        }
    }



    /**
     * 查询HBase数据
     */
    private List<Map<String, Object>> queryHBaseData(String protocolType, List<String> dataIds,
                                                   LocalDate startDate, LocalDate endDate) {
        List<Map<String, Object>> allData = new ArrayList<>();

        if (dataIds == null || dataIds.isEmpty()) {
            log.warn("[HBase查询] 数据ID列表为空，跳过查询");
            return allData;
        }

        // 按月份分组查询（HBase表按月分表）
        Set<String> months = getMonthsBetween(startDate, endDate);
        log.info("[HBase查询] 协议: {} | 月份数: {} | 数据ID数: {}", protocolType, months.size(), dataIds.size());

        for (String month : months) {
            String tableName = hBaseQueryUtil.getTableName(protocolType, month);

            try {
                // 检查表是否存在
                if (!hBaseQueryUtil.tableExists(tableName)) {
                    log.warn("[HBase查询] 表不存在，跳过查询 | 表: {}", tableName);
                    continue;
                }

                log.debug("[HBase查询] 开始查询 | 表: {} | 数据ID数: {}", tableName, dataIds.size());
                List<Map<String, Object>> monthData = hBaseQueryUtil.batchGet(tableName, dataIds);
                allData.addAll(monthData);
                log.debug("[HBase查询] 查询完成 | 表: {} | 结果数: {}", tableName, monthData.size());

            } catch (Exception e) {
                log.error("[HBase查询] 查询失败 | 表: {} | 错误: {}", tableName, e.getMessage(), e);
                // 继续处理其他月份的数据，不中断整个流程
            }
        }

        log.info("[HBase查询] 总查询完成 | 协议: {} | 总结果数: {}", protocolType, allData.size());
        return allData;
    }

    /**
     * 获取时间范围内的月份列表
     */
    private Set<String> getMonthsBetween(LocalDate startDate, LocalDate endDate) {
        Set<String> months = new HashSet<>();
        LocalDate current = startDate.withDayOfMonth(1);

        while (!current.isAfter(endDate)) {
            months.add(current.format(DateTimeFormatter.ofPattern("yyyyMM")));
            current = current.plusMonths(1);
        }

        return months;
    }

    /**
     * 生成CSV文件
     */
    private void generateCsvFile(Integer metricId, String protocolType,
                               List<Map<String, Object>> hbaseData,
                               List<DetailFieldConfig> fieldConfigs, LocalDate targetDate) throws IOException {

        long timestamp = System.currentTimeMillis();
        Path filePath = fileSystemUtil.createExportFilePath(metricId, protocolType, timestamp, targetDate);

        try (BufferedWriter writer = Files.newBufferedWriter(filePath, StandardCharsets.UTF_8)) {
            // 1. 写入CSV头部
            writeCsvHeader(writer, fieldConfigs);

            // 2. 写入数据行
            for (Map<String, Object> rowData : hbaseData) {
                writeCsvRow(writer, rowData, fieldConfigs, metricId, protocolType);
            }
        }

        log.info("[文件生成] CSV文件生成成功: {}", filePath);
    }

    /**
     * 写入CSV头部
     */
    private void writeCsvHeader(BufferedWriter writer, List<DetailFieldConfig> fieldConfigs) throws IOException {
        List<String> headers = new ArrayList<>();

        // 通用字段
        headers.addAll(Arrays.asList("监控指标", "系统", "数据层级", "表名", "协议类型编码",  "指标账号"));

        // 协议明细字段
        for (DetailFieldConfig config : fieldConfigs) {
            headers.add(config.getFieldName());
        }

        writer.write(String.join(",", headers));
        writer.newLine();
    }

    /**
     * 写入CSV数据行
     */
    private void writeCsvRow(BufferedWriter writer, Map<String, Object> rowData,
                           List<DetailFieldConfig> fieldConfigs, Integer metricId, String protocolType) throws IOException {
        // 预处理：去除 "_data_" 前缀并处理账号字段映射
        Map<String, Object> processedRowData = new HashMap<>();
        for (Map.Entry<String, Object> entry : rowData.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            //忽略es里的data_id,保留hbase里的data_id
            if ("data_id".equals(key) && rowData.containsKey("_data_data_id")){
                continue;
            }

            if (key.startsWith("_data_")) {
                // 去除 "_data_" 前缀
                key = key.substring("_data_".length());

                // 处理账号字段映射：_data_main_account -> main_account
                // 在HBase中账号字段为_data_main_account或_data_auth_account
                // 在ES中账号字段为main_account或auth_account
                if ("main_account".equals(key) || "auth_account".equals(key)) {
                    // 如果ES数据中已经有account字段，优先使用ES的account字段
                    if (!processedRowData.containsKey("account")) {
                        processedRowData.put("account", value);
                    }
                }
            }
            processedRowData.put(key, value);
        }


        List<String> values = new ArrayList<>();

        // 通用字段值
        values.add(metricId.toString()); // 监控指标
        values.add("搜索"); // 系统
        values.add("HBase"); // 数据层级
        values.add("明细表"); // 表名
        values.add(protocolType); // 协议类型编码
        values.add(escapeCSV(String.valueOf(processedRowData.getOrDefault("account", "")))); // 指标账号

        // 协议明细字段值
        for (DetailFieldConfig config : fieldConfigs) {
            String fieldValue = String.valueOf(processedRowData.getOrDefault(config.getFieldName(), ""));
            values.add(escapeCSV(fieldValue));
        }

        writer.write(String.join(",", values));
        writer.newLine();
    }

    /**
     * CSV字段转义
     */
    private String escapeCSV(String value) {
        if (value == null) {
            return "";
        }

        // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (value.contains(",") || value.contains("\"") || value.contains("\n") || value.contains("\r")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }

        return value;
    }

    /**
     * 为查询字符串转义账号值
     * 处理账号值中的特殊字符，确保在query_string中正确使用
     */
    private String escapeAccountForQuery(String account) {
        if (account == null) {
            return "\"\"";
        }

        // 转义特殊字符：双引号、反斜杠、换行符等
        String escaped = account
                .replace("\\", "\\\\")  // 反斜杠必须先转义
                .replace("\"", "\\\"")  // 转义双引号
                .replace("\n", "\\n")   // 转义换行符
                .replace("\r", "\\r")   // 转义回车符
                .replace("\t", "\\t");  // 转义制表符

        return "\"" + escaped + "\"";
    }

    /**
     * 为字符串替换转义账号值
     * 处理在JSON字符串中直接替换时的转义问题
     */
    private String escapeAccountForReplacement(String account) {
        if (account == null) {
            return "";
        }

        // 转义JSON字符串中的特殊字符
        return account
                .replace("\\", "\\\\")  // 反斜杠必须先转义
                .replace("\"", "\\\"")  // 转义双引号
                .replace("\n", "\\n")   // 转义换行符
                .replace("\r", "\\r")   // 转义回车符
                .replace("\t", "\\t");  // 转义制表符
    }

    /**
     * 为JSON替换转义字符串
     * 处理整个查询字符串在JSON中替换时的转义问题
     */
    private String escapeForJsonReplacement(String value) {
        if (value == null) {
            return "";
        }

        // 对于已经构建好的查询字符串，只需要转义可能破坏JSON结构的字符
        return value
                .replace("\\", "\\\\")  // 转义反斜杠
                .replace("\"", "\\\""); // 转义双引号
    }
}

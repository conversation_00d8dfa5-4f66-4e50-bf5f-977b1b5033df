package com.semptian.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.semptian.common.MetricNameEnum;
import com.semptian.common.PanelMetricMapping;
import com.semptian.common.SystemEnum;
import com.semptian.component.I18nUtils;
import com.semptian.entity.BusinessMetricConfig;
import com.semptian.entity.OpsApiAccessResult;
import com.semptian.model.metrics.DailyTrendItem;
import com.semptian.model.metrics.DetailItem;
import com.semptian.model.metrics.HomePageTop10;
import com.semptian.model.metrics.MetricResponse;
import com.semptian.service.BusinessMetricConfigService;
import com.semptian.service.InterSystemDataAnalysisService;
import com.semptian.service.OpsApiAccessResultService;
import com.semptian.util.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InterSystemDataAnalysisServiceImpl implements InterSystemDataAnalysisService {
    @Resource
    private BusinessMetricConfigService businessMetricConfigService;

    @Resource
    private OpsApiAccessResultService opsApiAccessResultService;

    public InterSystemDataAnalysisServiceImpl() {
    }

    @Override
    public List<MetricResponse> getMetrics(int page, int size, String type, LocalDate startDate, LocalDate endDate) {
        log.debug("[指标查询] 方法入参 page={} size={} type={} startDate={} endDate={}", page, size, type, startDate, endDate);
        long queryStart = System.currentTimeMillis();
        try {
            List<MetricResponse> metricResponses = new Vector<>();
            if (startDate.isAfter(endDate)) {
                throw new IllegalArgumentException("开始日期不能晚于结束日期");
            }
            if (startDate.isBefore(LocalDate.now().minusYears(1))) {
                throw new IllegalArgumentException("仅支持查询一年内的数据");
            }
            //根据条件查询指标配置
            Page<BusinessMetricConfig> businessMetricConfigPage = businessMetricConfigService.getMetricConfigList(page, size, type, null, "ACTIVE");
            long queryEnd = System.currentTimeMillis();
            log.debug("[指标查询] 数据库查询耗时 {}ms 获取到{}条配置", queryEnd - queryStart, businessMetricConfigPage.getRecords().size());
            CountDownLatch latch = new CountDownLatch(businessMetricConfigPage.getRecords().size());
            businessMetricConfigPage.getRecords().forEach(metricConfig -> ThreadPoolUtil.getDayExecutorInstance().execute(() -> {
                try {
                    log.debug("处理指标配置: {}", metricConfig.getMetricName());
                    //根据指标配置补全对比的系统信息
                    Double overallDiffRate = opsApiAccessResultService.calculateDiffRate(metricConfig.getId(), startDate, endDate);
                    //计算关联系统
                    List<MetricResponse.RelatedSystem> relatedSystems = new ArrayList<>();
                    String[] relatedSystem = metricConfig.getCompareSystems().split(",");
                    for (String systemName : relatedSystem) {
                        relatedSystems.add(MetricResponse.RelatedSystem.builder().appId(SystemEnum.fromName(systemName).getAppId()).value(SystemEnum.fromName(systemName).getEnglishName()).build());
                    }
                    //构建MetricResponse对象
                    MetricResponse metricResponse = MetricResponse.builder().metricId(metricConfig.getId()).name(metricConfig.getMetricName()).relatedSystems(relatedSystems).description(metricConfig.getDescription()).monitorType(metricConfig.getMetricModelName()).threshold(metricConfig.getThreshold()).overallDiffRate(overallDiffRate).isOverThreshold(overallDiffRate > metricConfig.getThreshold()).build();
                    metricResponses.add(metricResponse);
                } finally {
                    latch.countDown();
                }
            }));
            try {
                latch.await();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("并发查询被中断", e);
            }
            //在此处进行国际化
            metricResponses.forEach(metricResponse -> {
                //获取指标名称枚举并记录日志
                MetricNameEnum metricNameEnum = MetricNameEnum.fromName(metricResponse.getName());
                log.debug("指标名称枚举: {}", metricNameEnum);

                //获取监控类型枚举并记录日志
                MetricNameEnum monitorTypeEnum = MetricNameEnum.fromName(metricResponse.getMonitorType());
                log.debug("监控类型枚举: {}", monitorTypeEnum);
                String name = I18nUtils.getMessage(metricNameEnum.getI18nKey());
                String monitorType = I18nUtils.getMessage(monitorTypeEnum.getI18nKey());
                metricResponse.setName(name);
                metricResponse.setMonitorType(monitorType);
            });
            //metricResponses按照metricId排序
            metricResponses.sort(Comparator.comparing(MetricResponse::getMetricId));
            log.debug("[指标查询] 数据转换完成，生成{}个响应对象", metricResponses.size());
            return metricResponses;

        } catch (Exception e) {
            log.error("[指标查询] 处理异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<DailyTrendItem> getDailyTrend(String metricId, LocalDate startDate, LocalDate endDate) {
        log.debug("[日趋势] 方法入参 metricId={} startDate={} endDate={}", metricId, startDate, endDate);
        long start = System.currentTimeMillis();
        try {
            // 暂时模拟 startDate 到 endDate 区间数据进行返回
            List<DailyTrendItem> dailyTrendItems = new ArrayList<>();
            if (startDate.isEqual(endDate)) {
                // 如果不跨天，则这里可根据实际需求实现每一小时数据的模拟，当前示例仍以天为单位模拟
                Double overallDiffRate = opsApiAccessResultService.calculateDiffRate(Long.valueOf(metricId), startDate, endDate);
                dailyTrendItems.add(DailyTrendItem.builder().date(startDate).overAllDiffRate(overallDiffRate).build());
            } else {
                // 如果跨天，则返回区间内的每天数据
                for (LocalDate date = startDate; date.isBefore(endDate.plusDays(1)); date = date.plusDays(1)) {
                    // 查询真实日趋势数据
                    Double overallDiffRate = opsApiAccessResultService.calculateDiffRate(Long.valueOf(metricId), date, date);
                    dailyTrendItems.add(DailyTrendItem.builder().date(date).overAllDiffRate(overallDiffRate).build());
                }
            }
            log.debug("日趋势数据转换完成，生成{}个数据点", dailyTrendItems.size());
            return dailyTrendItems;
        } catch (Exception e) {
            log.error("[日趋势] 处理异常: {}", e.getMessage(), e);
            throw e;
        } finally {
            log.debug("[日趋势] 总耗时 {}ms", System.currentTimeMillis() - start);
        }
    }

    @Override
    public Page<DetailItem> getDetails(String metricId, int page, int size, String account, String sort, boolean desc, LocalDate startDate, LocalDate endDate) {
        log.debug("[差异明细] 方法入参 metricId={} page={} size={} account={} sort={} desc={}", metricId, page, size, account, sort, desc);
        // 检查日期范围是否合理
        if (startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }
        if (startDate.isBefore(LocalDate.now().minusYears(1))) {
            throw new IllegalArgumentException("仅支持查询一年内的数据");
        }
        long current = System.currentTimeMillis();
        // 查询全部数据 当sort是有效系统appid时，SQL不排序，使用CountDownLatch进行并发按天查询
        List<OpsApiAccessResult> allResults = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch latch = new CountDownLatch((int) ChronoUnit.DAYS.between(startDate, endDate) + 1);
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            final LocalDate queryDate = date;
            ThreadPoolUtil.getDayExecutorInstance().execute(() -> {
                try {
                    Page<OpsApiAccessResult> dayPage = opsApiAccessResultService.pageQuery(Long.valueOf(metricId), new Page<>(1, -1), account, null, desc, queryDate, queryDate);
                    allResults.addAll(dayPage.getRecords());
                } catch (Exception e) {
                    log.error("[差异明细] 查询异常: {}", e.getMessage(), e);
                } finally {
                    latch.countDown();
                }
            });
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("并发查询被中断", e);
        }
        // 将并发查询结果封装到Page对象中
        Page<OpsApiAccessResult> fullResultPage = new Page<>();
        fullResultPage.setRecords(allResults);
        fullResultPage.setTotal(allResults.size());
        // 内存分页计算
        int startIndex = (page - 1) * size;
        BusinessMetricConfig metricConfig = businessMetricConfigService.getById(metricId);
        List<SystemEnum> validSystems = Arrays.stream(metricConfig.getCompareSystems().split(",")).map(SystemEnum::fromName).collect(Collectors.toList());
        log.debug("[差异明细] 查询耗时 {}ms 获取到{}条原始数据", System.currentTimeMillis() - current, fullResultPage.getRecords().size());
        List<DetailItem> detailItems = fullResultPage.getRecords().parallelStream().collect(Collectors.groupingByConcurrent(OpsApiAccessResult::getAccount)).entrySet().parallelStream().map(entry -> {
            String accountTmp = entry.getKey();
            List<OpsApiAccessResult> accountResults = entry.getValue();
            // 预过滤当前账号的数据
            List<OpsApiAccessResult> filteredResults = allResults.stream().filter(result -> result.getAccount().equals(accountTmp)).collect(Collectors.toList());
            Double accountDiffRate = opsApiAccessResultService.calculateAccountDiffRate(Long.valueOf(metricId), accountTmp, startDate, endDate, filteredResults);
            // 使用预计算的系统映射
            List<DetailItem.SystemData> systemMap = validSystems.stream().map(validSystem -> {
                //这里需要将每天的数据相加,如果为空则显示为-
                List<OpsApiAccessResult> systemResults = accountResults.stream().filter(result -> result.getSystemName().equals(validSystem.getChineseName())).collect(Collectors.toList());
                long value = systemResults.isEmpty() ? 99999999L : systemResults.stream().mapToLong(OpsApiAccessResult::getStatCount).sum();
                String lastValue = value == 99999999L ? "-" : String.valueOf(value);
                return DetailItem.SystemData.builder().appid(validSystem.getAppId()).value(lastValue).numericValue(value == 99999999L ? 0L : value).build();
            }).collect(Collectors.toList());

            return DetailItem.builder().systems(systemMap).account(accountTmp).singleDiffRate(accountDiffRate).build();
        }).collect(Collectors.toList());
        // 处理系统appid排序
        String finalSort = sort;
        boolean isSystemSort = validSystems.stream().anyMatch(sys -> (sys.getAppId() + "").equals(finalSort));
        if (StringUtils.isEmpty(sort)) sort = "singleDiffRate";
        if ("singleDiffRate".equals(sort)) {
            Comparator<DetailItem> comparator = Comparator.comparing(DetailItem::getSingleDiffRate);
            if (desc) {
                comparator = comparator.reversed();
            }
            detailItems = detailItems.stream().sorted(comparator).collect(Collectors.toList());
        } else if (isSystemSort) {
            Comparator<DetailItem> comparator = getDetailItemComparator(sort, desc);
            detailItems = detailItems.stream().sorted(comparator).collect(Collectors.toList());
        }
        // 构建内存分页对象
        Page<DetailItem> detailItemPage = new Page<>(page, size);
        detailItemPage.setRecords(detailItems.subList(startIndex, Math.min(startIndex + size, detailItems.size())));
        detailItemPage.setTotal(detailItems.size());
        return detailItemPage;
    }

    private static Comparator<DetailItem> getDetailItemComparator(String sort, boolean desc) {
        final String targetAppId = sort;
        Comparator<DetailItem> comparator = Comparator.comparing(item -> item.getSystems().stream().filter(sys -> String.valueOf(sys.getAppid()).equals(targetAppId)).findFirst().map(DetailItem.SystemData::getNumericValue).orElse(0L));
        if (desc) {
            comparator = comparator.reversed();
        }
        return comparator;
    }

    @Override
    public Object getHomepagePanel(LocalDate startDate, LocalDate endDate, int panelId) {
        log.debug("[首页展示] 方法入参 startDate={} endDate={} panelId={}", startDate, endDate, panelId);
        //如果startDate和endDate为空，则默认查询昨日数据，
        if (startDate == null) {
            startDate = LocalDate.now().minusDays(1);
        }
        if (endDate == null) {
            endDate = LocalDate.now().minusDays(1);
        }
        long metricId = PanelMetricMapping.getMetricIdByPanelId(panelId);
        //根据panelId查询指标配置
        BusinessMetricConfig metricConfig = businessMetricConfigService.getById(metricId);
        //计算系统间一致率
        Double overallDiffRate = opsApiAccessResultService.calculateDiffRate(metricId, startDate, endDate);
        //计算差异明细
        Page<DetailItem> detailItemPage = getDetails(String.valueOf(metricId), 1, 10, null, "singleDiffRate", true, startDate, endDate);
        //detailItemPage中的差异率/100
        detailItemPage.getRecords().forEach(detailItem -> detailItem.setSingleDiffRate(detailItem.getSingleDiffRate() / 100));
        //组装结果信息
        return HomePageTop10.builder().detailItemPage(detailItemPage).overallDiffRate(overallDiffRate).metricName(metricConfig.getMetricName()).build();
    }
}
package com.semptian.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.semptian.entity.BusinessSysConfig;

import java.util.List;

public interface BusinessSysConfigService {
    Page<BusinessSysConfig> getSysConfigList(int pageNum, int pageSize, String sysName, String sysType, Boolean enable);

    // Get system configuration by ID
    BusinessSysConfig getSysConfigById(Long id);

    List<BusinessSysConfig> getSysConfigBySysNameAndMetricId(String[] compareSystems, Integer metricId);
}
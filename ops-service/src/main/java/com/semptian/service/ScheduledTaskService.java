package com.semptian.service;

import com.semptian.config.ScheduledTaskConfig;
import com.semptian.entity.OpsApiAccessResultDifference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * 定时调度服务
 * 
 * <AUTHOR>
 * @since 2025/1/21
 */
@Slf4j
@Service
public class ScheduledTaskService {

    @Resource
    private InterSystemDataCompareService interSystemDataCompareService;

    @Resource
    private DetailExportService detailExportService;

    @Resource
    private BusinessMetricConfigService businessMetricConfigService;

    @Resource
    private ScheduledTaskConfig scheduledTaskConfig;

    /**
     * 每天上午12点执行定时任务
     * 1. 获取昨日所有指标明细差异
     * 2. 导出昨日所有指标HBase明细数据
     */
    @Scheduled(cron = "0 0 12 * * ?")
    @ConditionalOnProperty(name = "scheduled.tasks.daily.enabled", havingValue = "true", matchIfMissing = true)
    public void executeDaily12PMTasks() {
        // 检查配置是否启用
        if (!scheduledTaskConfig.isEnabled()) {
            log.info("定时任务已被配置禁用，跳过执行");
            return;
        }
        log.info("=== 开始执行每日12点定时任务 ===");
        
        LocalDate yesterday = LocalDate.now().minusDays(1);
        log.info("处理日期: {}", yesterday);

        try {
            // 任务1：获取昨日所有指标明细差异
            executeTask1_GetAllMetricsDifference(yesterday);
            
            // 任务2：导出昨日所有指标HBase明细数据
            executeTask2_ExportAllMetricsHBaseDetail(yesterday);
            
            log.info("=== 每日12点定时任务执行完成 ===");
            
        } catch (Exception e) {
            log.error("=== 每日12点定时任务执行失败 ===", e);
        }
    }

    /**
     * 任务1：获取昨日所有指标明细差异
     */
    private void executeTask1_GetAllMetricsDifference(LocalDate targetDate) {
        log.info("--- 开始执行任务1：获取昨日所有指标明细差异 ---");
        
        try {
            // 获取所有指标ID列表
            List<Integer> allMetricIds = getAllMetricIds();
            log.info("找到指标数量: {}", allMetricIds.size());
            
            if (allMetricIds.isEmpty()) {
                log.warn("没有找到任何指标配置，跳过差异分析任务");
                return;
            }

            int successCount = 0;
            int failCount = 0;

            // 遍历所有指标，执行差异分析
            for (Integer metricId : allMetricIds) {
                try {
                    log.info("开始处理指标[{}]的差异分析", metricId);

                    // 调用差异分析服务
                    List<OpsApiAccessResultDifference> differences = interSystemDataCompareService.compareResult(
                        metricId, targetDate, targetDate, null);

                    log.info("指标[{}]差异分析完成，发现差异数量: {}", metricId, differences.size());
                    successCount++;

                } catch (Exception e) {
                    log.error("指标[{}]差异分析失败", metricId, e);
                    failCount++;
                }
            }
            
            log.info("--- 任务1执行完成 | 成功: {} | 失败: {} ---", successCount, failCount);
            
        } catch (Exception e) {
            log.error("--- 任务1执行异常 ---", e);
            throw e;
        }
    }

    /**
     * 任务2：导出昨日所有指标HBase明细数据
     */
    private void executeTask2_ExportAllMetricsHBaseDetail(LocalDate targetDate) {
        log.info("--- 开始执行任务2：导出昨日所有指标HBase明细数据 ---");
        
        try {
            // 调用明细导出服务，传入null表示导出所有指标
            String result = detailExportService.exportHBaseDetail(null, targetDate, targetDate);
            
            log.info("--- 任务2执行完成 | 结果: {} ---", result);
            
        } catch (Exception e) {
            log.error("--- 任务2执行异常 ---", e);
            throw e;
        }
    }

    /**
     * 获取所有指标ID列表
     */
    private List<Integer> getAllMetricIds() {
        try {
            // 从业务指标配置服务获取所有指标ID
            return businessMetricConfigService.getAllMetricIds();
        } catch (Exception e) {
            log.error("获取所有指标ID失败", e);
            throw new RuntimeException("获取指标ID列表失败", e);
        }
    }

    /**
     * 手动触发定时任务（用于测试）
     */
    public void manualTriggerDailyTasks() {
        log.info("=== 手动触发每日定时任务 ===");
        executeDaily12PMTasks();
    }

    /**
     * 手动触发指定日期的任务
     */
    public void manualTriggerTasksForDate(LocalDate targetDate) {
        log.info("=== 手动触发指定日期[{}]的定时任务 ===", targetDate);

        try {
            // 任务1：获取指定日期所有指标明细差异
            executeTask1_GetAllMetricsDifference(targetDate);

            // 任务2：导出指定日期所有指标HBase明细数据
            executeTask2_ExportAllMetricsHBaseDetail(targetDate);

            log.info("=== 指定日期[{}]定时任务执行完成 ===", targetDate);

        } catch (Exception e) {
            log.error("=== 指定日期[{}]定时任务执行失败 ===", targetDate, e);
            throw e;
        }
    }

    /**
     * 获取定时任务配置信息
     */
    public ScheduledTaskConfig getTaskConfig() {
        return scheduledTaskConfig;
    }

    /**
     * 检查定时任务是否启用
     */
    public boolean isTaskEnabled() {
        return scheduledTaskConfig.isEnabled();
    }
}

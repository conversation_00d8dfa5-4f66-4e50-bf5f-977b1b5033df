package com.semptian.aspect;

import com.semptian.base.util.Md5Util;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Mock数据拦截器（mybatis的方法自动mock，无需注解）
 *
 * <AUTHOR>
 * &#064;date  2025/2/20 下午12:27
 */
@Aspect
@Component
public class MockDataRecorderAspect {
    @Value("${inter_system.is_use_mock:false}")
    private boolean isUseMock;

    /**
     * 定义切入点，拦截带有OperateLog注解的方法
     */
    @Pointcut("@annotation(com.semptian.annotation.OperateMock)")
    public void requestMapping() {
    }

    /**
     * 定义切入点，拦截MyBatis-Plus Service包下的方法
     */
    @Pointcut("execution(* com.baomidou.mybatisplus.extension.service..*.*(..))")
    public void mybatisPlusServiceMethods() {
    }

    /**
     * 拦截带有OperateMock注解的方法(Impl中自定义的方法)
     */
    @Around("requestMapping()")
    public Object aroundRequestMapping(ProceedingJoinPoint joinPoint) throws Throwable {
        //拿到方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        //signature.getDeclaringType()拿到方法所在类,即Impl类的class对象
        return recordMockData(joinPoint, "Mock数据记录失败", signature.getDeclaringType().getSimpleName());
    }

    /**
     * 拦截MyBatis-Plus Service包下的方法(Impl实现的IService接口中的方法)
     */
    @Around("mybatisPlusServiceMethods()")
    public Object aroundMybatisPlusService(ProceedingJoinPoint joinPoint) throws Throwable {
        //业务代码中写的是：Impl对象.方法，由于进行AOP切面，实际是Impl的CGLIB代理对象.方法 进行调用
        //joinPoint.getTarget()，获取被代理的原始对象，即xxxImpl，而非xxx$$EnhancerBySpringCGLIB$$a0a26464的代理对象
        //再获取原始对象的class对象,再获取类名
        return recordMockData(joinPoint, "MyBatis-Plus Service方法Mock数据记录失败", joinPoint.getTarget().getClass().getSimpleName());
    }

    /**
     * 共用的Mock数据记录方法
     *
     * @param joinPoint    切入点
     * @param errorMessage 错误消息前缀
     * @return 方法执行结果
     * @throws Throwable 如果方法执行失败
     */
    private Object recordMockData(ProceedingJoinPoint joinPoint, String errorMessage, String className) throws Throwable {
        if (!isUseMock) {
            return joinPoint.proceed();
        }
        Object result = joinPoint.proceed();

        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            String methodName = signature.getName();

            // 构建数据结构
            Map<String, Object> record = new HashMap<>();
            record.put("class", className);
            record.put("method", methodName);
            record.put("timestamp", LocalDateTime.now().toString());
            record.put("args", joinPoint.getArgs());
            record.put("result", result);
            // 获取方法参数类型
            Class<?>[] parameterTypes = signature.getParameterTypes();
            String[] parameterTypeNames = new String[parameterTypes.length];
            for (int i = 0; i < parameterTypes.length; i++) {
                parameterTypeNames[i] = parameterTypes[i].getName();
            }
            record.put("argsType", parameterTypeNames);

            //如果args的值长度小于10，则直接使用，否则使用md5
            String argsMd5 = Arrays.toString(joinPoint.getArgs()).length() < 10 ? Arrays.toString(joinPoint.getArgs()) : Md5Util.getMD5(Arrays.toString(joinPoint.getArgs()));

            //如果该方法已经生成了mock文件，则不再生成
            Path mockFilePath = StaticMethodRecorderProxy.getMockFilePath(StaticMethodRecorderProxy.BASE_DIR, className, methodName, argsMd5);
            if (Files.exists(mockFilePath)) {
                System.out.println("该方法已经生成了mock文件，不再生成");
                return result;
            }

            StaticMethodRecorderProxy.writeRecord(className, methodName, argsMd5, record);

        } catch (Exception e) {
            System.err.println(errorMessage + e.getMessage());
        }
        return result;
    }

}

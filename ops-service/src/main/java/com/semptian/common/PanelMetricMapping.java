package com.semptian.common;

import lombok.Getter;

@Getter
public enum PanelMetricMapping {
    PANEL_1(1, 7), PANEL_2(2, 4), PANEL_3(3, 13);

    private final int panelId;
    private final int metricId;

    PanelMetricMapping(int panelId, int metricId) {
        this.panelId = panelId;
        this.metricId = metricId;
    }

    public static int getMetricIdByPanelId(int panelId) {
        for (PanelMetricMapping mapping : values()) {
            if (mapping.getPanelId() == panelId) {
                return mapping.getMetricId();
            }
        }
        throw new IllegalArgumentException("未找到对应的 panelId: " + panelId);
    }
}
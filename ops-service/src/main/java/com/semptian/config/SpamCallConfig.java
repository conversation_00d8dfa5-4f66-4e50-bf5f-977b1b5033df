package com.semptian.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 骚扰号码库配置类
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Component
@ConfigurationProperties(prefix = "spam-call")
public class SpamCallConfig {

    /**
     * 骚扰号码库数据库名称，默认为deye_basic_library
     */
    private String databaseName = "deye_basic_library";

    /**
     * 布隆过滤器预期元素数量，默认10万
     */
    private int expectedInsertions = 100000;

    /**
     * 布隆过滤器误判率，默认1%
     */
    private double falsePositiveProbability = 0.01;

    /**
     * 缓存过期时间（小时），默认2小时
     */
    private int cacheExpirationHours = 2;

    /**
     * 热点缓存最大大小，默认1000
     */
    private int hotCacheMaxSize = 1000;

    /**
     * 分批查询大小，默认1000
     */
    private int batchSize = 1000;
}

package com.semptian.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 定时任务配置类
 *
 * <AUTHOR>
 * @since 2025/1/21
 */
@Data
@Component
@ConfigurationProperties(prefix = "scheduled.tasks.daily")
public class ScheduledTaskConfig {

    /**
     * Cron表达式
     */
    private String cron = "0 0 12 * * ?";

    /**
     * 是否启用定时任务
     */
    private boolean enabled = true;

    /**
     * 任务描述
     */
    private String description = "每天上午12点自动执行：1.获取昨日所有指标明细差异 2.导出昨日所有指标HBase明细数据";
}

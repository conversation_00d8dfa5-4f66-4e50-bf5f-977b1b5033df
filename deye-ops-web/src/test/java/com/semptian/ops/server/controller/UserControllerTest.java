/*
package com.semptian.ops.server.controller;

import com.google.common.collect.Lists;
import com.semptian.entity.UserEntity;
import com.semptian.mapper.UserMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
public class UserControllerTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private UserMapper userMapper;

    private Map<String, Object> resultMapAll = new HashMap<>();

    private Map<String, Object> resultMapBasic = new HashMap<>();


    private UserEntity userEntity = new UserEntity();
    */
/**
     * web项目上下文
     *//*

    @Autowired
    private WebApplicationContext webApplicationContext;


    @BeforeEach
    void setup() {
        mvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        userEntity.setId(1L);
        userEntity.setAccount("lijianhua");
        userEntity.setUserName("lijianhua");
        userEntity.setPassword("da5fc89f630fbdb5f46b11bcdb414f4c");
        userEntity.setCreateTime(1741332271518L);
        userEntity.setModifyTime(1741332271518L);

        resultMapBasic.put("id", userEntity.getId());
        resultMapBasic.put("userName", userEntity.getUserName());
        resultMapBasic.put("account", userEntity.getAccount());
        resultMapBasic.put("userState", 1);
        resultMapBasic.put("isAdmin", 1);
        resultMapBasic.put("userImg", "http://192.168.80.158:9000/view/2020/11/14/*************.png");
        resultMapBasic.put("loginNum", 381);
        resultMapBasic.put("lastOrgId", "-1");
        resultMapBasic.put("gender", 1);
        resultMapBasic.put("create_time", userEntity.getCreateTime());
        resultMapBasic.put("expiration", 0);
        resultMapBasic.put("heartbeatInterval", 30000);
        resultMapBasic.put("positionIds", Collections.singletonList(0));
        resultMapBasic.put("isSimple", true);

        resultMapAll.put("id", userEntity.getId());
        resultMapAll.put("userName", userEntity.getUserName());
        resultMapAll.put("account", userEntity.getAccount());
        resultMapAll.put("userState", 1);
        resultMapAll.put("isAdmin", 1);
        resultMapAll.put("userImg", "http://192.168.80.158:9000/view/2020/11/14/*************.png");
        resultMapAll.put("loginNum", 381);
        resultMapAll.put("lastOrgId", "-1");
        resultMapAll.put("gender", 1);
        resultMapAll.put("create_time", userEntity.getCreateTime());
        resultMapAll.put("expiration", 0);
        resultMapAll.put("heartbeatInterval", 30000);
        resultMapAll.put("positionIds", Collections.singletonList(0));
        resultMapAll.put("isSimple", true);

        // 添加额外的信息
        resultMapAll.put("finger", new ArrayList<>());
        resultMapAll.put("portalMenuList", Lists.newArrayList());
        resultMapAll.put("portalOperateList", Lists.newArrayList());
        resultMapAll.put("postIds", Collections.singletonList(0));
        resultMapAll.put("orgs", new ArrayList<>());
        resultMapAll.put("userRoleId", Collections.singletonList("1"));
        resultMapAll.put("userRole", Collections.singletonList("SuperAdmin"));
        resultMapAll.put("manageOrgs", new ArrayList<>());

    }

    //单元测试 isBasic为0 获取全量信息
    @Test
    public void query_all() throws Exception {
        when(userMapper.selectById(1L)).thenReturn(userEntity);
        mvc.perform(get("/user/query_current_user.json?lang=zh_CN&token=test-token&isBasicInfo=0")
                        .header("userId", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.msg").value("响应成功"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.userName").value("lijianhua"))
                .andExpect(jsonPath("$.data.account").value("lijianhua"))
                .andExpect(jsonPath("$.data.userState").value(1))
                .andExpect(jsonPath("$.data.isAdmin").value(1))
                .andExpect(jsonPath("$.data.userImg").value("http://192.168.80.158:9000/view/2020/11/14/*************.png"))
                .andExpect(jsonPath("$.data.loginNum").value(381))
                .andExpect(jsonPath("$.data.lastOrgId").value("-1"))
                .andExpect(jsonPath("$.data.gender").value(1))
                .andExpect(jsonPath("$.data.create_time").value(1741332271518L))
                .andExpect(jsonPath("$.data.expiration").value(0))
                .andExpect(jsonPath("$.data.heartbeatInterval").value(30000))
                .andExpect(jsonPath("$.data.positionIds[0]").value(0))
                .andExpect(jsonPath("$.data.isSimple").value(true))
                .andExpect(jsonPath("$.data.finger").isEmpty())
                .andExpect(jsonPath("$.data.portalMenuList").isEmpty())
                .andExpect(jsonPath("$.data.portalOperateList").isEmpty())
                .andExpect(jsonPath("$.data.postIds[0]").value(0))
                .andExpect(jsonPath("$.data.orgs").isEmpty())
                .andExpect(jsonPath("$.data.userRoleId[0]").value("1"))
                .andExpect(jsonPath("$.data.userRole[0]").value("SuperAdmin"))
                .andExpect(jsonPath("$.data.manageOrgs").isEmpty())
                .andExpect(jsonPath("$.params").isEmpty())
                .andExpect(jsonPath("$.traceId").isEmpty());
    }


    //单元测试 isBasic为1 获取基础信息
    @Test
    public void query_basic() throws Exception {
        when(userMapper.selectById(1L)).thenReturn(userEntity);
        mvc.perform(get("/user/query_current_user.json?lang=zh_CN&token=test-token&isBasicInfo=1")
                        .header("userId", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.msg").value("响应成功"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.userName").value("lijianhua"))
                .andExpect(jsonPath("$.data.account").value("lijianhua"))
                .andExpect(jsonPath("$.data.userState").value(1))
                .andExpect(jsonPath("$.data.isAdmin").value(1))
                .andExpect(jsonPath("$.data.userImg").value("http://192.168.80.158:9000/view/2020/11/14/*************.png"))
                .andExpect(jsonPath("$.data.loginNum").value(381))
                .andExpect(jsonPath("$.data.lastOrgId").value("-1"))
                .andExpect(jsonPath("$.data.gender").value(1))
                .andExpect(jsonPath("$.data.create_time").value(1741332271518L))
                .andExpect(jsonPath("$.data.expiration").value(0))
                .andExpect(jsonPath("$.data.heartbeatInterval").value(30000))
                .andExpect(jsonPath("$.data.positionIds[0]").value(0))
                .andExpect(jsonPath("$.data.isSimple").value(true))
                .andExpect(jsonPath("$.params").isEmpty())
                .andExpect(jsonPath("$.traceId").isEmpty());
    }

    //单元测试 获取用户信息失败
    @Test
    public void query_fail() throws Exception {
        when(userMapper.selectById(100L)).thenReturn(null);
        mvc.perform(get("/user/query_current_user.json?lang=zh_CN&token=test-token&isBasicInfo=1")
                        .header("userId", "100"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(80060))
                .andExpect(jsonPath("$.msg").value("用户不存在!"))
                .andExpect(jsonPath("$.data").isEmpty())
                .andExpect(jsonPath("$.params").isEmpty())
                .andExpect(jsonPath("$.traceId").isEmpty());
    }


}
*/

package com.semptian.ops.server.controller;

import com.semptian.entity.UserEntity;
import com.semptian.service.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Map;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
public class LoginControllerTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private UserService userService;

    private Map<String, Object> loginData = new java.util.HashMap<>();

    private UserEntity userEntity = new UserEntity();
    /**
     * web项目上下文
     */
    @Autowired
    private WebApplicationContext webApplicationContext;


    @BeforeEach
    void setup() {
        // 在这里可以进行测试前的准备工作
        mvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        loginData.put("positionIds", new ArrayList<>());
        loginData.put("redirectUrl", "");
        loginData.put("positionTypes", Collections.singletonList(0));
        loginData.put("portalMenuList", new ArrayList<>());
        loginData.put("isSimple", false);
        loginData.put("lang", "0");
        loginData.put("userId", 2);
        loginData.put("account", "user1");
        loginData.put("token", "test-token");

        userEntity.setId(1L);
        userEntity.setAccount("lijianhua");
        userEntity.setUserName("lijianhua");
        userEntity.setPassword("da5fc89f630fbdb5f46b11bcdb414f4c");
        userEntity.setCreateTime(1741332271518L);
        userEntity.setModifyTime(1741332271518L);
    }

    //单元测试 测试登录成功
    @Test
    public void should_login_successfully() throws Exception {
        // 模拟登录成功的返回结果
        when(userService.selectByAccount("lijianhua"))
                .thenReturn(userEntity);
        // 构造请求体
        String requestBody = "{\"account\":\"lijianhua\",\"password\":\"da5fc89f630fbdb5f46b11bcdb414f4c\",\"appId\":70}";
        ResultActions perform = mvc.perform(post("/singleLogin/login.json")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody));
        // 执行请求并验证
        perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.msg").value("响应成功"))
                .andExpect(jsonPath("$.data.userId").value(1))
                .andExpect(jsonPath("$.data.account").value("lijianhua"))
                .andExpect(jsonPath("$.data.token").value("test-token"))
                .andExpect(jsonPath("$.data.positionIds").isEmpty())
                .andExpect(jsonPath("$.data.redirectUrl").value(""))
                .andExpect(jsonPath("$.data.positionTypes[0]").value(0))
                .andExpect(jsonPath("$.data.portalMenuList").isEmpty())
                .andExpect(jsonPath("$.data.isSimple").value(false))
                .andExpect(jsonPath("$.data.lang").value("0"))
                .andExpect(jsonPath("$.params").isEmpty())
                .andExpect(jsonPath("$.traceId").isEmpty());
    }


    //单元测试 测试登录失败
    @Test
    public void not_login_successfully() throws Exception {
        // 模拟登录失败的返回用户实体类
        when(userService.selectByAccount("lijianhua1"))
                .thenReturn(null);
        // 构造请求体
        String requestBody = "{\"account\":\"lijianhua\",\"password\":\"da5fc89f630fbdb5f46b11bcdb414f4c\",\"appId\":70}";
        ResultActions perform = mvc.perform(post("/singleLogin/login.json")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody));
        // 执行请求并验证
        perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(80060))
                .andExpect(jsonPath("$.msg").value("User does not exist!"))
                .andExpect(jsonPath("$.data").isEmpty())
                .andExpect(jsonPath("$.params").isEmpty())
                .andExpect(jsonPath("$.traceId").isEmpty());
    }

    @Test
    public void login_out_successfully() throws Exception {
        // 构造请求体
        String requestBody = "{\"account\":\"lijianhua\"}";
        ResultActions perform = mvc.perform(post("/singleLogin/login_out.json")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody));
        // 执行请求并验证
        perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.msg").value("响应成功"))
                .andExpect(jsonPath("$.data").isEmpty())
                .andExpect(jsonPath("$.params").isEmpty())
                .andExpect(jsonPath("$.traceId").isEmpty());
    }


}

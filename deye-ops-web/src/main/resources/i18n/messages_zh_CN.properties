portal.user.password.error=ç¨æ·å¯ç éè¯¯
portal.user.not_exist=ç¨æ·ä¸å­å¨ï¼
portal.user.account.error=ç¨æ·è´¦å·ç±è±æãæ°å­ãä¸åçº¿ç»æï¼é¿åº¦ä¸è¶è¿64ï¼


# Metric Names
ops.metric.name.active_count=æ´»è·æ¬¡æ°
ops.metric.name.contact_account_count=éèè´¦å·åæ¬¡æ°
ops.metric.name.radius_account_count=å³èRADIUSè´¦å·æ°é
ops.metric.name.network_access_count=ä¸ç½æ¬¡æ°
ops.metric.name.network_access_detail=ä¸ç½æç»
ops.metric.name.network_virtual_account=ä¸ç½å³èèæè´¦å·
ops.metric.name.communication_count=éä¿¡æ¬¡æ°
ops.metric.name.contact_account_detail=éèè´¦å·æç»
ops.metric.name.auth_billing=é´æè®¡è´¹
ops.metric.name.base_station_location=åºç«ä½ç½®
ops.metric.name.network_access_frequency=è®¿é®ç½ç»æ¬¡æ°
ops.metric.name.virtual_account_count=å³èèæè´¦å·æ°é
ops.metric.name.auth_record=è®¤è¯è®°å½

# Monitor Types
ops.monitor.type.email_analysis=Emailæ°æ®å·®å¼åæ
ops.monitor.type.number_analysis=å·ç æ°æ®å·®å¼åæ
ops.monitor.type.fixed_radius_analysis=Fixed RADIUSæ°æ®å·®å¼åæ
ops.monitor.type.im_analysis=IMè´¦å·æ°æ®å·®å¼åæ
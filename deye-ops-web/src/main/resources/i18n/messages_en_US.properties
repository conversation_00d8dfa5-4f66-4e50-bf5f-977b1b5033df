portal.user.password.error= User Password Error
portal.user.not_exist= User does not exist!
portal.user.account.error= User account consists of English, numbers, and underscores, with a length not exceeding 64!


# Metric Names
ops.metric.name.active_count=Active Count
ops.metric.name.contact_account_count=Contact Account Count
ops.metric.name.radius_account_count=RADIUS Account Count
ops.metric.name.network_access_count=Network Access Count
ops.metric.name.network_access_detail=Network Access Detail
ops.metric.name.network_virtual_account=Network Virtual Account
ops.metric.name.communication_count=Communication Count
ops.metric.name.contact_account_detail=Contact Account Detail
ops.metric.name.auth_billing=Authentication Billing
ops.metric.name.base_station_location=Base Station Location
ops.metric.name.network_access_frequency=Network Access Frequency
ops.metric.name.virtual_account_count=Virtual Account Count
ops.metric.name.auth_record=Authentication Record

# Monitor Types
ops.monitor.type.email_analysis=Email Data Difference Analysis
ops.monitor.type.number_analysis=Number Data Difference Analysis
ops.monitor.type.fixed_radius_analysis=Fixed RADIUS Data Difference Analysis
ops.monitor.type.im_analysis=IM Account Data Difference Analysis

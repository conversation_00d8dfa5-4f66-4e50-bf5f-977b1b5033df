server {
    #访问端口
    listen       6062;
    #服务地址
    server_name  localhost;
    #前端dist包需要放置的路径，可根据需要修改
    location ~ \.(js|css|png|jpg|swf|gif|jpeg|woff|woff2|ttf|docx|mp3|ico)$ {
        root /semptian/web/ops/dist/;
    }
    #前端dist包需要放置的路径，可根据需要修改
    location ~ \.(html)$ {
        add_header Cache-Control 'no-cache,must-revalidate,proxy-revalidate,max-age=0';
        root /semptian/web/ops/dist/;
    }
    #首页配置
    location / {
        root /semptian/web/ops/dist/;
        try_files $uri $uri/ @router;
        index index.html;
    }
    #异常配置
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   html;
    }
    #后端接口地址
     location ^~ /ops/ {
        proxy_pass http://**************:8899/ops/;
    }
}

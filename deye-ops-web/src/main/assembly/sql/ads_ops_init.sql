-- ads_ops.ops_api_access_result definition
DROP TABLE IF EXISTS `ops_api_access_result`;
CREATE TABLE `ops_api_access_result` (
  `metric_id` varchar(64) NOT NULL COMMENT '指标ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `account` varchar(200) NOT NULL COMMENT '操作账号',
  `system_name` varchar(50) NULL COMMENT '系统名称',
  `api_path` varchar(500) NULL COMMENT '接口路径',
  `request_time` datetime NULL COMMENT '请求时间',
  `response_status` smallint NULL COMMENT '响应状态码',
  `cost_time` int NULL COMMENT '耗时(ms)',
  `response_data` text NULL COMMENT '响应数据',
  `stat_count` bigint NOT NULL DEFAULT "0" COMMENT '统计结果数量',
  `is_success` boolean NULL COMMENT '是否成功',
  `error_code` varchar(50) NULL COMMENT '错误代码',
  `is_kv` boolean NULL COMMENT '是否是key-value对比，true为kv',
  `kv_content` text NULL COMMENT '响应的kv数据，json格式',
  `params` json NULL COMMENT '请求参数JSON',
  `is_dynamic` boolean NULL COMMENT '是否是动态账号',
  `redundant_field` varchar(100) NULL COMMENT '冗余字段'
) ENGINE=OLAP
UNIQUE KEY(`metric_id`, `stat_date`, `account`, `system_name`)
COMMENT 'API访问结果监控表（含统计量）'
AUTO PARTITION BY RANGE (date_trunc(`stat_date`, 'day'))
()
DISTRIBUTED BY HASH(`metric_id`, `stat_date`) BUCKETS 6
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);

-- ads_ops.ops_archive_data_monitor_statistics definition
DROP TABLE IF EXISTS `ops_archive_data_monitor_statistics`;
CREATE TABLE `ops_archive_data_monitor_statistics` (
  `data_type` varchar(20) NOT NULL COMMENT '协议名称 Website/application/phone number/radius/email/im',
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `theoretical_number` bigint NULL COMMENT '理论建档数',
  `actual_number` bigint NOT NULL COMMENT '实际建档数',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`data_type`,`capture_day`)
COMMENT '全息漏建档数统计表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`capture_day`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);

-- ads_ops.ops_archive_missing_doc_detail definition
DROP TABLE IF EXISTS `ops_archive_missing_doc_detail`;
CREATE TABLE `ops_archive_missing_doc_detail` (
  `data_type` varchar(20) NOT NULL COMMENT '协议名称 Website/application/phone number/radius/email/im',
  `capture_day` date NOT NULL COMMENT '数据捕获日期',
  `missing_doc_account` varchar(256) NULL COMMENT '漏建档账号',
  `app_type` varchar(256) NULL COMMENT '应用类型/im应用类型',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`data_type`, `capture_day`,`missing_doc_account`,`app_type`)
COMMENT '全息漏建档数详情统计表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`capture_day`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_case_date_monitor_statistics definition
DROP TABLE IF EXISTS `ops_case_date_monitor_statistics`;
CREATE TABLE `ops_case_date_monitor_statistics` (
  `clue_type` int NOT NULL COMMENT '线索类型',
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `capture_hour` tinyint NOT NULL COMMENT '统计数据创建小时',
  `clue_num` bigint NOT NULL COMMENT '线索数',
  `clue_hit_num` bigint NOT NULL COMMENT '线索命中数',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`clue_type`, `capture_day`, `capture_hour`)
COMMENT 'NF-Fixed-Radius关联率统计表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`capture_hour`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_nf_data_delay_statistics definition
DROP TABLE IF EXISTS `ops_nf_data_delay_statistics`;
CREATE TABLE `ops_nf_data_delay_statistics` (
  `data_type` varchar(20) NOT NULL COMMENT '协议类型',
  `data_layer` varchar(5) NOT NULL COMMENT '数据分层',
  `uparea_alias` varchar(6) NOT NULL COMMENT '数据上报地',
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `capture_hour` tinyint NOT NULL COMMENT '统计数据创建小时',
  `delay` int NOT NULL COMMENT '数据时延区间类型，5(0-5);10:(5-10);15(10-15);20:(15-20)',
  `num` bigint NOT NULL COMMENT '统计数',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`data_type`, `data_layer`, `uparea_alias`, `capture_day`, `capture_hour`, `delay`)
COMMENT 'nf数据延时监控表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`capture_hour`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_nf_data_monitor_statistics definition
DROP TABLE IF EXISTS `ops_nf_data_monitor_statistics`;
CREATE TABLE `ops_nf_data_monitor_statistics` (
  `data_type` varchar(20) NOT NULL COMMENT '协议类型',
  `uparea_alias` varchar(6) NOT NULL COMMENT '数据上报地',
  `data_layer` varchar(5) NOT NULL COMMENT '数据分层',
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `capture_hour` tinyint NOT NULL COMMENT '统计数据创建小时',
  `num` bigint NOT NULL COMMENT '统计数',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`data_type`, `uparea_alias`, `data_layer`, `capture_day`, `capture_hour`)
COMMENT 'nf数据监控实时表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`capture_hour`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_nf_data_monitor_statistics_store definition
DROP TABLE IF EXISTS `ops_nf_data_monitor_statistics_store`;
CREATE TABLE `ops_nf_data_monitor_statistics_store` (
  `data_type` varchar(20) NOT NULL COMMENT '协议类型',
  `uparea_alias` varchar(6) NOT NULL COMMENT '数据上报地',
  `data_layer` varchar(5) NOT NULL COMMENT '数据分层',
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `capture_hour` tinyint NOT NULL COMMENT '统计数据创建小时',
  `num` bigint NOT NULL COMMENT '统计数',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`data_type`, `uparea_alias`, `data_layer`, `capture_day`, `capture_hour`)
COMMENT 'dw层nf数据监控归档表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`capture_hour`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_nf_fixed_radius_association_statistics definition
DROP TABLE IF EXISTS `ops_nf_fixed_radius_association_statistics`;
CREATE TABLE `ops_nf_fixed_radius_association_statistics` (
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `statistics_type` varchar(20) NOT NULL COMMENT '统计值类型;Association;Accuracy',
  `auth_count` bigint NOT NULL COMMENT '上网账号数',
  `radius_count` bigint NOT NULL COMMENT 'radius账号基数',
  `statistics_value` double NOT NULL COMMENT '统计值',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`capture_day`, `statistics_type`)
COMMENT 'NF-Fixed-Radius关联率统计表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`statistics_type`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_nf_layer_diff_statistics definition
DROP TABLE IF EXISTS `ops_nf_layer_diff_statistics`;
CREATE TABLE `ops_nf_layer_diff_statistics` (
  `data_type` varchar(20) NOT NULL COMMENT '协议类型',
  `uparea_alias` varchar(6) NOT NULL COMMENT '数据上报地',
  `data_layer` varchar(20) NOT NULL COMMENT '数据分层',
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `capture_hour` tinyint NOT NULL COMMENT '统计数据创建小时',
  `diff_num` decimal(20,5) NOT NULL COMMENT '层级之间差异数',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`data_type`, `uparea_alias`, `data_layer`, `capture_day`, `capture_hour`)
COMMENT 'nf数据数仓层数据差异结果表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`capture_hour`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_nf_mobilenet_radius_association_statistics definition
DROP TABLE IF EXISTS `ops_nf_mobilenet_radius_association_statistics`;
CREATE TABLE `ops_nf_mobilenet_radius_association_statistics` (
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `operator` varchar(10) NOT NULL COMMENT '运营商;Ooredoo,Mobilis,Djezzy,Autre',
  `statistics_type` varchar(20) NOT NULL COMMENT '统计值类型;Association;Accuracy',
  `auth_count` bigint NOT NULL COMMENT '上网账号数',
  `radius_count` bigint NOT NULL COMMENT 'radius账号基数',
  `statistics_value` double NOT NULL COMMENT '统计值',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`capture_day`, `operator`, `statistics_type`)
COMMENT 'NF-Moilenet-Radius关联率统计表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`statistics_type`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_nf_radius_association_detail definition
DROP TABLE IF EXISTS `ops_nf_radius_association_detail`;
CREATE TABLE IF NOT EXISTS ops_nf_radius_association_detail(
    `capture_day` DATE  NOT NULL  COMMENT '数据日期',
    `operator` VARCHAR(10)  NOT NULL COMMENT '运营商;Ooredoo,Mobilis,Djezzy,Autre',
    `statistics_type` VARCHAR(20)  NOT NULL COMMENT '统计类型(Association/Accuracy,_1为账号异常,_2为时间异常)',
    `auth_type` VARCHAR(7) NOT NULL COMMENT  '认证类型(Mobile/Fixed)',
    `radius_ip` VARCHAR(64) COMMENT 'RADIUS IP',
    `data_type` VARCHAR(20) NOT NULL COMMENT  '数据类型',
    `radius_account` VARCHAR(128) COMMENT 'RADIUS账号',
    `radius_start_port` INT COMMENT '起始端口',
    `radius_end_port` INT COMMENT '结束端口',
    `radius_start_time` BIGINT COMMENT '会话开始时间',
    `radius_end_time` BIGINT COMMENT '会话结束时间',
    `account` VARCHAR(128) COMMENT '账号',
    `strsrc_ip` VARCHAR(64) COMMENT '源IP',
    `src_port` INT COMMENT '源端口',
    `capture_time` BIGINT NOT NULL COMMENT '捕获时间',
    `data_id` VARCHAR(64) COMMENT '数据ID',
    `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
DUPLICATE KEY(capture_day, operator, statistics_type, auth_type, radius_ip,data_type)
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(radius_ip) BUCKETS 3
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********"
);


-- ads_ops.ops_pr_data_delay_statistics definition
DROP TABLE IF EXISTS `ops_pr_data_delay_statistics`;
CREATE TABLE `ops_pr_data_delay_statistics` (
  `data_type` varchar(20) NOT NULL COMMENT '协议类型',
  `original_data_type` varchar(20) NULL COMMENT 'ods层原始协议类型',
  `data_layer` varchar(5) NOT NULL COMMENT '数据分层',
  `uparea_alias` varchar(6) NOT NULL COMMENT '数据上报地',
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `capture_hour` tinyint NOT NULL COMMENT '统计数据创建小时',
  `delay` int NOT NULL COMMENT '数据时延区间类型，5(0-5);10:(5-10);15(10-15);20:(15-20)',
  `num` bigint NOT NULL COMMENT '统计数',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`data_type`, `original_data_type`, `data_layer`, `uparea_alias`, `capture_day`, `capture_hour`, `delay`)
COMMENT 'pr数据延时监控表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`capture_hour`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_pr_data_monitor_statistics definition
DROP TABLE IF EXISTS `ops_pr_data_monitor_statistics`;
CREATE TABLE `ops_pr_data_monitor_statistics` (
  `data_type` varchar(20) NOT NULL COMMENT '协议类型',
  `original_data_type` varchar(20) NULL COMMENT 'ods层原始协议类型',
  `uparea_alias` varchar(6) NOT NULL COMMENT '数据上报地',
  `data_layer` varchar(5) NOT NULL COMMENT '数据分层',
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `capture_hour` tinyint NOT NULL COMMENT '统计数据创建小时',
  `num` bigint NOT NULL COMMENT '统计数',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`data_type`, `original_data_type`, `uparea_alias`, `data_layer`, `capture_day`, `capture_hour`)
COMMENT 'pr数据监控实时表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`capture_hour`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_pr_data_monitor_statistics_store definition
DROP TABLE IF EXISTS `ops_pr_data_monitor_statistics_store`;
CREATE TABLE `ops_pr_data_monitor_statistics_store` (
  `data_type` varchar(20) NOT NULL COMMENT '协议类型',
  `original_data_type` varchar(20) NULL COMMENT 'ods层原始协议类型',
  `uparea_alias` varchar(6) NOT NULL COMMENT '数据上报地',
  `data_layer` varchar(5) NOT NULL COMMENT '数据分层',
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `capture_hour` tinyint NOT NULL COMMENT '统计数据创建小时',
  `num` bigint NOT NULL COMMENT '统计数',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`data_type`, `original_data_type`, `uparea_alias`, `data_layer`, `capture_day`, `capture_hour`)
COMMENT 'dw层pr数据监控归档表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`capture_hour`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_pr_fixed_radius_association_statistics definition
DROP TABLE IF EXISTS `ops_pr_fixed_radius_association_statistics`;
CREATE TABLE `ops_pr_fixed_radius_association_statistics` (
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `statistics_type` varchar(20) NOT NULL COMMENT '统计值类型;Association Rate;Accuracy Rate',
  `auth_count` bigint NOT NULL COMMENT '上网账号数',
  `radius_count` bigint NOT NULL COMMENT 'radius账号基数',
  `statistics_value` double NOT NULL COMMENT '统计值',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`capture_day`, `statistics_type`)
COMMENT 'PR-Fixed-Radius关联率统计表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`statistics_type`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_pr_layer_diff_statistics definition
DROP TABLE IF EXISTS `ops_pr_layer_diff_statistics`;
CREATE TABLE `ops_pr_layer_diff_statistics` (
  `data_type` varchar(20) NOT NULL COMMENT '协议类型',
  `uparea_alias` varchar(6) NOT NULL COMMENT '数据上报地',
  `data_layer` varchar(20) NOT NULL COMMENT '数据分层',
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `capture_hour` tinyint NOT NULL COMMENT '统计数据创建小时',
  `diff_num` decimal(20,5) NOT NULL COMMENT '层级之间差异数',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`data_type`, `uparea_alias`, `data_layer`, `capture_day`, `capture_hour`)
COMMENT 'pr数据数仓层数据差异结果表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`capture_hour`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_pr_mobilenet_radius_association_statistics definition
DROP TABLE IF EXISTS `ops_pr_mobilenet_radius_association_statistics`;
CREATE TABLE `ops_pr_mobilenet_radius_association_statistics` (
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `operator` varchar(10) NOT NULL COMMENT '运营商;Ooredoo,Mobilis,Djezzy,Autre',
  `statistics_type` varchar(20) NOT NULL COMMENT '统计值类型;Association;Accuracy',
  `auth_count` bigint NOT NULL COMMENT '上网账号数',
  `radius_count` bigint NOT NULL COMMENT 'radius账号基数',
  `statistics_value` double NOT NULL COMMENT '统计值',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`capture_day`, `operator`, `statistics_type`)
COMMENT 'PR-Moilenet-Radius关联率统计表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`statistics_type`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_pr_radius_association_detail definition
DROP TABLE IF EXISTS `ops_pr_radius_association_detail`;
CREATE TABLE IF NOT EXISTS ops_pr_radius_association_detail(
    `capture_day` DATE  NOT NULL  COMMENT '数据日期',
    `operator` VARCHAR(10)  NOT NULL COMMENT '运营商;Ooredoo,Mobilis,Djezzy,Autre',
    `statistics_type` VARCHAR(20)  NOT NULL COMMENT '统计类型(Association/Accuracy,_1为账号异常,_2为时间异常)',
    `auth_type` VARCHAR(7) NOT NULL COMMENT  '认证类型(Mobile/Fixed)',
    `radius_ip` VARCHAR(64) COMMENT 'RADIUS IP',
    `data_type` VARCHAR(20) NOT NULL COMMENT  '数据类型',
    `radius_account` VARCHAR(128) COMMENT 'RADIUS账号',
    `radius_start_port` INT COMMENT '起始端口',
    `radius_end_port` INT COMMENT '结束端口',
    `radius_start_time` BIGINT COMMENT '会话开始时间',
    `radius_end_time` BIGINT COMMENT '会话结束时间',
    `account` VARCHAR(128) COMMENT '账号',
    `strsrc_ip` VARCHAR(64) COMMENT '源IP',
    `src_port` INT COMMENT '源端口',
    `capture_time` BIGINT NOT NULL COMMENT '捕获时间',
    `data_id` VARCHAR(64) COMMENT '数据ID',
    `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
DUPLICATE KEY(capture_day, operator, statistics_type, auth_type, radius_ip,data_type)
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(radius_ip) BUCKETS 3
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********"
);


-- ads_ops.ops_radius_data_delay_statistics definition
DROP TABLE IF EXISTS `ops_radius_data_delay_statistics`;
CREATE TABLE `ops_radius_data_delay_statistics` (
  `data_type` varchar(20) NOT NULL COMMENT '协议类型',
  `data_layer` varchar(5) NOT NULL COMMENT '数据分层',
  `operator` varchar(10) NULL COMMENT '运营商',
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `capture_hour` tinyint NOT NULL COMMENT '统计数据创建小时',
  `delay` int NOT NULL COMMENT '数据时延区间类型，5(0-5);10:(5-10);15(10-15);20:(15-20)',
  `num` bigint NOT NULL COMMENT '统计数',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`data_type`, `data_layer`, `operator`, `capture_day`, `capture_hour`, `delay`)
COMMENT 'radius数据延时监控表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`capture_hour`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_radius_data_monitor_statistics definition
DROP TABLE IF EXISTS `ops_radius_data_monitor_statistics`;
CREATE TABLE `ops_radius_data_monitor_statistics` (
  `data_type` varchar(20) NOT NULL COMMENT '协议类型',
  `operator` varchar(10) NULL COMMENT '运营商',
  `data_layer` varchar(5) NOT NULL COMMENT '数据分层',
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `capture_hour` tinyint NOT NULL COMMENT '统计数据创建小时',
  `num` bigint NOT NULL COMMENT '统计数',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`data_type`, `operator`, `data_layer`, `capture_day`, `capture_hour`)
COMMENT 'pr数据监控实时表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`capture_hour`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_radius_data_monitor_statistics_store definition
DROP TABLE IF EXISTS `ops_radius_data_monitor_statistics_store`;
CREATE TABLE `ops_radius_data_monitor_statistics_store` (
  `data_type` varchar(20) NOT NULL COMMENT '协议类型',
  `operator` varchar(10) NULL COMMENT '运营商',
  `data_layer` varchar(5) NOT NULL COMMENT '数据分层',
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `capture_hour` tinyint NOT NULL COMMENT '统计数据创建小时',
  `num` bigint NOT NULL COMMENT '统计数',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`data_type`, `operator`, `data_layer`, `capture_day`, `capture_hour`)
COMMENT 'pr数据监控实时表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`capture_hour`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


-- ads_ops.ops_radius_layer_diff_statistics definition
DROP TABLE IF EXISTS `ops_radius_layer_diff_statistics`;
CREATE TABLE `ops_radius_layer_diff_statistics` (
  `data_type` varchar(20) NOT NULL COMMENT '协议类型',
  `data_layer` varchar(20) NOT NULL COMMENT '数据分层',
  `capture_day` date NOT NULL COMMENT '统计数据计算日期',
  `capture_hour` tinyint NOT NULL COMMENT '统计数据创建小时',
  `diff_num` decimal(20,5) NOT NULL COMMENT '层级之间差异数',
  `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
UNIQUE KEY(`data_type`, `data_layer`, `capture_day`, `capture_hour`)
COMMENT 'radius数据数仓层数据差异结果表'
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(`capture_hour`) BUCKETS 1
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);
/*
 Navicat Premium Dump SQL

 Source Server         : *************
 Source Server Type    : MySQL
 Source Server Version : 50719 (5.7.19-log)
 Source Host           : *************:3306
 Source Schema         : deye_ops

 Target Server Type    : MySQL
 Target Server Version : 50719 (5.7.19-log)
 File Encoding         : 65001

 Date: 02/04/2025 11:35:26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tb_ops_monitor_metric_sql
-- ----------------------------
DROP TABLE IF EXISTS `tb_ops_monitor_metric_sql`;
CREATE TABLE `tb_ops_monitor_metric_sql`  (
  `id` bigint(13) NOT NULL COMMENT '主键',
  `sql_content` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'sql语句',
  `sql_desc` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'sql功能描述',
  `resource_conf` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '待执行的spark_sql资源配置json',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '监控指标项关联sql表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_ops_monitor_metric_sql
-- ----------------------------
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (1, 'WITH daily_count AS ( SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, \\\"\\\" as uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_voip_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"), 1) AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, \\\"\\\" as uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_voip_fax_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"), 1) AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, \\\"\\\" as uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_call_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"), 1) AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") )SELECT num, data_type, original_data_type, uparea_alias, data_layer, insert_day AS capture_day, insert_hour AS capture_hour FROM daily_count WHERE insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\";', '统计ODS层Call/SMS/Fax协议数据量天任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (2, 'WITH daily_count as   ( SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_http_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1) AND t.capture_hour BETWEEN 20 AND 23)  GROUP BY t.data_type , t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") ) SELECT data_type, original_data_type, uparea_alias, data_layer, insert_day AS capture_day, insert_hour AS capture_hour, num FROM daily_count WHERE insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\";', '统计pr中ods层http协议的数据量天任务', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (3, 'with time_diff AS ( SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_http_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" ) SELECT COUNT(1) as num, time_diff.delay as delay, time_diff.data_type as data_type, time_diff.data_type as original_data_type,time_diff.uparea_alias as uparea_alias, \\\"ODS\\\" as data_layer, time_diff.capture_day as capture_day, time_diff.capture_hour as capture_hour FROM time_diff GROUP BY delay, data_type,uparea_alias, capture_day, capture_hour;', '统计ODS层PR-Http协议每条数据时延天任务', '{\r\n    \"spark.driver.memory\": \"8g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (4, 'WITH daily_count AS  (SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer,   from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day ,   from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour   FROM v64_deye_dw_ods.ods_pr_im_store t   WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1) AND t.capture_hour BETWEEN 20 AND 23)   GROUP BY t.data_type,t.uparea_id,from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\")   UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day ,   from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour   FROM v64_deye_dw_ods.ods_pr_vpn_store t   WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1) AND t.capture_hour BETWEEN 20 AND 23)   GROUP BY t.data_type,t.uparea_id,from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\")   UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day ,   from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour   FROM v64_deye_dw_ods.ods_pr_overnet_store t   WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1) AND t.capture_hour BETWEEN 20 AND 23)   GROUP BY t.data_type,t.uparea_id,from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\")   UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day ,   from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour   FROM v64_deye_dw_ods.ods_pr_remotectrl_store t   WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1) AND t.capture_hour BETWEEN 20 AND 23)   GROUP BY t.data_type,t.uparea_id,from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\"))SELECT data_type,original_data_type,uparea_alias,data_layer,insert_day AS capture_day,insert_hour AS capture_hour,num FROM daily_count WHERE insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\";', '统计pr中ods层im/vpn/remotectl协议的数据量天任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (5, 'with time_diff AS ( SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_vpn_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_im_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_remotectrl_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" ) SELECT COUNT(1) as num, time_diff.delay as delay, time_diff.data_type as data_type, time_diff.data_type as original_data_type,time_diff.uparea_alias as uparea_alias, \\\"ODS\\\" as data_layer, time_diff.capture_day as capture_day, time_diff.capture_hour as capture_hour FROM time_diff GROUP BY delay, data_type,uparea_alias, capture_day, capture_hour;', '统计ODS层PR-im/vpn/remotectl协议每条数据时延天任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (6, 'with daily_count as(SELECT COUNT(1) as num,t.data_type as data_type,t.data_type as original_data_type,t.uparea_id as uparea_alias,\\\"ODS\\\" as data_layer,from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_email_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND capture_hour BETWEEN LPAD(CAST (\\\"{captureStartHour}\\\" AS INT)-1, 2, \\\"0\\\") AND \\\"{captureEndHour}\\\" GROUP BY t.data_type ,t.uparea_id,from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") )SELECT data_type,original_data_type,uparea_alias,data_layer,insert_day AS capture_day, insert_hour AS capture_hour,num FROM daily_count WHERE insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND LPAD(insert_hour,2,\\\"0\\\") BETWEEN \\\"{captureStartHour}\\\" AND \\\"{captureEndHour}\\\";', '统计pr中ods层数据量小时任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"1\",\r\n    \"spark.executor.memory\": \"2G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"1\",\r\n    \"spark.default.parallelism\": \"4\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (7, 'with time_diff AS (SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type , t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_voip_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type , t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_call_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type , t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_voip_fax_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" ) SELECT COUNT(1) as num, time_diff.delay as delay, time_diff.data_type as data_type, time_diff.data_type as original_data_type,\\\"\\\" as uparea_alias, \\\"ODS\\\" as data_layer, time_diff.capture_day as capture_day, time_diff.capture_hour as capture_hour FROM time_diff GROUP BY delay, data_type, capture_day, capture_hour;', '统计Call/SMS/Fax协议数据ods每天数据时延', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (8, 'WITH time_diff AS (SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , \\\"1020004\\\" AS data_type , \\\"7\\\" AS operator, t.capture_day AS capture_day , t.capture_hour AS capture_hour FROM v64_deye_dw_ods.ods_mobilenet_radius_djezzy_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , \\\"1020004\\\" AS data_type , \\\"6\\\" AS operator, t.capture_day AS capture_day , t.capture_hour AS capture_hour FROM v64_deye_dw_ods.ods_mobilenet_radius_mobilis_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , \\\"1020004\\\" AS data_type , \\\"5\\\" AS operator, t.capture_day AS capture_day , t.capture_hour AS capture_hour FROM v64_deye_dw_ods.ods_mobilenet_radius_ooredoo_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , \\\"1020001\\\" AS data_type , \\\"99\\\" AS operator, t.capture_day AS capture_day , t.capture_hour AS capture_hour FROM v64_deye_dw_ods.ods_fixnet_radius_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\") SELECT COUNT(1) AS num, time_diff.delay as delay, time_diff.data_type AS data_type,time_diff.operator as operator, \\\"ODS\\\" AS data_layer, time_diff.capture_day AS capture_day, time_diff.capture_hour AS capture_hour FROM time_diff GROUP BY delay, data_type,operator, capture_day, capture_hour;', 'Radius数据ODS层数据时延统计', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (9, 'WITH daily_count AS   (SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_shop_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_keyapp_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_video_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_hardwarestring_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_voice_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_iot_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_webshare_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_digcurrency_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_hackertools_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_religion_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_vm_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_game_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_webbbs_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_picture_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_lbs_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_live_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_sns_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_payment_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_browser_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_navigation_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_inputmethod_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_weblogin_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_voip_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_mblog_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_govserv_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_email_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_basewifi_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_p2p_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_engine_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_webid_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_telnet_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_safety_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_news_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_ftp_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_netbank_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_financial_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_map_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_softinstall_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_appmarket_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_pr_infoserv_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"   OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"),1)   AND t.capture_hour BETWEEN 20 AND 23) GROUP BY t.data_type, t.uparea_id, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\")) SELECT data_type, original_data_type, uparea_alias, data_layer, insert_day AS capture_day, insert_hour AS capture_hour, num FROM daily_count WHERE insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\";', '统计pr中ods层其他协议的数据量天任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (10, 'WITH daily_count AS   (SELECT COUNT(1) AS num, t.data_type AS data_type, t.data_type AS original_data_type, t.uparea_id AS uparea_id, \\\"DWD\\\" AS data_layer, t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_http_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour) SELECT data_type, original_data_type, uparea_id, data_layer, insert_day AS capture_day, insert_hour AS capture_hour, num FROM daily_count', '统计pr中dwd层http协议的数据量天任务', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (11, 'WITH daily_count AS ( SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer, t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_im_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer, t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_vpn_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer, t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_remotectrl_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour) SELECT data_type, original_data_type, uparea_id, data_layer, insert_day AS capture_day, insert_hour AS capture_hour, num FROM daily_count', '统计pr中dwd层im/vpn/remotectl协议的数据量天任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (12, 'WITH daily_count AS ( SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_engine_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_entertainment_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_finance_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_ftp_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_lbs_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_multimedia_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_news_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_others_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_shopping_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_sns_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_telnet_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL  SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_email_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL  SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_terminal_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_tool_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_travel_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer,  t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_pr_voip_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\"  GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour )SELECT data_type, original_data_type, uparea_id, data_layer, insert_day AS capture_day, insert_hour AS capture_hour, num FROM daily_count', '统计pr中dwd层其他协议的数据量天任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (13, 'WITH daily_count AS ( SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.insert_day as insert_day, t.insert_hour as insert_hour FROM v64_deye_dw_dwd.dwd_pr_email_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND t.insert_hour BETWEEN \\\"{captureStartHour}\\\" AND \\\"{captureEndHour}\\\" GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour )SELECT data_type, original_data_type, uparea_alias, data_layer, insert_day AS capture_day, insert_hour AS capture_hour, num FROM daily_count', '统计pr中dwd层数据量小时任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"1\",\r\n    \"spark.executor.memory\": \"2G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"1\",\r\n    \"spark.default.parallelism\": \"4\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (18, 'WITH daily_count AS ( SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, \\\"\\\" as uparea_alias, \\\"DWD\\\" as data_layer, t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_sms_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, \\\"\\\" as uparea_alias, \\\"DWD\\\" as data_layer, t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_call_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, \\\"\\\" as uparea_alias, \\\"DWD\\\" as data_layer, t.insert_day AS insert_day, t.insert_hour AS insert_hour FROM v64_deye_dw_dwd.dwd_fax_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.insert_day, t.insert_hour) SELECT num, data_type, original_data_type, uparea_alias, data_layer, insert_day AS capture_day, insert_hour AS capture_hour FROM daily_count;', '统计DWD层Call/SMS/Fax协议数据量天任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (19, 'WITH daily_count AS ( SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_http_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour) SELECT data_type, original_data_type, uparea_alias, data_layer, capture_day, capture_hour, num FROM daily_count', '统计ods层pr中http子协议的归档数据量天任务', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (20, 'WITH daily_count AS (SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_im_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_vpn_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_overnet_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_remotectrl_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour) SELECT data_type, original_data_type,uparea_alias, data_layer, capture_day, capture_hour, num FROM daily_count', '统计ods层pr中im/vpn/remotectl子协议的归档数据量天任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (21, 'WITH daily_count AS ( SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_http_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5) GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour )SELECT data_type, original_data_type, uparea_id as uparea_alias, data_layer, capture_day, capture_hour, num FROM daily_count WHERE capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\";', '统计dwd层pr中http子协议的归档数据量天任务', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (22, 'WITH daily_count AS (SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_im_store t WHERE (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_vpn_store t WHERE (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_id, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_remotectrl_store t WHERE (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour )SELECT data_type, original_data_type, uparea_id as uparea_alias, data_layer, capture_day, capture_hour, num FROM daily_count', '统计dwd层pr中im/vpn/remotectl子协议的归档数据量天任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (23, 'WITH daily_count AS ( SELECT COUNT(1) as num, t.data_type as data_type, t.data_type AS original_data_type, \\\"\\\" as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_voip_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type AS original_data_type, \\\"\\\" as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_voip_fax_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type AS original_data_type, \\\"\\\" as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_call_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.capture_day, t.capture_hour) SELECT num, data_type, original_data_type, uparea_alias, data_layer, capture_day, capture_hour FROM daily_count;', 'Call/SMS/Fax线路ODS层归档数据统计', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (24, 'WITH daily_count AS ( SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, \\\"DWD\\\" as data_layer, \\\"\\\" as uparea_alias, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_sms_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"), 1) AND t.insert_hour BETWEEN 0 AND 5) GROUP BY t.data_type, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, \\\"DWD\\\" as data_layer, \\\"\\\" as uparea_alias, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_call_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"), 1) AND t.insert_hour BETWEEN 0 AND 5) GROUP BY t.data_type, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, \\\"DWD\\\" as data_layer, \\\"\\\" as uparea_alias, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_fax_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"), 1) AND t.insert_hour BETWEEN 0 AND 5) GROUP BY t.data_type, t.capture_day, t.capture_hour )SELECT num, data_type, original_data_type, uparea_alias, data_layer, capture_day, capture_hour FROM daily_count WHERE capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\";', 'Call/SMS/Fax线路DWD层归档数据统计', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (29, 'WITH daily_count AS ( SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"7\\\" as operator, \\\"ODS\\\"as data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_mobilenet_radius_djezzy_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"), 1) AND t.capture_hour BETWEEN 20 AND 23) GROUP BY from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\"), \\\"7\\\", \\\"1020004\\\" UNION ALL SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"6\\\" as operator, \\\"ODS\\\"as data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_mobilenet_radius_mobilis_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"), 1) AND t.capture_hour BETWEEN 20 AND 23) GROUP BY from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\"), \\\"1020004\\\", \\\"6\\\" UNION ALL SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"5\\\" as operator, \\\"ODS\\\"as data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_mobilenet_radius_ooredoo_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"), 1) AND t.capture_hour BETWEEN 20 AND 23) GROUP BY from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\"), \\\"5\\\", \\\"1020004\\\" UNION ALL SELECT COUNT(1) as num, \\\"1020001\\\" as data_type, \\\"99\\\" as operator, \\\"ODS\\\"as data_layer, from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 AS BIGINT), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_fixnet_radius_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.capture_day = DATE_SUB(TO_DATE(\\\"{captureStartDay}\\\"), 1) AND t.capture_hour BETWEEN 20 AND 23) GROUP BY from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\"), \\\"99\\\", \\\"1020001\\\" )SELECT num, data_type, operator, data_layer, insert_day AS capture_day, insert_hour AS capture_hour FROM daily_count WHERE insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\";', 'Radius线路-ODS层数据量统计天任务', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (30, 'WITH daily_count AS ( SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"7\\\" as operator, \\\"ODS\\\"as data_layer, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_mobilenet_radius_djezzy_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.capture_day, t.capture_hour, \\\"7\\\", \\\"1020004\\\" UNION ALL SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"6\\\" as operator, \\\"ODS\\\"as data_layer, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_mobilenet_radius_mobilis_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.capture_day, t.capture_hour, \\\"6\\\", \\\"1020004\\\" UNION ALL SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"5\\\" as operator, \\\"ODS\\\"as data_layer, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_mobilenet_radius_ooredoo_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.capture_day, t.capture_hour, \\\"5\\\", \\\"1020004\\\" UNION ALL SELECT COUNT(1) as num, \\\"1020001\\\" as data_type, \\\"99\\\" as operator, \\\"ODS\\\"as data_layer, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_fixnet_radius_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.capture_day, t.capture_hour, \\\"99\\\", \\\"1020001\\\" )SELECT num, data_type, operator, data_layer, capture_day, capture_hour FROM daily_count;', 'Radius线路-ODS层数据量归档统计天任务', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (31, 'WITH daily_count AS ( SELECT COUNT(1) as num, \\\"1020001\\\" as data_type, \\\"99\\\" as operator, \\\"DWD\\\" as data_layer, t.insert_day as insert_day, t.insert_hour as insert_hour FROM v64_deye_dw_dwd.dwd_fixnet_radius_store t WHERE t.insert_day BETWEEN\\\"{captureStartDay}\\\" AND\\\"{captureEndDay}\\\" GROUP BY \\\"1020001\\\", t.insert_day, t.insert_hour, \\\"99\\\" UNION ALL SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"7\\\" as operator, \\\"DWD\\\" as data_layer, t.insert_day as insert_day, t.insert_hour as insert_hour FROM v64_deye_dw_dwd.dwd_mobile_radius_djezzy_store t WHERE t.insert_day BETWEEN\\\"{captureStartDay}\\\" AND\\\"{captureEndDay}\\\" GROUP BY \\\"1020004\\\", t.insert_day, t.insert_hour, \\\"7\\\" UNION ALL SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"6\\\" as operator, \\\"DWD\\\" as data_layer, t.insert_day as insert_day, t.insert_hour as insert_hour FROM v64_deye_dw_dwd.dwd_mobile_radius_mobilis_store t WHERE t.insert_day BETWEEN\\\"{captureStartDay}\\\" AND\\\"{captureEndDay}\\\" GROUP BY \\\"1020004\\\", t.insert_day, t.insert_hour, \\\"6\\\" UNION ALL SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"5\\\" as operator, \\\"DWD\\\" as data_layer, t.insert_day as insert_day, t.insert_hour as insert_hour FROM v64_deye_dw_dwd.dwd_mobile_radius_ooredoo_store t WHERE t.insert_day BETWEEN\\\"{captureStartDay}\\\" AND\\\"{captureEndDay}\\\" GROUP BY \\\"1020004\\\", t.insert_day, t.insert_hour, \\\"5\\\") SELECT num, data_type, operator, data_layer, insert_day AS capture_day, insert_hour AS capture_hour FROM daily_count;', 'Radius线路-dwd层数据量统计天任务', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (32, 'WITH daily_count AS ( SELECT COUNT(1) as num, \\\"1020001\\\" as data_type, \\\"99\\\" as operator, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_fixnet_radius_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"), 1) AND t.insert_hour BETWEEN 0 AND 5) GROUP BY \\\"1020001\\\", t.capture_day, t.capture_hour, \\\"99\\\" UNION ALL SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"7\\\" as operator, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_mobile_radius_djezzy_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"), 1) AND t.insert_hour BETWEEN 0 AND 5) GROUP BY \\\"1020004\\\", t.capture_day, t.capture_hour, \\\"7\\\" UNION ALL SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"6\\\" as operator, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_mobile_radius_mobilis_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"), 1) AND t.insert_hour BETWEEN 0 AND 5) GROUP BY \\\"1020004\\\", t.capture_day, t.capture_hour, \\\"6\\\" UNION ALL SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"5\\\" as operator, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_mobile_radius_ooredoo_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"), 1) AND t.insert_hour BETWEEN 0 AND 5) GROUP BY \\\"1020004\\\", t.capture_day, t.capture_hour, \\\"5\\\" )SELECT num, data_type, operator, data_layer, capture_day, capture_hour FROM daily_count WHERE capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\";', 'Radius线路-dwd层数据量归档统计天任务', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (33, 'SELECT SUM(t.behavior_num) as num, t.data_type as data_type, \\\"\\\" as operator, \\\"DWS\\\"as data_layer, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_dws.dws_theme_auth_account_info t WHERE (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\") AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY data_type , t.capture_day, t.capture_hour', 'Radius数据DWS层数据统计-天', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (34, 'SELECT SUM(t.behavior_num) as num, t.data_type as data_type, \\\"\\\" as operator, \\\"DWS\\\"as data_layer, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_dws.dws_theme_auth_account_info t WHERE (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\") AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY data_type , t.capture_day, t.capture_hour', 'Radius数据DWS层数据归档统计', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (35, 'WITH daily_count AS ( SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_shop_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_keyapp_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_video_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_hardwarestring_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_overnet_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_voice_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_iot_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_webshare_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_digcurrency_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_hackertools_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_religion_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_vm_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_game_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_webbbs_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_picture_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_lbs_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_live_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_sns_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_payment_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_browser_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_navigation_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_inputmethod_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_weblogin_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_voip_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_mblog_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_govserv_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_email_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_basewifi_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_p2p_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_engine_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_webid_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_telnet_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_safety_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_news_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_ftp_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_netbank_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_financial_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_map_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_softinstall_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_appmarket_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"ODS\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_infoserv_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour )SELECT data_type, original_data_type,uparea_alias, data_layer, capture_day, capture_hour, num FROM daily_count', '统计pr中ods层其他协议的归档数据量天任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (36, 'WITH daily_count AS ( SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_engine_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_email_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_entertainment_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_finance_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_ftp_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_lbs_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_multimedia_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_news_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_others_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_shopping_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_sns_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_telnet_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_terminal_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_tool_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_travel_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type as original_data_type, t.uparea_id as uparea_alias, \\\"DWD\\\" as data_layer, t.capture_day as capture_day, t.capture_hour as capture_hour FROM v64_deye_dw_dwd.dwd_pr_voip_store t where (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour )SELECT data_type, original_data_type, uparea_alias, data_layer, capture_day, capture_hour, num FROM daily_count', '统计pr中dwd层其他协议的归档数据量天任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (37, 'with daily_count as( SELECT COUNT(1) as num, t.data_type as data_type, t.data_type AS original_data_type, \\\"\\\" as uparea_alias, \\\"ODS\\\" as data_layer, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_voip_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND capture_hour BETWEEN LPAD(CAST (\\\"{captureStartHour}\\\" AS INT)-1, 2, \\\"0 \\\") AND \\\"{captureEndHour}\\\" GROUP BY t.data_type , from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type AS original_data_type, \\\"\\\" as uparea_alias, \\\"ODS\\\" as data_layer, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_voip_fax_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND capture_hour BETWEEN LPAD(CAST (\\\"{captureStartHour}\\\" AS INT)-1, 2, \\\"0 \\\") AND \\\"{captureEndHour}\\\" GROUP BY t.data_type , from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type AS original_data_type, \\\"\\\" as uparea_alias, \\\"ODS\\\" as data_layer, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_call_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND capture_hour BETWEEN LPAD(CAST (\\\"{captureStartHour}\\\" AS INT)-1, 2, \\\"0 \\\") AND \\\"{captureEndHour}\\\" GROUP BY t.data_type , from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") )SELECT num, data_type, original_data_type, uparea_alias, data_layer, insert_day AS capture_day, insert_hour AS capture_hour FROM daily_count WHERE insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND LPAD(insert_hour,2,\\\"0\\\") BETWEEN \\\"{captureStartHour}\\\" AND \\\"{captureEndHour}\\\";', '统计Phone-ODS层统计数据小时任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"1\",\r\n    \"spark.executor.memory\": \"2G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"1\",\r\n    \"spark.default.parallelism\": \"4\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (38, 'WITH daily_count AS ( SELECT COUNT(1) as num, t.data_type as data_type, t.data_type AS original_data_type, \\\"\\\" as uparea_alias, \\\"DWD\\\" as data_layer, t.insert_day as insert_day, t.insert_hour as insert_hour FROM v64_deye_dw_dwd.dwd_sms_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND t.insert_hour BETWEEN \\\"{captureStartHour}\\\" AND \\\"{captureEndHour}\\\" GROUP BY t.data_type , t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type AS original_data_type, \\\"\\\" as uparea_alias, \\\"DWD\\\" as data_layer, t.insert_day as insert_day, t.insert_hour as insert_hour FROM v64_deye_dw_dwd.dwd_fax_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND t.insert_hour BETWEEN \\\"{captureStartHour}\\\" AND \\\"{captureEndHour}\\\" GROUP BY t.data_type , t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, t.data_type as data_type, t.data_type AS original_data_type, \\\"\\\" as uparea_alias, \\\"DWD\\\" as data_layer, t.insert_day as insert_day, t.insert_hour as insert_hour FROM v64_deye_dw_dwd.dwd_call_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND t.insert_hour BETWEEN \\\"{captureStartHour}\\\" AND \\\"{captureEndHour}\\\" GROUP BY t.data_type , t.insert_day, t.insert_hour )SELECT num, data_type, original_data_type, uparea_alias, data_layer, insert_day AS capture_day, insert_hour AS capture_hour FROM daily_count;', '统计Phone-DWD层统计数据小时任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"1\",\r\n    \"spark.executor.memory\": \"2G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"1\",\r\n    \"spark.default.parallelism\": \"4\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (39, 'with daily_count as( SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"7\\\" as operator, \\\"ODS\\\"as data_layer, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_mobilenet_radius_djezzy_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND capture_hour BETWEEN LPAD(CAST (\\\"{captureStartHour}\\\" AS INT)-1, 2, \\\"0 \\\") AND \\\"{captureEndHour}\\\" GROUP BY from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\"), \\\"7\\\", \\\"1020004\\\" UNION ALL SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"6\\\" as operator, \\\"ODS\\\"as data_layer, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_mobilenet_radius_mobilis_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND capture_hour BETWEEN LPAD(CAST (\\\"{captureStartHour}\\\" AS INT)-1, 2, \\\"0 \\\") AND \\\"{captureEndHour}\\\" GROUP BY from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\"), \\\"6\\\", \\\"1020004\\\" UNION ALL SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"5\\\" as operator, \\\"ODS\\\"as data_layer, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_mobilenet_radius_ooredoo_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND capture_hour BETWEEN LPAD(CAST (\\\"{captureStartHour}\\\" AS INT)-1, 2, \\\"0 \\\") AND \\\"{captureEndHour}\\\" GROUP BY from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\"), \\\"5\\\", \\\"1020004\\\" UNION ALL SELECT COUNT(1) as num, \\\"1020001\\\" as data_type , \\\"99\\\" as operator, \\\"ODS\\\"as data_layer, from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\") AS insert_day , from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\") AS insert_hour FROM v64_deye_dw_ods.ods_fixnet_radius_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND capture_hour BETWEEN LPAD(CAST (\\\"{captureStartHour}\\\" AS INT)-1, 2, \\\"0 \\\") AND \\\"{captureEndHour}\\\" GROUP BY from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"yyyy-MM-dd\\\"), from_unixtime(CAST(insert_time / 1000 as BIGINT ), \\\"H\\\"), \\\"99\\\", \\\"1020001\\\" ) SELECT num, data_type, operator, data_layer, insert_day AS capture_day, insert_hour AS capture_hour FROM daily_count WHERE insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND LPAD(insert_hour,2,\\\"0\\\") BETWEEN \\\"{captureStartHour}\\\" AND \\\"{captureEndHour}\\\";', 'Radius数据ODS层数据统计-小时', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"1\",\r\n    \"spark.executor.memory\": \"2G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"1\",\r\n    \"spark.default.parallelism\": \"4\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (40, 'WITH daily_count AS ( SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"7\\\" as operator, \\\"DWD\\\" as data_layer, t.insert_day as insert_day, t.insert_hour as insert_hour FROM v64_deye_dw_dwd.dwd_mobile_radius_djezzy_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND t.insert_hour BETWEEN \\\"{captureStartHour}\\\" AND \\\"{captureEndHour}\\\" GROUP BY \\\"1020004\\\" , \\\"7\\\", t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"6\\\" as operator, \\\"DWD\\\" as data_layer, t.insert_day as insert_day, t.insert_hour as insert_hour FROM v64_deye_dw_dwd.dwd_mobile_radius_mobilis_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND t.insert_hour BETWEEN \\\"{captureStartHour}\\\" AND \\\"{captureEndHour}\\\" GROUP BY \\\"1020004\\\" , \\\"6\\\", t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, \\\"1020004\\\" as data_type, \\\"5\\\" as operator, \\\"DWD\\\" as data_layer, t.insert_day as insert_day, t.insert_hour as insert_hour FROM v64_deye_dw_dwd.dwd_mobile_radius_ooredoo_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND t.insert_hour BETWEEN \\\"{captureStartHour}\\\" AND \\\"{captureEndHour}\\\" GROUP BY \\\"1020004\\\" , \\\"5\\\", t.insert_day, t.insert_hour UNION ALL SELECT COUNT(1) as num, \\\"1020001\\\" as data_type, \\\"99\\\" as operator, \\\"DWD\\\" as data_layer, t.insert_day as insert_day, t.insert_hour as insert_hour FROM v64_deye_dw_dwd.dwd_fixnet_radius_store t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND t.insert_hour BETWEEN \\\"{captureStartHour}\\\" AND \\\"{captureEndHour}\\\" GROUP BY \\\"1020001\\\" , \\\"99\\\", t.insert_day, t.insert_hour )SELECT num, data_type, operator, data_layer, insert_day AS capture_day, insert_hour AS capture_hour FROM daily_count;', 'Radius数据DWD层数据统计-小时', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"1\",\r\n    \"spark.executor.memory\": \"2G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"1\",\r\n    \"spark.default.parallelism\": \"4\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (41, 'SELECT SUM(t.behavior_num) as num, t.data_type as data_type, \\\"\\\" as operator, \\\"DWS\\\"as data_layer, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_dws.dws_theme_auth_account_info t WHERE (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\") AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" AND t.capture_hour BETWEEN \\\"{captureStartHour}\\\" AND \\\"{captureEndHour}\\\" GROUP BY data_type , t.capture_day, t.capture_hour', 'Radius数据DWS层数据统计-小时', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"1\",\r\n    \"spark.executor.memory\": \"2G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"1\",\r\n    \"spark.default.parallelism\": \"4\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (42, 'SELECT t.data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, t.insert_day AS capture_day, t.insert_hour AS capture_hour,  COUNT(1) AS num FROM v64_deye_dw_ods.ods_nf_other_log t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour', '统计nf中ods层other_log数据量', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (43, 'SELECT t.data_type, t.uparea_id as uparea_alias,  \\\"DWD\\\" AS data_layer, t.insert_day as capture_day, t.insert_hour as capture_hour, SUM(t.behavior_num) as num FROM v64_deye_dw_dwd.dwd_behavior_log_other_log t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour', '统计nf中dwd层other_log数据量', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (44, 'SELECT data_type, uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, insert_day AS capture_day, insert_hour AS capture_hour, COUNT(1) AS num FROM v64_deye_dw_ods.ods_nf_url WHERE insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY data_type, uparea_id, insert_day, insert_hour UNION ALL SELECT data_type, uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, insert_day AS capture_day, insert_hour AS capture_hour, COUNT(1) AS num FROM v64_deye_dw_ods.ods_nf_chat WHERE insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY data_type, uparea_id, insert_day, insert_hour UNION ALL SELECT data_type, uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, insert_day AS capture_day, insert_hour AS capture_hour, COUNT(1) AS num FROM v64_deye_dw_ods.ods_nf_mail WHERE insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY data_type, uparea_id, insert_day, insert_hour UNION ALL SELECT data_type, uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, insert_day AS capture_day, insert_hour AS capture_hour, COUNT(1) AS num FROM v64_deye_dw_ods.ods_nf_bbs_weibo WHERE insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY data_type, uparea_id, insert_day, insert_hour', '统计nf中ods层除other_log以外其他协议的数据量', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (45, 'SELECT t.data_type AS data_type, t.uparea_id AS uparea_alias, \\\"DWD\\\" AS data_layer, t.insert_day AS capture_day, t.insert_hour AS capture_hour, SUM(t.behavior_num) AS num FROM v64_deye_dw_dwd.dwd_behavior_log_others t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour UNION ALL SELECT t.data_type AS data_type, t.uparea_id AS uparea_alias, \\\"DWD\\\" AS data_layer, t.insert_day AS capture_day, t.insert_hour AS capture_hour, SUM(t.behavior_num) AS num FROM v64_deye_dw_dwd.dwd_behavior_log_url t WHERE t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.insert_day, t.insert_hour', '统计nf中dwd层除other_log以外其他类型的数据量', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (46, 'SELECT t.data_type, t.uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, t.capture_day, t.capture_hour, COUNT(1) AS num FROM v64_deye_dw_ods.ods_nf_other_log_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour;', 'nf中ods层other_log数据归档统计', '{\r\n    \"spark.driver.memory\": \"8g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"64\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (47, 'SELECT data_type, uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, capture_day, capture_hour, COUNT(1) AS num FROM v64_deye_dw_ods.ods_nf_url_store WHERE capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY data_type, uparea_id, capture_day, capture_hour', 'nf中ods层url协议数据归档统计', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (48, 'SELECT t.data_type, t.uparea_id AS uparea_alias,\\\"DWD\\\" AS data_layer, t.capture_day, t.capture_hour, SUM(t.behavior_num) AS num FROM v64_deye_dw_dwd.dwd_behavior_log_other_log t WHERE (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour;', 'nf中dwd层nf_other_log归档数据统计', '{\r\n    \"spark.driver.memory\": \"8g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"64\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (49, 'SELECT t.data_type AS data_type,  t.uparea_id AS uparea_alias,  \\\"DWD\\\" AS data_layer,  t.capture_day AS capture_day,  t.capture_hour AS capture_hour,  SUM(t.behavior_num) AS num FROM v64_deye_dw_dwd.dwd_behavior_log_others t WHERE (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour', 'nf中dwd层其他协议数据归档统计', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (50, 'SELECT data_type,  uparea_id AS uparea_alias,  \\\"ODS\\\" AS data_layer,  capture_day,  capture_hour,  COUNT(1) AS num FROM v64_deye_dw_ods.ods_nf_chat_store WHERE capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY data_type, uparea_id, capture_day, capture_hour UNION ALL SELECT data_type, uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, capture_day, capture_hour, COUNT(1) AS num FROM v64_deye_dw_ods.ods_nf_mail_store WHERE capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY data_type, uparea_id, capture_day, capture_hour UNION ALL SELECT data_type, uparea_id AS uparea_alias, \\\"ODS\\\" AS data_layer, capture_day, capture_hour, COUNT(1) AS num FROM v64_deye_dw_ods.ods_nf_bbs_weibo_store WHERE capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY data_type, uparea_id, capture_day, capture_hour', 'nf中ods层其他协议数据归档统计', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (51, 'SELECT t.data_type AS data_type, t.uparea_id AS uparea_alias, \\\"DWD\\\" AS data_layer, t.capture_day AS capture_day, t.capture_hour AS capture_hour, SUM(t.behavior_num) AS num FROM v64_deye_dw_dwd.dwd_behavior_log_url t WHERE (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" GROUP BY t.data_type, t.uparea_id, t.capture_day, t.capture_hour', 'nf中dwd层url协议数据归档统计', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (52, 'with time_diff AS (SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_shop_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_keyapp_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_video_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_hardwarestring_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_voice_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_iot_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_webshare_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_digcurrency_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_hackertools_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_religion_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_vm_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_game_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_webbbs_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_picture_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_lbs_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_live_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_sns_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_payment_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_browser_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_navigation_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_inputmethod_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_weblogin_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_voip_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_mblog_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_govserv_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_email_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_basewifi_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_p2p_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_engine_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_webid_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_telnet_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_safety_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_news_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_ftp_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_netbank_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_financial_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_map_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_softinstall_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_appmarket_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, t.capture_day as capture_day , t.capture_hour as capture_hour FROM v64_deye_dw_ods.ods_pr_infoserv_store t WHERE t.capture_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" ) SELECT COUNT(1) as num, time_diff.delay as delay, time_diff.data_type as data_type, time_diff.data_type as original_data_type,time_diff.uparea_alias as uparea_alias, \\\"ODS\\\" as data_layer, time_diff.capture_day as capture_day, time_diff.capture_hour as capture_hour FROM time_diff GROUP BY delay, data_type,uparea_alias, capture_day, capture_hour; ', '统计ODS层PR其他协议每条数据时延天任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"4\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (53, 'with time_diff AS (SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, FROM_UNIXTIME(CAST(t.capture_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS capture_day, FROM_UNIXTIME(CAST(t.capture_time / 1000 AS BIGINT), \\\"H\\\") AS capture_hour FROM v64_deye_dw_ods.ods_nf_other_log t WHERE (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND FROM_UNIXTIME(CAST(t.capture_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\") SELECT COUNT(1) as num, time_diff.delay as delay, time_diff.data_type as data_type,time_diff.uparea_alias as uparea_alias, \\\"ODS\\\" as data_layer, time_diff.capture_day as capture_day, time_diff.capture_hour as capture_hour FROM time_diff GROUP BY delay, data_type,uparea_alias, capture_day, capture_hour;', '统计ODS层NF-Other_Log协议数据时延天任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (54, 'with time_diff AS (SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, FROM_UNIXTIME(CAST(t.capture_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS capture_day, FROM_UNIXTIME(CAST(t.capture_time / 1000 AS BIGINT), \\\"H\\\") AS capture_hour FROM v64_deye_dw_ods.ods_nf_url t WHERE (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND FROM_UNIXTIME(CAST(t.capture_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, FROM_UNIXTIME(CAST(t.capture_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS capture_day, FROM_UNIXTIME(CAST(t.capture_time / 1000 AS BIGINT), \\\"H\\\") AS capture_hour FROM v64_deye_dw_ods.ods_nf_chat t WHERE (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND FROM_UNIXTIME(CAST(t.capture_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, FROM_UNIXTIME(CAST(t.capture_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS capture_day, FROM_UNIXTIME(CAST(t.capture_time / 1000 AS BIGINT), \\\"H\\\") AS capture_hour FROM v64_deye_dw_ods.ods_nf_mail t WHERE (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND FROM_UNIXTIME(CAST(t.capture_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" UNION ALL SELECT LEAST(60,(FLOOR(cast((t.insert_time -t.capture_time)/ 60000 AS Integer) / 5) * 5) + 5) AS delay , t.data_type as data_type ,t.uparea_id AS uparea_alias, FROM_UNIXTIME(CAST(t.capture_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") AS capture_day, FROM_UNIXTIME(CAST(t.capture_time / 1000 AS BIGINT), \\\"H\\\") AS capture_hour FROM v64_deye_dw_ods.ods_nf_bbs_weibo t WHERE (t.insert_day BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\" OR (t.insert_day = DATE_ADD(TO_DATE(\\\"{captureEndDay}\\\"),1) AND t.insert_hour BETWEEN 0 AND 5)) AND FROM_UNIXTIME(CAST(t.capture_time / 1000 AS BIGINT), \\\"yyyy-MM-dd\\\") BETWEEN \\\"{captureStartDay}\\\" AND \\\"{captureEndDay}\\\") SELECT COUNT(1) as num, time_diff.delay as delay, time_diff.data_type as data_type,time_diff.uparea_alias as uparea_alias, \\\"ODS\\\" as data_layer, time_diff.capture_day as capture_day, time_diff.capture_hour as capture_hour FROM time_diff GROUP BY delay, data_type,uparea_alias, capture_day, capture_hour;', '统计ODS层NF-其他协议数据时延天任务', '{\r\n    \"spark.driver.memory\": \"2g\",\r\n    \"spark.driver.cores\": \"4\",\r\n    \"spark.executor.memory\": \"8G\",\r\n    \"spark.executor.instances\": \"8\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"32\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (55, 'INSERT INTO ads_ops.ops_pr_layer_diff_statistics (data_type,uparea_alias,data_layer,diff_num,capture_day,capture_hour,create_time) ( SELECT result_tab.data_type, result_tab.uparea_alias as uparea_alias, \'ODS-DWD\' AS data_layer, MAX(result_tab.ods_num)-MAX(result_tab.dwd_num) as diff_num, result_tab.capture_day, result_tab.capture_hour, UNIX_TIMESTAMP()*1000 as create_time  FROM ( SELECT t.data_type , t.uparea_alias , t.data_layer , t.capture_day , SUM(num) as num , SUM(CASE WHEN t.data_layer = \'ODS\' THEN t.num ELSE 0 END) AS ods_num, SUM(CASE WHEN t.data_layer = \'DWD\' THEN t.num ELSE 0 END) AS dwd_num, t.capture_hour FROM ads_ops.ops_pr_data_monitor_statistics_store t WHERE t.capture_day BETWEEN  %s AND  %s \r\n AND t.data_type NOT IN (\'Call\',\'SMS\',\'Fax\') GROUP BY t.data_type , t.uparea_alias , t.data_layer , t.capture_day, t.capture_hour ORDER BY t.data_type ASC, t.data_layer ASC  ) result_tab GROUP BY result_tab.data_type , result_tab.capture_day, result_tab.capture_hour, result_tab.uparea_alias UNION ALL SELECT result_tab.data_type, result_tab.uparea_alias as uparea_alias, \'DWD-ADS\' AS data_layer, MAX(result_tab.dwd_num)-MAX(result_tab.ads_num) as diff_num, result_tab.capture_day, result_tab.capture_hour, UNIX_TIMESTAMP()*10000 as create_time  FROM ( SELECT t.data_type , t.uparea_alias , t.data_layer , t.capture_day , SUM(num) as num , SUM(CASE WHEN t.data_layer = \'DWD\' THEN t.num ELSE 0 END) AS dwd_num, SUM(CASE WHEN t.data_layer = \'ADS\' THEN t.num ELSE 0 END) AS ads_num, t.capture_hour FROM ads_ops.ops_pr_data_monitor_statistics_store t WHERE t.capture_day BETWEEN  %s AND  %s \r\n AND t.data_type NOT IN (\'Call\',\'SMS\',\'Fax\')  GROUP BY t.data_type , t.uparea_alias , t.data_layer , t.capture_day, t.capture_hour ORDER BY t.data_type ASC, t.data_layer ASC  ) result_tab GROUP BY result_tab.data_type , result_tab.capture_day, result_tab.capture_hour, result_tab.uparea_alias);', '统计PR数仓层数据差', ' ');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (56, 'INSERT INTO ads_ops.ops_nf_layer_diff_statistics (data_type,uparea_alias,data_layer,diff_num,capture_day,capture_hour,create_time) ( SELECT result_tab.data_type, result_tab.uparea_alias as uparea_alias, \'ODS-DWD\' AS data_layer, MAX(result_tab.ods_num)-MAX(result_tab.dwd_num) as diff_num, result_tab.capture_day, result_tab.capture_hour, UNIX_TIMESTAMP()*1000 as create_time  FROM ( SELECT t.data_type , t.uparea_alias , t.data_layer , t.capture_day , SUM(num) as num , SUM(CASE WHEN t.data_layer = \'ODS\' THEN t.num ELSE 0 END) AS ods_num, SUM(CASE WHEN t.data_layer = \'DWD\' THEN t.num ELSE 0 END) AS dwd_num, t.capture_hour FROM ads_ops.ops_nf_data_monitor_statistics_store t WHERE t.capture_day BETWEEN  %s AND  %s GROUP BY t.data_type , t.uparea_alias , t.data_layer , t.capture_day, t.capture_hour ORDER BY t.data_type ASC, t.data_layer ASC  ) result_tab GROUP BY result_tab.data_type , result_tab.capture_day, result_tab.capture_hour, result_tab.uparea_alias );', '统计NF数据ODS-DWD层数据差', ' ');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (57, 'INSERT INTO ads_ops.ops_radius_layer_diff_statistics (data_type,data_layer,diff_num,capture_day,capture_hour,create_time) ( SELECT result_tab.data_type, \'ODS-DWD\' AS data_layer, MAX(result_tab.ods_num)-MAX(result_tab.dwd_num) as diff_num, result_tab.capture_day, result_tab.capture_hour, UNIX_TIMESTAMP()*1000 as create_time  FROM ( SELECT t.data_type , t.data_layer , t.capture_day , SUM(num) as num , SUM(CASE WHEN t.data_layer = \'ODS\' THEN t.num ELSE 0 END) AS ods_num, SUM(CASE WHEN t.data_layer = \'DWD\' THEN t.num ELSE 0 END) AS dwd_num, t.capture_hour FROM ads_ops.ops_radius_data_monitor_statistics_store t WHERE t.capture_day BETWEEN  %s AND  %s GROUP BY t.data_type , t.data_layer , t.capture_day, t.capture_hour ORDER BY t.data_type ASC, t.data_layer ASC  ) result_tab GROUP BY result_tab.data_type , result_tab.capture_day, result_tab.capture_hour UNION ALL SELECT result_tab.data_type, \'DWD-ADS\' AS data_layer, MAX(result_tab.dwd_num)-MAX(result_tab.ads_num) as diff_num, result_tab.capture_day, result_tab.capture_hour, UNIX_TIMESTAMP()*10000 as create_time  FROM ( SELECT t.data_type,t.data_layer , t.capture_day , SUM(num) as num , SUM(CASE WHEN t.data_layer = \'DWD\' THEN t.num ELSE 0 END) AS dwd_num, SUM(CASE WHEN t.data_layer = \'ADS\' THEN t.num ELSE 0 END) AS ads_num, t.capture_hour FROM ads_ops.ops_radius_data_monitor_statistics_store t WHERE t.capture_day BETWEEN  %s AND  %s GROUP BY t.data_type , t.data_layer , t.capture_day, t.capture_hour ORDER BY t.data_type ASC, t.data_layer ASC  ) result_tab GROUP BY result_tab.data_type , result_tab.capture_day, result_tab.capture_hour);', '统计Radius数据仓层数据差', ' ');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (58, '{\"aggs\":{\"data_type\":{\"terms\":{\"field\":\"norm_data_typeField\",\"size\":25},\"aggs\":{\"capture_day\":{\"terms\":{\"field\":\"capture_dayField\",\"size\":25},\"aggs\":{\"uparea_alias\":{\"terms\":{\"field\":\"uparea_id_mapField\",\"size\":10}}}}}}},\"query\":{\"range\":{\"capture_dayField\":{\"gte\":\"{captureStartDay}\",\"lte\":\"{captureEndDay}\"}}}}', '统计PR/Phone线路ADS层协议数据量天任务', '');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (59, '{\"aggs\":{\"data_type\":{\"terms\":{\"field\":\"norm_data_typeField\",\"size\":25},\"aggs\":{\"capture_day\":{\"terms\":{\"field\":\"capture_dayField\",\"size\":25},\"aggs\":{\"operator\":{\"terms\":{\"field\":\"isp_latitude\",\"size\":10}}}}}}},\"query\":{\"range\":{\"capture_dayField\":{\"gte\":\"{captureStartDay}\",\"lte\":\"{captureEndDay}\"}}}}', '统计Radius线路ADS层协议数据量天任务', ' ');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (60, 'select \\\"{captureDay}\\\" as capture_day, \\\"Association\\\" as statistics_type, COALESCE(sum(if (pr.auth_account is not null and pr.auth_account <> \\\"\\\", 1, 0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE(CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if (pr.auth_account is not null and pr.auth_account <> \\\"\\\", 1, 0)) / sum(1) END, 0) as statistics_value from ( select ip from v64_deye_dw_ods.ods_fixnet_radius_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" and action in (\\\"logout\\\", \\\"login\\\") group by ip ) radius left join ( select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_http_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_im_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_email_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_remotectrl_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_ftp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_game_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_p2p_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_telnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_vpn_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_hardwarestring_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_shop_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_keyapp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_video_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_overnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_voice_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_iot_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_webshare_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_digcurrency_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_hackertools_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_religion_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_vm_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_webbbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_picture_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_lbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_live_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_sns_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_payment_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_browser_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_navigation_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_inputmethod_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_weblogin_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_voip_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_mblog_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_govserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_basewifi_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_engine_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_webid_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_safety_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_news_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_netbank_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_financial_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_map_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_softinstall_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_appmarket_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account union all select strsrc_ip, auth_account from v64_deye_dw_ods.ods_pr_infoserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by strsrc_ip, auth_account ) pr on radius.ip = pr.strsrc_ip where pr.strsrc_ip is not null;', 'PR-固网Radius关联率统计任务', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (61, 'select \\\"{captureDay}\\\" as capture_day, \\\"Accuracy\\\" as statistics_type, COALESCE(sum(if(LOWER(t1.auth_account) = LOWER(t1.account) and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)), 1, 0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE(CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if(LOWER(t1.auth_account) = LOWER(t1.account) and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)), 1, 0)) / sum(1) END, 0) as statistics_value from ( select radius.ip, radius.account, radius.start_time, IF(radius.end_time is null, {end_time}, radius.end_time) as end_time, pr.auth_account, pr.capture_time from ( select ip, account, min(CASE WHEN action = \\\"login\\\" THEN capture_time END) as start_time, max(CASE WHEN action = \\\"logout\\\" THEN capture_time END) as end_time from v64_deye_dw_ods.ods_fixnet_radius_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by ip, account having start_time is not null ) radius left join ( select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_http_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_im_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_email_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_remotectrl_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_ftp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_game_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_p2p_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_telnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_vpn_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_hardwarestring_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_shop_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_keyapp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_video_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_overnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_voice_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_iot_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_webshare_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_digcurrency_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_hackertools_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_religion_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_vm_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_webbbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_picture_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_lbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_live_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_sns_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_payment_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_browser_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_navigation_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_inputmethod_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_weblogin_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_voip_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_mblog_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_govserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_basewifi_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_engine_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_webid_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_safety_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_news_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_netbank_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_financial_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_map_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_softinstall_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_appmarket_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select strsrc_ip, auth_account, capture_time from v64_deye_dw_ods.ods_pr_infoserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\") pr on pr.strsrc_ip = radius.ip where pr.strsrc_ip is not null and pr.auth_account is not null and pr.auth_account <> \\\"\\\") t1;', 'PR-固网Radius关联准确率统计任务', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (62, 'select \\\"{captureDay}\\\" as capture_day, \\\"Association\\\" as statistics_type, COALESCE(sum(if (nf.user_name is not null and nf.user_name <> \\\"\\\", 1, 0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE( CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if (nf.user_name is not null and nf.user_name <> \\\"\\\", 1, 0)) / sum(1) END, 0) as statistics_value from ( select ip from v64_deye_dw_ods.ods_fixnet_radius_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by ip ) radius left join ( select user_name, strsrc_ip from v64_deye_dw_ods.ods_nf_other_log_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by user_name, strsrc_ip union all select user_name, strsrc_ip from v64_deye_dw_ods.ods_nf_url_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by user_name, strsrc_ip ) nf on radius.ip = nf.strsrc_ip where nf.strsrc_ip is not null;', 'NF-固网Radius关联率统计任务', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (63, 'select \\\"{captureDay}\\\" as capture_day, \\\"Accuracy\\\" as statistics_type, COALESCE(sum(if(LOWER(t1.user_name) = LOWER(t1.account) and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)),1,0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE( CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if(LOWER(t1.user_name) = LOWER(t1.account) and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)), 1, 0)) / sum(1) END, 0) as statistics_value from ( select radius.ip, radius.account, radius.start_time, IF(radius.end_time is null, {end_time}, radius.end_time) as end_time, nf.user_name as user_name, nf.capture_time as capture_time from ( select ip, account, min(CASE WHEN action = \\\"login\\\" THEN capture_time END) as start_time, max(CASE WHEN action = \\\"logout\\\" THEN capture_time END) as end_time from v64_deye_dw_ods.ods_fixnet_radius_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" group by ip, account having start_time is not null ) radius left join ( select user_name, strsrc_ip, capture_time from v64_deye_dw_ods.ods_nf_other_log_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" union all select user_name, strsrc_ip, capture_time from v64_deye_dw_ods.ods_nf_url_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{fixed_start_hour}\\\" and \\\"{fixed_end_hour}\\\" ) nf on radius.ip = nf.strsrc_ip where nf.strsrc_ip is not null and nf.user_name is not null and nf.user_name <> \\\"\\\") t1;', 'NF-固网Radius关联准确率统计任务', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (64, 'select \\\"{captureDay}\\\" as capture_day, \\\"Djezzy\\\" as operator, \\\"Association\\\" as statistics_type, COALESCE(sum(if (pr.auth_account is not null and pr.auth_account <> \\\"\\\", 1, 0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE(CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if (pr.auth_account is null and pr.auth_account <> \\\"\\\", 1, 0)) / sum(1) END, 0) as statistics_value from ( select internet_ip, cast(split(port_range, \\\"-\\\")[0] as int) as start_port, cast(split(port_range, \\\"-\\\")[1] as int) as end_port from v64_deye_dw_ods.ods_mobilenet_radius_djezzy_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" and action in (\\\"Start\\\", \\\"Stop\\\") group by internet_ip, port_range ) radius left join ( select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_http_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_im_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_email_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_remotectrl_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_ftp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_game_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_p2p_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_telnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_vpn_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_hardwarestring_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_shop_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_keyapp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_video_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_overnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_voice_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_iot_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_webshare_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_digcurrency_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_hackertools_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_religion_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_vm_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_webbbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_picture_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_lbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_live_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_sns_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_payment_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_browser_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_navigation_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_inputmethod_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_weblogin_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_voip_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_mblog_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_govserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_basewifi_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_engine_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_webid_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_safety_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_news_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_netbank_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_financial_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_map_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_softinstall_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_appmarket_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_infoserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port ) pr on radius.internet_ip = pr.strsrc_ip and pr.src_port >= radius.start_port and pr.src_port <= radius.end_port where pr.strsrc_ip is not null;', 'PR-移动网Radius关联率统计任务-Djezzy', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (65, 'select \\\"{captureDay}\\\" as capture_day, \\\"Djezzy\\\" as operator, \\\"Accuracy\\\" as statistics_type, COALESCE(sum(if(LOWER(auth_account) = LOWER(calling_station_id) and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)), 1, 0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE(CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if(LOWER(auth_account) = LOWER(calling_station_id) and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)), 1, 0)) / sum(1) END, 0) as statistics_value from ( select radius.internet_ip, radius.calling_station_id, radius.start_port, radius.end_port, radius.start_time, IF(radius.end_time is null, {end_time}, radius.end_time) as end_time, pr.auth_account, pr.capture_time from ( select internet_ip, calling_station_id, start_port, end_port, start_time, end_time from ( select internet_ip, calling_station_id, cast(split(port_range, \\\"-\\\")[0] as int) as start_port, cast(split(port_range, \\\"-\\\")[1] as int) as end_port, min(CASE WHEN action = \\\"Start\\\" THEN capture_time END) as start_time, max(CASE WHEN action = \\\"Stop\\\" THEN capture_time END) as end_time from v64_deye_dw_ods.ods_mobilenet_radius_djezzy_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by internet_ip, calling_station_id, port_range having start_time is not null ) as t1 where (IF(t1.end_time is null, {end_time}, t1.end_time) - t1.start_time > {online_limit_min} * 60 * 1000) ) radius left join ( select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_http_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_im_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_email_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_remotectrl_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_ftp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_game_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_p2p_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_telnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_vpn_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_hardwarestring_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_shop_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_keyapp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_video_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_overnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_voice_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_iot_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_webshare_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_digcurrency_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_hackertools_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_religion_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_vm_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_webbbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_picture_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_lbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_live_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_sns_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_payment_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_browser_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_navigation_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_inputmethod_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_weblogin_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_voip_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_mblog_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_govserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_basewifi_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_engine_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_webid_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_safety_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_news_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_netbank_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_financial_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_map_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_softinstall_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_appmarket_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_infoserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\") pr on pr.strsrc_ip = radius.internet_ip and pr.src_port >= radius.start_port and pr.src_port <= radius.end_port where pr.strsrc_ip is not null and pr.auth_account is not null and pr.auth_account <> \\\"\\\") t;', 'PR-移动网Radius关联准确率统计任务-Djezzy', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (66, 'select \\\"{captureDay}\\\" as capture_day, \\\"Djezzy\\\" as operator, \\\"Association\\\" as statistics_type, COALESCE(sum(if (nf.user_name is not null and nf.user_name <> \\\"\\\", 1, 0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE( CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if (nf.user_name is not null and nf.user_name <> \\\"\\\", 1, 0))/ sum(1) END, 0) as statistics_value from ( select internet_ip, cast(split(port_range, \\\"-\\\")[0] as int) as start_port, cast(split(port_range, \\\"-\\\")[1] as int) as end_port from v64_deye_dw_ods.ods_mobilenet_radius_djezzy_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by internet_ip, port_range ) radius left join ( select user_name, strsrc_ip, src_port from v64_deye_dw_ods.ods_nf_other_log_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by user_name, strsrc_ip, src_port union all select user_name, strsrc_ip, src_port from v64_deye_dw_ods.ods_nf_url_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by user_name, strsrc_ip, src_port ) nf on radius.internet_ip = nf.strsrc_ip and nf.src_port >= radius.start_port and nf.src_port <= radius.end_port where nf.strsrc_ip is not null;', 'NF-移动网Radius关联率统计任务-Djezzy', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (67, 'select \\\"{captureDay}\\\" as capture_day, \\\"Djezzy\\\" as operator, \\\"Accuracy\\\" as statistics_type, COALESCE(sum(if(LOWER(user_name) = LOWER(calling_station_id) and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)),1,0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE(CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if(LOWER(user_name) = LOWER(calling_station_id) and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)),1,0)) / sum(1) END, 0) as statistics_value from ( select radius.internet_ip, radius.calling_station_id, radius.start_port, radius.end_port, radius.start_time, IF(radius.end_time is null, {end_time}, radius.end_time) as end_time, nf.user_name, nf.capture_time from ( select internet_ip, calling_station_id, start_port, end_port, start_time, end_time from ( select internet_ip, calling_station_id, cast(split(port_range, \\\"-\\\")[0] as int) as start_port, cast(split(port_range, \\\"-\\\")[1] as int) as end_port, min(CASE WHEN action = \\\"Start\\\" THEN capture_time END) as start_time, max(CASE WHEN action = \\\"Stop\\\" THEN capture_time END) as end_time from v64_deye_dw_ods.ods_mobilenet_radius_djezzy_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by internet_ip, calling_station_id, port_range having start_time is not null ) as t1 where (IF(t1.end_time is null, {end_time}, t1.end_time) - t1.start_time > {online_limit_min} * 60 * 1000) ) radius left join ( select strsrc_ip, user_name, src_port, capture_time from v64_deye_dw_ods.ods_nf_other_log_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, user_name, src_port, capture_time from v64_deye_dw_ods.ods_nf_url_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" ) nf on nf.strsrc_ip = radius.internet_ip and nf.src_port >= radius.start_port and nf.src_port <= radius.end_port where nf.strsrc_ip is not null and nf.user_name is not null and nf.user_name <> \\\"\\\") t;', 'NF-移动网Radius关联准确率统计任务-Djezzy', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (68, '{\"size\":1000,\"_source\":[\"_id\"],\"query\":{\"bool\":{\"must\":[{\"match\":{\"uparea_idField\":\"{uparea_id}\"}}],\"filter\":[{\"range\":{\"capture_timeField\":{\"gte\":{captureStartTime},\"lte\":{captureEndTime}}}}]}}}', '统计PR线路ES-Hbase数据量差异', '{\"sample_size\":10000}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (69, 'select \\\"{captureDay}\\\" as capture_day, \\\"Mobilis\\\" as operator, \\\"Association\\\" as statistics_type, COALESCE(sum(if (pr.auth_account is not null and pr.auth_account <> \\\"\\\", 1, 0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE(CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if (pr.auth_account is not null and pr.auth_account <> \\\"\\\", 1, 0)) / sum(1) END, 0) as statistics_value from ( select internet_ip, cast(split(port_range, \\\"-\\\")[0] as int) as start_port, cast(split(port_range, \\\"-\\\")[1] as int) as end_port from v64_deye_dw_ods.ods_mobilenet_radius_mobilis_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" and action in (\\\"Start\\\", \\\"Stop\\\") group by internet_ip, port_range ) radius left join ( select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_http_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_im_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_email_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_remotectrl_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_ftp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_game_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_p2p_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_telnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_vpn_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_hardwarestring_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_shop_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_keyapp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_video_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_overnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_voice_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_iot_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_webshare_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_digcurrency_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_hackertools_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_religion_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_vm_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_webbbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_picture_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_lbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_live_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_sns_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_payment_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_browser_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_navigation_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_inputmethod_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_weblogin_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_voip_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_mblog_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_govserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_basewifi_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_engine_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_webid_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_safety_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_news_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_netbank_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_financial_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_map_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_softinstall_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_appmarket_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_infoserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port ) pr on radius.internet_ip = pr.strsrc_ip and pr.src_port >= radius.start_port and pr.src_port <= radius.end_port where pr.strsrc_ip is not null;', 'PR-移动网Radius关联率统计任务-Mobilis', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (70, 'select \\\"{captureDay}\\\" as capture_day, \\\"Ooredoo\\\" as operator, \\\"Association\\\" as statistics_type, COALESCE(sum(if (pr.auth_account is not null and pr.auth_account <> \\\"\\\", 1, 0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE(CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if (pr.auth_account is not null and pr.auth_account <> \\\"\\\", 1, 0)) / sum(1) END, 0) as statistics_value from ( select internet_ip, cast(split(port_range, \\\"-\\\")[0] as int) as start_port, cast(split(port_range, \\\"-\\\")[1] as int) as end_port from v64_deye_dw_ods.ods_mobilenet_radius_ooredoo_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" and action in (\\\"Start\\\", \\\"Stop\\\") group by internet_ip, port_range ) radius left join ( select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_http_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_im_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_email_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_remotectrl_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_ftp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_game_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_p2p_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_telnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_vpn_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_hardwarestring_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_shop_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_keyapp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_video_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_overnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_voice_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_iot_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_webshare_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_digcurrency_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_hackertools_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_religion_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_vm_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_webbbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_picture_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_lbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_live_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_sns_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_payment_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_browser_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_navigation_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_inputmethod_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_weblogin_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_voip_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_mblog_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_govserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_basewifi_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_engine_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_webid_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_safety_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_news_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_netbank_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_financial_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_map_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_softinstall_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_appmarket_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port union all select strsrc_ip, auth_account, src_port from v64_deye_dw_ods.ods_pr_infoserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by strsrc_ip, auth_account, src_port ) pr on radius.internet_ip = pr.strsrc_ip and pr.src_port >= radius.start_port and pr.src_port <= radius.end_port where pr.strsrc_ip is not null;', 'PR-移动网Radius关联率统计任务-Ooredoo', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (71, 'select \\\"{captureDay}\\\" as capture_day, \\\"Mobilis\\\" as operator, \\\"Accuracy\\\" as statistics_type, COALESCE(sum(if(LOWER(auth_account) = LOWER(calling_station_id) and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)), 1, 0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE(CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if(LOWER(auth_account) = LOWER(calling_station_id)and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)), 1, 0)) / sum(1) END, 0) as statistics_value from ( select radius.internet_ip, radius.calling_station_id, radius.start_port, radius.end_port, radius.start_time, IF(radius.end_time is null, {end_time}, radius.end_time) as end_time, pr.auth_account, pr.capture_time from ( select internet_ip, calling_station_id, start_port, end_port, start_time, end_time from ( select internet_ip, calling_station_id, cast(split(port_range, \\\"-\\\")[0] as int) as start_port, cast(split(port_range, \\\"-\\\")[1] as int) as end_port, min(CASE WHEN action = \\\"Start\\\" THEN capture_time END) as start_time, max(CASE WHEN action = \\\"Stop\\\" THEN capture_time END) as end_time from v64_deye_dw_ods.ods_mobilenet_radius_mobilis_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by internet_ip, calling_station_id, port_range having start_time is not null ) as t1 where (IF(t1.end_time is null, {end_time}, t1.end_time) - t1.start_time > {online_limit_min} * 60 * 1000) ) radius left join ( select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_http_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_im_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_email_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_remotectrl_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_ftp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_game_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_p2p_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_telnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_vpn_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_hardwarestring_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_shop_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_keyapp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_video_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_overnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_voice_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_iot_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_webshare_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_digcurrency_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_hackertools_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_religion_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_vm_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_webbbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_picture_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_lbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_live_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_sns_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_payment_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_browser_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_navigation_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_inputmethod_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_weblogin_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_voip_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_mblog_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_govserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_basewifi_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_engine_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_webid_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_safety_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_news_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_netbank_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_financial_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_map_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_softinstall_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_appmarket_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_infoserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\") pr on pr.strsrc_ip = radius.internet_ip and pr.src_port >= radius.start_port and pr.src_port <= radius.end_port where pr.strsrc_ip is not null and pr.auth_account is not null and pr.auth_account <> \\\"\\\") t;', 'PR-移动网Radius关联准确率统计任务-Mobilis', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (72, 'select \\\"{captureDay}\\\" as capture_day, \\\"Ooredoo\\\" as operator, \\\"Accuracy\\\" as statistics_type, COALESCE(sum(if(LOWER(auth_account) = LOWER(calling_station_id) and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)), 1, 0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE(CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if(LOWER(auth_account) = LOWER(calling_station_id) and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)), 1, 0)) / sum(1) END, 0) as statistics_value from ( select radius.internet_ip, radius.calling_station_id, radius.start_port, radius.end_port, radius.start_time, IF(radius.end_time is null, {end_time}, radius.end_time) as end_time, pr.auth_account, pr.capture_time from ( select internet_ip, calling_station_id, start_port, end_port, start_time, end_time from ( select internet_ip, calling_station_id, cast(split(port_range, \\\"-\\\")[0] as int) as start_port, cast(split(port_range, \\\"-\\\")[1] as int) as end_port, min(CASE WHEN action = \\\"Start\\\" THEN capture_time END) as start_time, max(CASE WHEN action = \\\"Stop\\\" THEN capture_time END) as end_time from v64_deye_dw_ods.ods_mobilenet_radius_ooredoo_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by internet_ip, calling_station_id, port_range having start_time is not null ) as t1 where (IF(t1.end_time is null, {end_time}, t1.end_time) - t1.start_time > {online_limit_min} * 60 * 1000) ) radius left join ( select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_http_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_im_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_email_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_remotectrl_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_ftp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_game_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_p2p_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_telnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_vpn_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_hardwarestring_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_shop_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_keyapp_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_video_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_overnet_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_voice_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_iot_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_webshare_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_digcurrency_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_hackertools_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_religion_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_vm_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_webbbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_picture_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_lbs_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_live_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_sns_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_payment_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_browser_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_navigation_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_inputmethod_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_weblogin_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_voip_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_mblog_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_govserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_basewifi_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_engine_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_webid_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_safety_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_news_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_netbank_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_financial_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_map_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_softinstall_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_appmarket_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, auth_account, src_port, capture_time from v64_deye_dw_ods.ods_pr_infoserv_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\") pr on pr.strsrc_ip = radius.internet_ip and pr.src_port >= radius.start_port and pr.src_port <= radius.end_port where pr.strsrc_ip is not null and pr.auth_account is not null and pr.auth_account <> \\\"\\\") t;', 'PR-移动网Radius关联准确率统计任务-Ooredoo', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (73, 'select \\\"{captureDay}\\\" as capture_day, \\\"Mobilis\\\" as operator, \\\"Association\\\" as statistics_type, COALESCE(sum(if (nf.user_name is not null and nf.user_name <> \\\"\\\", 1, 0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE( CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if (nf.user_name is not null and nf.user_name <> \\\"\\\", 1, 0)) / sum(1) END, 0) as statistics_value from ( select internet_ip, cast(split(port_range, \\\"-\\\")[0] as int) as start_port, cast(split(port_range, \\\"-\\\")[1] as int) as end_port from v64_deye_dw_ods.ods_mobilenet_radius_mobilis_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by internet_ip, port_range ) radius left join ( select user_name, strsrc_ip, src_port from v64_deye_dw_ods.ods_nf_other_log_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by user_name, strsrc_ip, src_port union all select user_name, strsrc_ip, src_port from v64_deye_dw_ods.ods_nf_url_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by user_name, strsrc_ip, src_port ) nf on radius.internet_ip = nf.strsrc_ip and nf.src_port >= radius.start_port and nf.src_port <= radius.end_port where nf.strsrc_ip is not null;', 'NF-移动网Radius关联率统计任务-Mobilis', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (74, 'select \\\"{captureDay}\\\" as capture_day, \\\"Ooredoo\\\" as operator, \\\"Association\\\" as statistics_type, COALESCE(sum(if (nf.user_name is not null and nf.user_name <> \\\"\\\", 1, 0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE( CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if (nf.user_name is not null and nf.user_name <> \\\"\\\", 1, 0)) / sum(1) END, 0) as statistics_value from ( select internet_ip, cast(split(port_range, \\\"-\\\")[0] as int) as start_port, cast(split(port_range, \\\"-\\\")[1] as int) as end_port from v64_deye_dw_ods.ods_mobilenet_radius_ooredoo_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by internet_ip, port_range ) radius left join ( select user_name, strsrc_ip, src_port from v64_deye_dw_ods.ods_nf_other_log_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by user_name, strsrc_ip, src_port union all select user_name, strsrc_ip, src_port from v64_deye_dw_ods.ods_nf_url_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by user_name, strsrc_ip, src_port ) nf on radius.internet_ip = nf.strsrc_ip and nf.src_port >= radius.start_port and nf.src_port <= radius.end_port where nf.strsrc_ip is not null;', 'NF-移动网Radius关联率统计任务-Ooredoo', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (75, 'select \\\"{captureDay}\\\" as capture_day, \\\"Mobilis\\\" as operator, \\\"Accuracy\\\" as statistics_type, COALESCE(sum(if(LOWER(user_name) = LOWER(calling_station_id) and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)), 1, 0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE(CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if(LOWER(user_name) = LOWER(calling_station_id) and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)), 1, 0)) / sum(1) END, 0) as statistics_value from ( select radius.internet_ip, radius.calling_station_id, radius.start_port, radius.end_port, radius.start_time, IF(radius.end_time is null, {end_time}, radius.end_time) as end_time, nf.user_name, nf.capture_time from ( select internet_ip, calling_station_id, start_port, end_port, start_time, end_time from ( select internet_ip, calling_station_id, cast(split(port_range, \\\"-\\\")[0] as int) as start_port, cast(split(port_range, \\\"-\\\")[1] as int) as end_port, min(CASE WHEN action = \\\"Start\\\" THEN capture_time END) as start_time, max(CASE WHEN action = \\\"Stop\\\" THEN capture_time END) as end_time from v64_deye_dw_ods.ods_mobilenet_radius_mobilis_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by internet_ip, calling_station_id, port_range having start_time is not null ) as t1 where (IF(t1.end_time is null, {end_time}, t1.end_time) - t1.start_time > {online_limit_min} * 60 * 1000) ) radius left join ( select strsrc_ip, user_name, src_port, capture_time from v64_deye_dw_ods.ods_nf_other_log_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, user_name, src_port, capture_time from v64_deye_dw_ods.ods_nf_url_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" ) nf on nf.strsrc_ip = radius.internet_ip and nf.src_port >= radius.start_port and nf.src_port <= radius.end_port where nf.strsrc_ip is not null and nf.user_name is not null and nf.user_name <> \\\"\\\") t;', 'NF-移动网Radius关联准确率统计任务-Mobilis', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (76, 'select \\\"{captureDay}\\\" as capture_day, \\\"Ooredoo\\\" as operator, \\\"Accuracy\\\" as statistics_type, COALESCE(sum(if(LOWER(user_name) = LOWER(calling_station_id) and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)), 1, 0)), 0) as auth_count, COALESCE(sum(1), 0) as radius_count, COALESCE(CASE WHEN sum(1) = 0 THEN 0 ELSE sum(if(LOWER(user_name) = LOWER(calling_station_id) and (capture_time >= start_time and capture_time <= (end_time + {logout_delay_seconds} * 1000)), 1, 0)) / sum(1) END, 0) as statistics_value from ( select radius.internet_ip, radius.calling_station_id, radius.start_port, radius.end_port, radius.start_time, IF(radius.end_time is null, {end_time}, radius.end_time) as end_time, nf.user_name, nf.capture_time from ( select internet_ip, calling_station_id, start_port, end_port, start_time, end_time from ( select internet_ip, calling_station_id, cast(split(port_range, \\\"-\\\")[0] as int) as start_port, cast(split(port_range, \\\"-\\\")[1] as int) as end_port, min(CASE WHEN action = \\\"Start\\\" THEN capture_time END) as start_time, max(CASE WHEN action = \\\"Stop\\\" THEN capture_time END) as end_time from v64_deye_dw_ods.ods_mobilenet_radius_ooredoo_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" group by internet_ip, calling_station_id, port_range having start_time is not null ) as t1 where (IF(t1.end_time is null, {end_time}, t1.end_time) - t1.start_time > {online_limit_min} * 60 * 1000) ) radius left join ( select strsrc_ip, user_name, src_port, capture_time from v64_deye_dw_ods.ods_nf_other_log_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" union all select strsrc_ip, user_name, src_port, capture_time from v64_deye_dw_ods.ods_nf_url_store where capture_day = \\\"{captureDay}\\\" and capture_hour between \\\"{mobilenet_start_hour}\\\" and \\\"{mobilenet_end_hour}\\\" ) nf on nf.strsrc_ip = radius.internet_ip and nf.src_port >= radius.start_port and nf.src_port <= radius.end_port where nf.strsrc_ip is not null and nf.user_name is not null and nf.user_name <> \\\"\\\" ) t;', 'NF-移动网Radius关联准确率统计任务-Ooredoo', '{\r\n    \"spark.driver.memory\": \"4g\",\r\n    \"spark.driver.cores\": \"2\",\r\n    \"spark.executor.memory\": \"16G\",\r\n    \"spark.executor.instances\": \"16\",\r\n    \"spark.executor.cores\": \"4\",\r\n    \"spark.default.parallelism\": \"16\"\r\n}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (77, '{\"size\":1000,\"_source\":[\"_id\"],\"query\":{\"bool\":{\"filter\":[{\"range\":{\"capture_timeField\":{\"gte\":{captureStartTime},\"lte\":{captureEndTime}}}}]}}}', '统计Radius/Phone线路ES-HBase数据量差异', '{\"sample_size\":10000}');
INSERT INTO `tb_ops_monitor_metric_sql` VALUES (78, 'INSERT INTO ads_ops.ops_pr_layer_diff_statistics (data_type,uparea_alias,data_layer,diff_num,capture_day,capture_hour,create_time) ( SELECT result_tab.data_type, result_tab.uparea_alias as uparea_alias, \'ODS-DWD\' AS data_layer, MAX(result_tab.ods_num)-MAX(result_tab.dwd_num) as diff_num, result_tab.capture_day, result_tab.capture_hour, UNIX_TIMESTAMP()*1000 as create_time  FROM ( SELECT t.data_type , t.uparea_alias , t.data_layer , t.capture_day , SUM(num) as num , SUM(CASE WHEN t.data_layer = \'ODS\' THEN t.num ELSE 0 END) AS ods_num, SUM(CASE WHEN t.data_layer = \'DWD\' THEN t.num ELSE 0 END) AS dwd_num, t.capture_hour FROM ads_ops.ops_pr_data_monitor_statistics_store t WHERE t.capture_day BETWEEN  %s AND  %s \r\n AND t.data_type IN (\'Call\',\'SMS\',\'Fax\') GROUP BY t.data_type , t.uparea_alias , t.data_layer , t.capture_day, t.capture_hour ORDER BY t.data_type ASC, t.data_layer ASC  ) result_tab GROUP BY result_tab.data_type , result_tab.capture_day, result_tab.capture_hour, result_tab.uparea_alias UNION ALL SELECT result_tab.data_type, result_tab.uparea_alias as uparea_alias, \'DWD-ADS\' AS data_layer, MAX(result_tab.dwd_num)-MAX(result_tab.ads_num) as diff_num, result_tab.capture_day, result_tab.capture_hour, UNIX_TIMESTAMP()*10000 as create_time  FROM ( SELECT t.data_type , t.uparea_alias , t.data_layer , t.capture_day , SUM(num) as num , SUM(CASE WHEN t.data_layer = \'DWD\' THEN t.num ELSE 0 END) AS dwd_num, SUM(CASE WHEN t.data_layer = \'ADS\' THEN t.num ELSE 0 END) AS ads_num, t.capture_hour FROM ads_ops.ops_pr_data_monitor_statistics_store t WHERE t.capture_day BETWEEN  %s AND  %s \r\n AND t.data_type IN (\'Call\',\'SMS\',\'Fax\')  GROUP BY t.data_type , t.uparea_alias , t.data_layer , t.capture_day, t.capture_hour ORDER BY t.data_type ASC, t.data_layer ASC  ) result_tab GROUP BY result_tab.data_type , result_tab.capture_day, result_tab.capture_hour, result_tab.uparea_alias);', '统计Phone数仓层数据差', ' ');

SET FOREIGN_KEY_CHECKS = 1;

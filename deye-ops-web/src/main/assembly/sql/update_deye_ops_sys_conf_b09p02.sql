UPDATE `deye_ops`.`tb_ops_business_sys_config` SET `sys_name` = '综合搜索', `sys_type` = 'SEARCH', `env` = 'PROD', `base_url` = 'http://192.168.80.158:8888/search', `auth_type` = 'BASIC', `auth_config` = '{\"account\": \"search_1\", \"auth_url\": \"http://192.168.80.158:8104/portal/singleLogin/login.json\", \"password\": \"39d9315cbd989fec964b3e7e91b88a68\", \"backLogin\": true}', `timeout` = 5000, `enable` = 1, `appid` = '23', `create_time` = **********, `modify_time` = ********** WHERE `id` = 1;
UPDATE `deye_ops`.`tb_ops_business_sys_config` SET `sys_name` = '全息档案', `sys_type` = 'ARCHIVE', `env` = 'PROD', `base_url` = 'http://192.168.80.158:8109/archives_web', `auth_type` = 'BASIC', `auth_config` = '{\"account\": \"archive_1\", \"auth_url\": \"http://192.168.80.158:8104/portal/singleLogin/login.json\", \"password\": \"cc0f2fea0ccf6c6719d58f5078167bbf\", \"backLogin\": true}', `timeout` = 5000, `enable` = 1, `appid` = '92', `create_time` = **********, `modify_time` = ********** WHERE `id` = 2;
UPDATE `deye_ops`.`tb_ops_business_sys_config` SET `sys_name` = '案件管理', `sys_type` = 'CASE', `env` = 'PROD', `base_url` = 'http://192.168.80.158:8105/case-system', `auth_type` = 'BASIC', `auth_config` = '{\"account\": \"guolijun_1\", \"auth_url\": \"http://192.168.80.158:8104/portal/singleLogin/login.json\", \"password\": \"fbec0584d547c9b61823eef5aa7f03d2\", \"backLogin\": true}', `timeout` = 5000, `enable` = 1, `appid` = '75', `create_time` = **********, `modify_time` = ********** WHERE `id` = 3;

DELETE FROM tb_ops_business_account_config WHERE sample_strategy <> 'FIXED';
server:
  servlet:
    #服务根路由设置
    context-path: /community_dig
  #服务端口设置
  port: 8898
  max-http-header-size: 65535
  tomcat:
    uri-encoding: utf-8
    basedir: /semptian/temp
    max-connections: 5000
    max-http-header-size: 8096
    max-threads: 1000
    #在 Doris 中用于指定在 SQL 查询中允许使用的特殊字符
    relaxed-query-chars:
      - "<"
      - ">"
      - "["
      - "]"
      - "^"
      - "`"
      - "{"
      - "|"
      - "}"
feign:
  hystrix:
    enabled: false
spring:
  resources:
    static-locations: classpath:/img/,classpath:/META-INF/resources/,classpath:/file/
  application:
    http:
      multipart:
        enabled: true
        max-file-size: 30MB
        max-request-size: 30MB
    jackson:
      date-format: yyyy-MM-dd HH:mm:ss
      time-zone: GMT+8
  profiles:
    #运行模式，dev:开发模式，test:测试模式，pro:生产模式
    active: dev
  main:
    allow-bean-definition-overriding: true
  #redis配置
  redis:
    cache-prefix: community_dig_v6.4
    blockWhenExhausted: false
    password: Hy@2023
    timeout: 1000
    cluster:
      nodes: *************:7001,*************:7001,*************:7001,*************:7002,*************:7002,*************:7002
      max-redirects: 3
    pool:
      max-active: 200
      max-wait: -1
      max-idle: 10
      min-idle: 0
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  #数据源配置
  datasource:
    dynamic:
      datasource:
        #mysql配置
        mysql:
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.jdbc.Driver
          url: *****************************************************************************************************
          password: 123456
          username: root
      druid:
        connection-error-retry-attempts: 3
        min-evictable-idle-time-millis: 300000
        pool-prepared-statements: true
        time-between-eviction-runs-millis: 60000
        max-wait: 60000
        break-after-acquire-failure: true
        min-idle: 2
        initial-size: 2
        filters: stat
        validation-query: select 1
        async-init: true
        max-active: 30
        remove-abandoned: false
        #time-between-connect-error-millis: 600000
        test-while-idle: true
      strict: false
      primary: mysql
  messages:
    basename: i18n.messages
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    jdbc-type-for-null: null
  global-config:
    id-type: 1
community:
  serviceType: 3
  upload:
    update:
      #更新群组记录批次大小
      max: 2000
  dbName:
    ads: ads_community_dig
    dwd: dwd
    dws: dws
  playUrl:
    # 配置地址需要以/结尾
    fax: http://192.168.80.111/mass/
    # 如果海量部署多个传真解析设备，需要使用英文逗号隔开
    base: 2/,3/
  redis:
    switch: false
  field:
    translate:
      cache:
        init: false
  task:
    parallel:
      #任务并发数
      size: 5
    timeout:
      #任务执行超时天数阈值
      day: 1
      # 每小时整点执行
      cron: 0 0 * * * ?
    scheduled:
      # 每隔5分钟执行一次
      cron: 0 0/5 * * * ?
  data:
    timeout:
      #临时数据过期天数阈值
      day: 3
      # 每两小时整点执行
      cron: 0 0 */2 * * ?
  relation:
    node:
      max: 200
    #是否启用doris进行目标扩线统计数据计算，0不使用，1使用
    statistics: 1
    doris:
      #在启用doris情况下，是否启用case语句方式统计，0不使用，1使用
      case: 1
    #并发统计目标扩线统计数据时单批次最大账号数
    max_count: 50
    latest:
      #目标扩线生产是否启用最新时间过滤，0不使用，1使用
      filter: 1
      #目标扩线生产启用最新时间过滤的天阈值
      day: 365
    delay:
      #数据读取延迟小时数
      hour: 8
  # 目标扩线图存储生命周期阈值，使用负数，保存3天，包含生产的那天
  lifecycle: -3
#需要确认一下对应的ip和端口
operate-log-url: http://**************:9300/log/add_log.json
#天河环境信息
tianhe:
  data:
    request:
      host: http://*************:8999/api/rest
  #天河基础认证地址
  auth:
    host: http://*************:8999
    client:
      id: hy
      secret: hy
      scope: email
  doris:
    service:
      #使用代理地址
      url: ************
    port: 9030
    username: root
    password: 123456
    dbaseName: ads_community_dig
    fePort: 8030
  nebula:
    community:
      service:
        #使用代理地址
        url: ************:9669,************:9669,************:9669
      username: root
      password: nebula
      spaceName: community
    phone-connection:
      service:
        #使用代理地址
        url: ************:9669,************:9669,************:9669
      username: root
      password: nebula
      spaceName: phone_connection
logging:
  level:
    com:
      semptian:
        base:
          template:
            TianHeDorisTemplate: WARN
      meiya:
        whalex:
          sdk:
            api:
              ApiCall: WARN
redis:
  lock:
    enable: true
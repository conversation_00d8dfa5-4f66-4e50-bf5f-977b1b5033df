package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Objects;

/**
 * 角色菜单实体类
 *
 * <AUTHOR>
 * @date 2020/2/28 11:44
 */
@Data
@TableName("tb_portal_role_permission")
public class RolePermissionEntity {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 角色id
     */
    @TableField("role_id")
    private String roleId;
    /**
     * 菜单id
     */
    @TableField("menu_id")
    private String menuId;
    /**
     * 操作id
     */
    @TableField("operate_id")
    private String operateId;
    /**
     * appId
     */
    @TableField("app_id")
    private Integer appId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RolePermissionEntity that = (RolePermissionEntity) o;
        return Objects.equals(menuId, that.menuId) &&
                Objects.equals(operateId, that.operateId) &&
                Objects.equals(appId, that.appId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(  menuId, operateId, appId);
    }

//    public static void main(String[] args) {
//        ArrayList<RolePermissionEntity> rolePermissionEntityList = new ArrayList<>();
//        RolePermissionEntity rolePermissionEntity = new RolePermissionEntity();
//        rolePermissionEntity.setId(1L);
//        rolePermissionEntity.setAppId(1);
//        rolePermissionEntity.setMenuId("1");
//        rolePermissionEntity.setOperateId("1");
//        rolePermissionEntity.setRoleId("1");
//        rolePermissionEntityList.add(rolePermissionEntity);
//        ArrayList<RolePermissionEntity> singlePermission = new ArrayList<>();
//        RolePermissionEntity rolePermissionEntity2 = new RolePermissionEntity();
//        rolePermissionEntity2.setId(2L);
//        rolePermissionEntity2.setAppId(1);
//        rolePermissionEntity2.setMenuId("1");
//        rolePermissionEntity2.setOperateId("1");
//        rolePermissionEntity2.setRoleId("2");
//        singlePermission.add(rolePermissionEntity2);
//        Collection<RolePermissionEntity> intersection1 = CollUtil.intersection(rolePermissionEntityList, singlePermission){
//
//        };
//        Collection<RolePermissionEntity> intersection = CollectionUtil.intersection(rolePermissionEntityList, singlePermission);
//        System.out.println(intersection);
//    }
}

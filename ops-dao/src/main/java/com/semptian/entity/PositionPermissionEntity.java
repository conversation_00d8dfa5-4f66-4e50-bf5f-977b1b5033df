package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/10
 * @Description
 **/
@Data
@TableName("tb_portal_position_permission")
public class PositionPermissionEntity {

    @TableId(type = IdType.UUID)
    private String id;

    @TableField("post_id")
    private Integer postId;
    /**
     * appId
     */
    @TableField("app_id")
    private Integer appId;
    /**
     * 菜单id
     */
    @TableField("menu_id")
    private String menuId;
    /**
     * 操作id
     */
    @TableField("operate_id")
    private String operateId;


}

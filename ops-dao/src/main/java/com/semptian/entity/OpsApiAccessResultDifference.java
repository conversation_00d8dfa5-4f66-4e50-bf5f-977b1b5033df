package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.Date;

/**
 * 系统间一致性统计结果差异表
 *
 * <AUTHOR> Hu
 * @since 2025/5/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("ops_api_access_result_difference")
public class OpsApiAccessResultDifference {

    @TableId(type = IdType.INPUT)
    private String metricId;

    @TableField("metric_name")
    private String metricName;

    @TableField("stat_date")
    private Date statDate;

    @TableField("account")
    private String account;

    @TableField("is_kv")
    private Integer isKv;

    @TableField("kv_content")
    private String kvContent;

    @TableField("different_kv_content")
    private String differentKvContent;
}

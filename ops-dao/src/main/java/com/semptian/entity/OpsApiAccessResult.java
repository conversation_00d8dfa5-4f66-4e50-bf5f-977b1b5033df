package com.semptian.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("ops_api_access_result")
public class OpsApiAccessResult {
    @TableId(type = IdType.INPUT)
    private String metricId;

    @TableField("stat_date")
    private Date statDate;

    @TableField("account")
    private String account;

    @TableField("system_name")
    private String systemName;

    @TableField("api_path")
    private String apiPath;

    @TableField("request_time")
    private Date requestTime;

    @TableField("response_status")
    private Short responseStatus;

    @TableField("cost_time")
    private Integer costTime;

    @TableField("response_data")
    private String responseData;

    @TableField("stat_count")
    private long statCount = 0L;

    @TableField(value = "is_success")
    private Boolean success;

    @TableField("error_code")
    private String errorCode;

    @TableField("is_kv")
    private boolean isKv;

    @TableField("kv_content")
    private String kvContent;

    @TableField("params")
    private JSONObject params;

    @TableField("is_dynamic")
    private Boolean isDynamic;

    @TableField("redundant_field")
    private String redundantField;
}
package com.semptian.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

@Data
@TableName(value = "tb_ops_business_account_config", autoResultMap = true)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class BusinessAccountConfig {

    @Id
    private Long id;


    private Long metricId;

    private AccountType accountType;

    private String accountValue;


    private Integer sampleWeight = 100;


    private SampleStrategy sampleStrategy = SampleStrategy.FIXED;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject caseParam;


    private String dynamic_condition_id;


    private Boolean enable = true;


    private Long createTime;


    private Long modifyTime;

    public enum AccountType {
        EMAIL, PHONE, RADIUS, IM, VIRTUAL
    }

    public enum SampleStrategy {
        FIXED, DYNAMIC
    }
}
package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.DetailImportTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDate;
import java.util.List;

/**
 * 明细导入任务Mapper
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Mapper
public interface DetailImportTaskMapper extends BaseMapper<DetailImportTask> {

    /**
     * 根据任务ID查询任务
     *
     * @param taskId 任务ID
     * @return 导入任务
     */
    @Select("SELECT * FROM tb_ops_detail_import_task WHERE task_id = #{taskId}")
    DetailImportTask getByTaskId(@Param("taskId") String taskId);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 状态
     * @param errorMessage 错误信息
     * @param endTime 结束时间
     */
    @Update("UPDATE tb_ops_detail_import_task SET status = #{status}, error_message = #{errorMessage}, " +
            "end_time = #{endTime}, modify_time = #{modifyTime} WHERE task_id = #{taskId}")
    void updateTaskStatus(@Param("taskId") String taskId, 
                         @Param("status") Integer status,
                         @Param("errorMessage") String errorMessage,
                         @Param("endTime") Long endTime,
                         @Param("modifyTime") Long modifyTime);

    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     * @param processedRows 已处理行数
     * @param successRows 成功行数
     * @param failedRows 失败行数
     */
    @Update("UPDATE tb_ops_detail_import_task SET processed_rows = #{processedRows}, " +
            "success_rows = #{successRows}, failed_rows = #{failedRows}, modify_time = #{modifyTime} " +
            "WHERE task_id = #{taskId}")
    void updateTaskProgress(@Param("taskId") String taskId,
                           @Param("processedRows") Long processedRows,
                           @Param("successRows") Long successRows,
                           @Param("failedRows") Long failedRows,
                           @Param("modifyTime") Long modifyTime);

    /**
     * 查询指定日期和指标的导入任务
     *
     * @param metricId 指标ID
     * @param targetDate 目标日期
     * @return 导入任务列表
     */
    @Select("SELECT * FROM tb_ops_detail_import_task WHERE metric_id = #{metricId} AND target_date = #{targetDate} " +
            "ORDER BY create_time DESC")
    List<DetailImportTask> getByMetricIdAndDate(@Param("metricId") Integer metricId, 
                                               @Param("targetDate") LocalDate targetDate);

    /**
     * 查询正在处理中的任务
     *
     * @return 处理中的任务列表
     */
    @Select("SELECT * FROM tb_ops_detail_import_task WHERE status = 1 ORDER BY create_time ASC")
    List<DetailImportTask> getProcessingTasks();

    /**
     * 清理过期任务记录（保留30天）
     */
    @Update("DELETE FROM tb_ops_detail_import_task WHERE create_time < #{expireTime}")
    void cleanupExpiredTasks(@Param("expireTime") Long expireTime);
}

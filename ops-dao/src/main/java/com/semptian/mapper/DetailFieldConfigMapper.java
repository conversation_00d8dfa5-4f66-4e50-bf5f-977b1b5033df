package com.semptian.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.DetailFieldConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 明细字段配置Mapper
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Mapper
@DS("mysql")
public interface DetailFieldConfigMapper extends BaseMapper<DetailFieldConfig> {

    /**
     * 根据协议类型获取导出字段配置
     *
     * @param dataType 协议类型
     * @return 字段配置列表
     */
    @Select("SELECT * FROM tb_ops_detail_field_config WHERE data_type = #{dataType} AND is_export = 1 ORDER BY id")
    List<DetailFieldConfig> getExportFieldsByDataType(@Param("dataType") String dataType);
}

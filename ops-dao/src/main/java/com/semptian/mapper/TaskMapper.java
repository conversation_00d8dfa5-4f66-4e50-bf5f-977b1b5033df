package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.semptian.entity.TaskEntity;
import com.semptian.enums.ModuleNameEnum;
import com.semptian.enums.TaskStatusEnum;
import com.semptian.enums.TimeModeEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface TaskMapper extends BaseMapper<TaskEntity> {

    @Select("<script>" +
            "SELECT * FROM tb_ops_task " +
            "<where>" +
            "    <if test='module != null'>module_name = #{module}</if>" +
            "    <if test='timeMode != null'>AND time_mode = #{timeMode}</if>" +
            "    <if test='status != null'>AND status = #{status}</if>" +
            "    <if test='taskName != null'>AND task_name LIKE CONCAT('%',#{taskName},'%')</if>" +
            "</where>" +
            "ORDER BY submit_time DESC" +
            "</script>")
    IPage<TaskEntity> selectTaskPage(IPage<TaskEntity> page,
                                     @Param("module") ModuleNameEnum module,
                                     @Param("timeMode") TimeModeEnum timeMode,
                                     @Param("status") TaskStatusEnum status,
                                     @Param("taskName") String taskName);

    @Update("UPDATE tb_ops_task SET status = #{executing} WHERE id = #{taskId}")
    void updateStatus(String taskId, String executing);
}
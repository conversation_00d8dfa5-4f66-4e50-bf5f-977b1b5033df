<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.semptian.mapper.BusinessApiConfigMapper">
    <sql id="searchCondition">
        <where>
            <if test="systemName != null and systemName != ''">
                AND system_name LIKE CONCAT('%', #{systemName}, '%')
            </if>
            <if test="apiPath != null and apiPath != ''">
                AND api_path LIKE CONCAT('%', #{apiPath}, '%')
            </if>
            <if test="authType != null">
                AND auth_type = #{authType}
            </if>
        </where>
    </sql>

    <select id="selectPageList" resultType="com.semptian.entity.BusinessApiConfig">
        SELECT * FROM tb_ops_business_api_config
        <include refid="searchCondition"/>
        ORDER BY create_time DESC
    </select>
</mapper>
import logging
from logging.handlers import TimedRotatingFileHandler
import os

# 获取当前文件的绝对路径
current_file_path = os.path.abspath(__file__)
# 获取当前文件所在的目录
current_dir = os.path.dirname(current_file_path)
# 获取上一级目录
parent_dir = os.path.dirname(current_dir)
# 在上一级目录中定位logs文件夹
log_dir = os.path.join(parent_dir, "logs")
log_file_info = os.path.join(log_dir, "deye_v64_ops_info.log")
log_file_error = os.path.join(log_dir, "deye_v64_ops_error.log")

# 确保日志目录存在
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# 定义日志格式
formatter = logging.Formatter(
    "%(asctime)s - %(levelname)s - %(message)s"
)


def get_logger(name="deye_v64_ops"):
    # 配置基础日志
    # --INFO--
    logger = logging.getLogger(name)

    # 避免重复添加handler
    if logger.handlers:
        return logger

    # 设置全局日志级别
    logger.setLevel(logging.INFO)

    # 配置INFO级别日志 - 使用基于时间的滚动策略
    info_handler = TimedRotatingFileHandler(
        filename=log_file_info,
        when="midnight",  # 每天午夜滚动
        interval=1,  # 每1天滚动一次
        backupCount=7,  # 保留最近7天的日志
        encoding="utf-8",
        utc=False  # 使用本地时间
    )
    info_handler.setLevel(logging.INFO)
    info_handler.setFormatter(formatter)
    info_handler.suffix = "%Y-%m-%d.log"  # 设置日志文件后缀格式
    logger.addHandler(info_handler)

    # 配置ERROR级别日志 - 使用基于时间的滚动策略
    error_handler = TimedRotatingFileHandler(
        filename=log_file_error,
        when="midnight",  # 每天午夜滚动
        interval=1,  # 每1天滚动一次
        backupCount=7,  # 保留最近7天的日志
        encoding="utf-8",
        utc=False  # 使用本地时间
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    error_handler.suffix = "%Y-%m-%d.log"  # 设置日志文件后缀格式
    logger.addHandler(error_handler)

    return logger


# 创建默认logger实例，方便直接导入使用
default_logger = get_logger()


def info(msg):
    """记录INFO级别日志"""
    default_logger.info(msg)


def error(msg):
    """记录ERROR级别日志"""
    default_logger.error(msg)

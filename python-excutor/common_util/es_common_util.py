import http.client
import json
from datetime import datetime, timedelta
import time
import sys
import os
import hashlib

# 添加父目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
import common_util.data_type_mapping as dtm
from common_util.hbase_common_util import HbaseConnect, generate_hbase_table_suffixes
from datetime import datetime, timezone
import math
import re
import common_util.log_common_util as logger


class ElasticSearchOperator:

    def __init__(self, es_conf):
        if type(es_conf) is str:
            self.es_conf = json.loads(es_conf)
        else:
            self.es_conf = es_conf

    def exec_query(self, index_names, dsl):
        try:
            # 创建HTTP连接
            conn = http.client.HTTPConnection(self.es_conf['host'], self.es_conf['port'], timeout=30)
            # 构建请求路径
            path = f"/{index_names}/_search"
            # 设置请求头
            headers = {
                'Content-Type': 'application/json'
            }
            logger.info(f"执行查询的DSL=>GET {index_names}/_search/\n{dsl}")
            # 发送 GET 请求
            conn.request("GET", path, body=dsl, headers=headers)
            # 获取响应
            response = conn.getresponse()

            # 检查响应状态码
            status = response.status
            if status != 200:
                error_msg = f"ES查询失败: 状态码 {status}, 原因: {response.reason}"
                logger.error(error_msg)
                raise Exception(error_msg)

            # 读取响应内容
            response_content = response.read().decode('utf-8')
            return response_content
        except Exception as e:
            logger.error(f"查询出错：{e}")
            raise
        finally:
            if conn:
                conn.close()

    def exec_scroll_query(self, index_names, dsl, scroll_id):
        """
        执行滚动查询
        :param self:
        :param index_names:
        :param dsl:
        :param scroll_id:
        :return:
        """
        conn = None
        try:
            # 创建HTTP连接
            conn = http.client.HTTPConnection(self.es_conf['host'], self.es_conf['port'], timeout=30)
            # 设置请求头
            headers = {
                'Content-Type': 'application/json'
            }
            if scroll_id:
                # 继续滚动查询
                path = "/_search/scroll"
                dsl = "{\"scroll\":\"1m\",\"scroll_id\":\"{scroll_id}\"}".replace("{scroll_id}", scroll_id)
            else:
                # 初次执行查询，会返回一个scroll_id，作为后续查询的参数
                path = f"/{index_names}/_search?scroll=1m"
                logger.info(f"执行查询的DSL=>POST {index_names}/_search?scroll=1m\n{dsl}")
            # 发送 POST 请求
            conn.request("POST", path, body=dsl, headers=headers)
            # 获取响应
            response = conn.getresponse()

            # 检查响应状态码
            status = response.status
            if status != 200:
                error_msg = f"ES滚动查询失败: 状态码 {status}, 原因: {response.reason}"
                logger.error(error_msg)
                raise Exception(error_msg)

            # 读取响应内容
            response_content = response.read().decode('utf-8')
            return response_content
        except Exception as e:
            logger.error(f"滚动查询出错：{e}")
            raise
        finally:
            if conn:
                conn.close()


def get_ads_count(es_conf, dsl, resource_names, captureStartDay, captureEndDay):
    """
    查询ads层es数据量统计
    :param es_conf:
    :param resource_names:资源名集合,逗号拼接
    :param dsl:
    :param captureStartDay:
    :param captureEndDay:
    :return:
    """
    resource_names_list = resource_names.split(",")
    dsl = dsl[0].replace("{captureStartDay}", captureStartDay).replace("{captureEndDay}", captureEndDay)
    es_operator = ElasticSearchOperator(es_conf)
    index_set = generate_es_index_suffixes(resource_names, captureStartDay, captureEndDay)
    index_names = str.join(",", index_set)
    # 执行查询
    result = es_operator.exec_query(index_names=index_names, dsl=dsl)
    # 需要将获取到的查询结果组装成对应doris的结构
    is_radius_resource = any(r in ("deye_v64_mobilenetradius_djezzy", "deye_v64_mobilenetradius_mobilis",
                                   "deye_v64_mobilenetradius_ooredoo", "deye_v64_fixednetradius") for r in
                             resource_names_list)
    if is_radius_resource:
        # 组装radius的数据
        return get_radius_data_list(result)
    else:
        # 组装其他协议的数据
        return get_other_data_list(result)


def process_resource_data(resource_name, dsl, es_operator, hbase_conn, captureStartDay, captureEndDay,
                          captureStartHour, sample_size,
                          uparea_id=None):
    """
    处理单个资源的数据查询与差异计算
    :param resource_name: 单个资源名称
    :param dsl:
    :param es_operator:
    :param hbase_conn:
    :param captureStartDay:
    :param captureEndDay:
    :param captureStartHour
    :param sample_size: 配置的抽样数
    :param uparea_id:
    :return:
    """
    # 生成索引名和表名
    index_set = generate_es_index_suffixes(resource_name, captureStartDay, captureEndDay)
    index_names = ",".join(index_set)
    detail_table_name = generate_hbase_table_suffixes(resource_name, captureStartDay, captureEndDay)

    # 执行 ES 查询
    try:
        result = es_operator.exec_scroll_query(index_names=index_names, dsl=dsl, scroll_id="")
        result_json = json.loads(result)
    except json.JSONDecodeError as e:
        logger.error(f"ES query failed for {resource_name}: {e}")
        return [], []
    # 获取匹配的 IDs
    ids = scroll_query([], sample_size, result_json, es_operator)
    # 计算 HBase 差异
    try:
        hbase_num = hbase_conn.get_rows_count(detail_table_name, ids)
    except Exception as e:
        logger.error(f"HBase query failed for {resource_name}: {e}")
        hbase_num = 0

    # 构建数据记录
    data = {
        "data_type": dtm.resource_data_type[resource_name],
        "data_layer": "ES-HBase",
        "capture_day": captureStartDay,
        "capture_hour": captureStartHour,
        "diff_num": calculate_diff_ratio(len(ids), hbase_num),
        "create_time": int(time.time() * 1000)
    }
    if uparea_id is not None:
        data["uparea_alias"] = uparea_id if uparea_id == "" else dtm.uparea_mapping[uparea_id]

    return ids, data


def calculate_diff_ratio(es_count, hbase_count):
    """计算差异比例，避免除零错误"""
    if es_count == 0:
        return 0.0
    logger.info(f"计算抽样的差异率{es_count}-{hbase_count}/{es_count}")
    return round((es_count - hbase_count) / es_count, 4)


def get_es_hbase_diff(task_conf, dsl, resource_names, captureStartDay, captureEndDay, captureStartHour, captureEndHour):
    """
    根据传入的参数获取
    :param task_conf: 包含task_conf['es_conf'].task_conf['hbase_conf']
    :param dsl:
    :param resource_names:
    :param captureStartDay:
    :param captureEndDay:
    :param captureStartHour:
    :param captureEndHour:
    :return:
    """
    resource_names_list = resource_names.split(",")
    # 当前抽样最大的值
    sample_size = json.loads(dsl[1])["sample_size"]
    # 替换对应的时间戳
    dsl_template = dsl[0].replace("{captureStartTime}", get_timestamp(captureStartDay, captureStartHour)).replace(
        "{captureEndTime}", get_timestamp(captureEndDay, captureEndHour))
    # 声明es操作类
    es_operator = ElasticSearchOperator(task_conf["es_conf"])
    # 声明hbase操作
    hbase_conn = HbaseConnect(task_conf["hbase_conf"])

    datas = []

    # 判断是否是radius
    is_radius_resource = any(r in ("deye_v64_mobilenetradius_djezzy", "deye_v64_mobilenetradius_mobilis",
                                   "deye_v64_mobilenetradius_ooredoo", "deye_v64_fixednetradius") for r in
                             resource_names_list)
    # 判断是否为Phone
    is_phone_resource = any(r in ("deye_v64_call", "deye_v64_sms", "deye_v64_fax") for r in resource_names_list)
    if is_radius_resource:
        for resource_name in resource_names_list:
            _, data = process_resource_data(resource_name, dsl_template, es_operator, hbase_conn,
                                            captureStartDay, captureEndDay, captureStartHour, sample_size)
            datas.append(data)
    elif is_phone_resource:
        for resource_name in resource_names_list:
            _, data = process_resource_data(resource_name, dsl_template, es_operator, hbase_conn,
                                            captureStartDay, captureEndDay, captureStartHour, sample_size, "")
            datas.append(data)
    else:
        # 获取采集地集合
        uparea_ids = dtm.uparea_mapping.keys()
        for uparea_id in uparea_ids:
            # 需要执行三个采集的dsl
            dsl_modified = dsl_template.replace("{uparea_id}", uparea_id)
            for resource_name in resource_names_list:
                _, data = process_resource_data(resource_name, dsl_modified, es_operator, hbase_conn,
                                                captureStartDay, captureEndDay, captureStartHour, sample_size,
                                                uparea_id)
                datas.append(data)
    return datas


def scroll_query(ids, sample_size, result_json, es_operator):
    """
    滚动查询，并获取所有的_id一直到取够采样数为止。或者
    :param ids:
    :param sample_size:
    :param result_json:
    :param es_operator:
    :return:
    """
    # 使用迭代而不是递归，避免栈溢出
    while True:
        try:
            # 检查结果是否有效
            if not result_json or "hits" not in result_json or "hits" not in result_json["hits"]:
                logger.warning("滚动查询返回的结果格式不正确")
                return ids

            # 提取当前批次的ID
            current_hits = result_json["hits"]["hits"]
            for hit in current_hits:
                ids.append(hit["_id"])

            # 检查是否已经满足采样数量或者没有更多结果
            if len(current_hits) == 0 or len(ids) >= sample_size:
                return ids

            # 获取滚动ID并继续查询
            if "_scroll_id" not in result_json:
                logger.warning("滚动查询结果中没有scroll_id")
                return ids

            scroll_id = result_json["_scroll_id"]
            result = es_operator.exec_scroll_query(index_names="", dsl="", scroll_id=scroll_id)
            result_json = json.loads(result)

        except json.JSONDecodeError as e:
            logger.error(f"滚动查询JSON解析异常: {e}")
            return ids
        except Exception as e:
            logger.error(f"滚动查询异常: {e}")
            return ids


def get_other_data_list(result):
    """
    组装除radius外的其他协议的数据结果
    :param result:
    :return:
    """
    if result:
        try:
            result_json = json.loads(result)
            base_data = {"data_layer": "ADS", "capture_hour": 0}
            data_list = [
                {
                    **base_data,
                    "data_type": dtm.data_type_mapping[dt_bucket["key"]]['data_type_alias'],
                    "original_data_type": dtm.data_type_mapping[dt_bucket["key"]]['data_type_alias'],
                    "capture_day": cd_bucket["key"],
                    "uparea_alias": ua_bucket.get("key", ""),
                    "num": ua_bucket["doc_count"],
                    "create_time": int(time.time() * 1000)
                }
                for dt_bucket in result_json["aggregations"]["data_type"]["buckets"]
                for cd_bucket in dt_bucket["capture_day"]["buckets"]
                # 处理空值：若无 uparea_alias，则生成默认 Bucket
                for ua_bucket in cd_bucket.get("uparea_alias", {}).get("buckets", [])
                                 or [{"key": "", "doc_count": cd_bucket.get("doc_count", 0)}]
            ]
            return data_list
        except Exception as e:
            logger.error(f"组装ES统计结果异常:{e},result=>{result}")


def get_radius_data_list(result):
    """
    组装radius的数据量统计
    :param result:
    :return:
    """
    if result:
        try:
            result_json = json.loads(result)
            base_data = {"data_layer": "ADS", "capture_hour": 0}
            data_list = [
                {
                    **base_data,
                    "data_type": dtm.data_type_mapping[dt_bucket["key"]]['data_type_alias'],
                    "capture_day": cd_bucket["key"],
                    "operator": dtm.operator_mapping[
                        "99" if op_bucket.get("key", "") == "" else op_bucket.get("key", "99")],
                    "num": op_bucket["doc_count"],
                    "create_time": int(time.time() * 1000)
                }
                for dt_bucket in result_json["aggregations"]["data_type"]["buckets"]
                for cd_bucket in dt_bucket["capture_day"]["buckets"]
                # 处理空值：若无 operator，则生成默认 Bucket
                for op_bucket in cd_bucket.get("operator", {}).get("buckets", [])
                                 or [{"key": "99", "doc_count": cd_bucket.get("doc_count", 0)}]
            ]
            return data_list
        except Exception as e:
            logger.error(f"组装ES-Radius统计结果异常:{e},result=>{result}")


def generate_es_index_suffixes(indexs: str, captureStartDay, captureEndDay) -> set[str]:
    """
    根据传入的时间参数，计算索引的时间后缀。并替换
    :param indexs:
    :param captureStartDay:
    :param captureEndDay:
    :return:
    """
    try:
        start_date = datetime.strptime(captureStartDay, "%Y-%m-%d")
        end_date = datetime.strptime(captureEndDay, "%Y-%m-%d")
    except ValueError:
        raise ValueError("时间格式必须为 'YYYY-MM-DD'")
        # 校验时间跨度 ≤7 天
    delta = end_date - start_date
    if delta.days < 0:
        raise ValueError("结束时间不能早于起始时间")
    if delta.days > 6:  # 最大7天（含起始和结束）
        raise ValueError("时间跨度不能超过7天")
    # 遍历日期范围，收集所有涉及的月份
    suffixes = set()
    current_date = start_date
    while current_date <= end_date:
        # 生成索引后缀（格式：YYYYMM）
        suffix = current_date.strftime("%Y%m")
        suffixes.add(suffix)
        current_date += timedelta(days=1)
    # 按时间顺序排序并返回
    sorted_suffixes = sorted(suffixes)
    index_set = set()
    for suffix in sorted_suffixes:
        for index in indexs.split(','):
            index_set.add(index + "_" + suffix)
    return index_set


def get_timestamp(date_str: str, hour_str: str) -> str:
    """
    根据传入的日期+小时数计算对应的时间戳
    :param date_str:例如2025-03-31
    :param hour_str:例如00
    :return:
    """
    # 合并日期和小时，格式化为完整时间字符串
    full_time_str = f"{date_str} {hour_str.zfill(2)}:00:00"
    # 解析为 datetime 对象（假设为本地时间）
    dt = datetime.strptime(full_time_str, "%Y-%m-%d %H:%M:%S")
    # 转换为时间戳（单位：秒）
    return str(dt.timestamp() * 1000)


# 批次数
BATCH = 1000


def get_archive_data(es_conf, dsl, index_name, archive_datas, captureStartDay, captureEndDay):
    """
    根据hive表中查询的档案账号查询该时段内没有.
    此处需要注意：ES中的实际建档数，需要加一天。昨天的理论建档数，需要考虑昨天和今天的当前小时数的
    :param es_conf:
    :param index_name: 索引名称
    :param dsl:
    :param archive_datas: 索引名称+app_type
    :param captureStartDay: 昨日日期 2025-03-31
    :param captureEndDay: 今日日期 2025-04-01
    :return:
    """
    start_time = get_timestamp(date_str=captureStartDay, hour_str="00")
    # end_time = get_timestamp(date_str=captureEndDay, hour_str=datetime.now().strftime("%H"))
    es_operator = ElasticSearchOperator(es_conf)
    if archive_datas is None or len(archive_datas) == 0:
        return process_archive_data(index_name, [],
                                    es_operator,
                                    dsl, captureStartDay, start_time)

    statistics_data_list, detail_data_list = process_archive_data(index_name=index_name,
                                                                  archive_datas=archive_datas,
                                                                  es_operator=es_operator,
                                                                  dsl=dsl, captureStartDay=captureStartDay,
                                                                  start_time=start_time)
    return statistics_data_list, detail_data_list


def process_archive_data(index_name, archive_datas, es_operator, dsl, captureStartDay, start_time):
    """
     用来统计实际建档数。并构造出入库doris的数据
    :param index_name:
    :param archive_datas: 包含查询到的理论建档数据
    :param es_operator:
    :param dsl:
    :param captureStartDay:
    :param start_time:
    :return:
    """
    # 漏建档的档案
    actual_total = 0
    # 将所有的账号数据拼接。用OR拼接。这里需要拆分
    # 漏建档的明细
    detail_data_list = []
    batch_size = math.ceil(len(archive_datas) / BATCH)
    for i in range(0, batch_size):
        params = []
        # 按照对应的批次获取id集合批次查询hbase
        st = i * BATCH
        arc_datas_batch = archive_datas[st:st + BATCH]
        if dtm.resource_data_type[index_name] == 'IM' or dtm.resource_data_type[index_name] == 'Application' \
                or dtm.resource_data_type[index_name] == 'Fixed RADIUS':
            for ad in arc_datas_batch:
                archive_name = ad["archive_name"]
                app_type = ad["app_type"]
                if dtm.resource_data_type[index_name] == 'IM':
                    id = calculate_string_md5("103_" + archive_name + app_type)
                elif dtm.resource_data_type[index_name] == 'Fixed RADIUS':
                    # radius 的实际为auth_type
                    id = calculate_string_md5(app_type + "_" + archive_name)
                else:
                    id = calculate_string_md5(archive_name + "_" + app_type)
                params.append(id)
        else:
            arc_names = [archive_data["archive_name"] for archive_data in arc_datas_batch]
            # if dtm.resource_data_type[index_name] == 'Fixed IP':
            #     # 如果是固定ip类型的档案。需要过滤格式为ip格式的档案
            #     arc_names = filter_ips(arc_names)
            params.extend(arc_names)

        # 需要追加双引号
        arc_names_batch_str = [f'\\"{s}\\"' for s in params]
        query_str = str.join(" OR ", arc_names_batch_str)
        # 替换时间戳只要大于昨日0点
        es_dsl = dsl[0].replace("{query_str}", query_str).replace("{captureStartDay}", start_time)
        # 执行查询.结果为有更新的实际档案账号。这些账号需要记录到详情表。总数记录到统计表。需要将理论的建档账号排除掉这些账号
        result = es_operator.exec_query(index_names=index_name, dsl=es_dsl)
        try:
            if result:
                result_json = json.loads(result)
                count = result_json["hits"]["total"]["value"]
                if count > BATCH:
                    logger.error(f"本次查询总数超过批次数{es_dsl}")
                actual_total += count
                # 获取本次查询的所有已经建档的数据
                hists_archive_names = set([hit["_source"]["archive_name"] for hit in result_json["hits"]["hits"]])
                hists_archive_ids = set([hit["_id"] for hit in result_json["hits"]["hits"]])
                # 遍历理论应该建档的账号，找出不在实际建档的账号集合也不在实际的id集合的账号
                miss_archive_list = set([hit for hit in params if (
                        hit not in hists_archive_names and hit not in hists_archive_ids)])
                logger.info(f"本次查询结果=>{count},理论应查询{len(params)}")
                # 遍历本批次的数据，找出漏建档的数据
                detail_list = []
                for hit in arc_datas_batch:
                    # 获取必要的值，使用get方法避免KeyError
                    archive_name = hit["archive_name"]
                    app_type = hit.get("app_type", "other")

                    # 预先计算可能的ID，避免重复计算
                    im_id = calculate_string_md5("103_" + archive_name + app_type)
                    app_id = calculate_string_md5(archive_name + "_" + app_type)

                    # 检查是否为漏建档数据
                    if (archive_name in miss_archive_list or
                            im_id in miss_archive_list or
                            app_id in miss_archive_list):
                        detail_list.append({
                            "data_type": dtm.resource_data_type[index_name],
                            "capture_day": captureStartDay,
                            "app_type": app_type,
                            "missing_doc_account": archive_name,
                            "create_time": int(time.time() * 1000)
                        })
                detail_data_list.extend(detail_list)
        except Exception as e:
            logger.error(f"组装统计结果异常:{e},result=>{result}")
    # 组装好详情数据、统计数据。同步返回
    statistics_data_list = [
        {
            "data_type": dtm.resource_data_type[index_name],
            "capture_day": captureStartDay,
            # 计算实际建档数据
            "actual_number": actual_total,
            # 查询到的理论建档数
            "theoretical_number": len(archive_datas),
            "create_time": int(time.time() * 1000)
        }
    ]
    return statistics_data_list, detail_data_list


def filter_ips(ip_list):
    """
    筛选ip格式的ip
    :param ip_list:
    :return:
    """
    # 组合正则
    # IPv4正则（严格验证0-255）
    IPV4_PATTERN = r"^((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])$"

    # IPv6正则（支持缩写::和混合大小写）
    IPV6_PATTERN = r"^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|" \
                   r"((::([0-9a-fA-F]{1,4}:){0,6}[0-9a-fA-F]{1,4})|" \
                   r"([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){0,6}(:[0-9a-fA-F]{1,4}){1,6}|::)|::([fF]{4}:)?((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d))$"

    IP_REGEX = re.compile(f"({IPV4_PATTERN})|({IPV6_PATTERN})")
    """过滤合法IP列表"""
    return [ip for ip in ip_list if IP_REGEX.match(ip.strip())]


def calculate_string_md5(text):
    """计算字符串的MD5哈希值"""
    return hashlib.md5(text.encode('utf-8')).hexdigest()

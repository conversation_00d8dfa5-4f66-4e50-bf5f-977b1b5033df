import base64
import uuid
import http.client
import sys
import os

# 添加父目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
import common_util.data_type_mapping as dtm
import json
import time
from common_util.mysql_common_util import *
import common_util.log_common_util as logger


class DorisStreamLoader:
    def __init__(self, host, port, database, table_name):
        self.host = host
        self.port = port
        self.load_url = f"/api/{database}/{table_name}/_stream_load"

    def basic_auth_header(self, username, password):
        auth_string = f"{username}:{password}".encode('utf-8')
        encoded_auth = base64.b64encode(auth_string).decode('utf-8')
        return f"Basic {encoded_auth}"

    def load_json(self, json_data, username, password, columns):
        auth_header = self.basic_auth_header(username, password)
        # 这里需要改为动态传入
        headers = {
            'Expect': '100-continue',
            'Authorization': auth_header,
            'label': str(uuid.uuid4()),
            'format': 'json',
            'strip_outer_array': 'true',
            'columns': columns,
            'Content-Type': 'application/json'
        }

        url = f"http://{self.host}:{self.port}{self.load_url}"
        max_redirects = 5
        redirect_count = 0

        while True:
            if redirect_count > max_redirects:
                raise Exception("Too many redirects")

            parsed_url = http.client.urlsplit(url)
            host = parsed_url.hostname
            port = parsed_url.port if parsed_url.port else 80
            path = parsed_url.path + ('?' + parsed_url.query if parsed_url.query else '')

            try:
                conn = http.client.HTTPConnection(host, port)
                conn.request("PUT", path, body=json_data, headers=headers)
                response = conn.getresponse()
                status_code = response.status
                load_result = response.read().decode('utf-8')

                if status_code == 307:
                    new_url = response.getheader('Location')
                    if not new_url:
                        raise Exception("Received 307 redirect without Location header")
                    url = new_url
                    redirect_count += 1
                elif status_code != 200:
                    raise Exception(f"Stream load failed. status: {status_code} load result: {load_result}")
                else:
                    result_json = json.loads(load_result)
                    logger.info(result_json.get('Message', load_result))
                    break
            except Exception as e:
                logger.error(f"An error occurred: {e}")
                break
            finally:
                if conn:
                    conn.close()


def loadToDoris(outputs, target_table_name, columns, doris_conf):
    """
    装载数据写入doris
    :param outpus: 待装载的数str数据
    :return:
    """
    if outputs is None or outputs == "":
        return
    doris_conf = json.loads(doris_conf)
    splitlines = outputs.splitlines()
    # 待写入的数据集合
    datas = []
    columns_arr = columns.split(",")
    for line in splitlines:
        if line == "":
            continue
        splits = line.split("\t")
        data = {}
        for i, column in enumerate(columns_arr):
            if i == len(columns_arr) - 1:
                data[column] = int(time.time() * 1000)
            else:
                if column == 'data_type' and splits[i] in dtm.data_type_mapping.keys():
                    # 需要替换 data_type 协议名称为合并后的名称
                    data[column] = dtm.data_type_mapping[splits[i]]['data_type_alias']
                elif column == 'original_data_type' and splits[i] in dtm.data_type_mapping.keys():
                    # 需要替换 original_data_type 协议名称为合并后的名称
                    data[column] = dtm.data_type_mapping[splits[i]]['original_data_alias']
                elif column == 'uparea_alias' and splits[i] in dtm.uparea_mapping.keys():
                    # 替换上报地编码为对应的名称
                    data[column] = dtm.uparea_mapping[splits[i]]
                elif column == 'operator' and splits[i] in dtm.operator_mapping.keys():
                    # 运营商映射
                    data[column] = dtm.operator_mapping[splits[i]]
                else:
                    data[column] = splits[i]
        datas.append(data)
    all_data_str = json.dumps(datas)
    logger.info(f"all_data_str=>{all_data_str}")
    loader = DorisStreamLoader(doris_conf["host"], doris_conf["port"], doris_conf["database"], target_table_name)
    try:
        loader.load_json(all_data_str, doris_conf["username"], doris_conf["password"], columns)
    except Exception as e:
        logger.error(f"An error occurred: {e}")


def loadToDorisForCompleteDataCSV(outputs, target_table_name, columns, doris_conf):
    """
    装载数据写入doris
    :param outpus: 待装载的数str数据
    :return:
    """
    if outputs is None or outputs == "":
        return
    doris_conf = json.loads(doris_conf)
    splitlines = outputs.splitlines()
    # 待写入的数据集合
    datas = []
    columns_arr = columns.split(",")
    for line in splitlines:
        if line == "":
            continue
        splits = line.split("\t")
        data = {}
        for i, column in enumerate(columns_arr):
            if i == len(columns_arr) - 1:
                data[column] = int(time.time() * 1000)
            else:
                data[column] = splits[i]
        datas.append(data)
    all_data_str = json.dumps(datas)
    logger.info(f"all_data_str=>{all_data_str}")
    loader = DorisStreamLoader(doris_conf["host"], doris_conf["port"], doris_conf["database"], target_table_name)
    try:
        loader.load_json(all_data_str, doris_conf["username"], doris_conf["password"], columns)
    except Exception as e:
        logger.error(f"An error occurred: {e}")


def loadToDorisForCompleteData(datas, target_table_name, doris_conf):
    """
    直接将组装好的写入doris
    :param datas:
    :param target_table_name:
    :param doris_conf:
    :return:
    """
    doris_conf = json.loads(doris_conf)
    all_data_str = json.dumps(datas)
    logger.info(f"all_data_str=>{all_data_str}")
    loader = DorisStreamLoader(doris_conf["host"], doris_conf["port"], doris_conf["database"], target_table_name)
    try:
        loader.load_json(all_data_str, doris_conf["username"], doris_conf["password"], "")
    except Exception as e:
        logger.error(f"An error occurred: {e}")


def exec_insert_select(doris_conf, sql, captureStartDay, captureEndDay):
    """
    执行insert_select方法
    :param doris_conf: doris配置项
    :param sql:
    :param captureStartDay:
    :param captureEndDay:
    :return:
    """
    doris_conf = json.loads(doris_conf)
    doris_conf_ = {
        "host": doris_conf["host"],
        "user": doris_conf["username"],
        "port": doris_conf["fe_port"],
        "password": doris_conf["password"],
        "database": doris_conf["database"],
        "raise_on_warnings": True,
    }
    doris_connect = MySqlConnect(doris_conf_)
    params = ()
    exc_sql = sql.format(captureStartDay=captureStartDay, captureEndDay=captureEndDay)
    doris_connect.execute_sql(query_sql=exc_sql, params=params)

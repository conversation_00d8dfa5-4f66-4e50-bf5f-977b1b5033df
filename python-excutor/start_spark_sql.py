import argparse
import json
import subprocess

from common_util.doris_common_util import loadToDoris
from common_util.mysql_common_util import getProductSql
import common_util.log_common_util as logger

# 定义 spark-sql 命令
spark_sql_command = """
spark-sql -e "{}" \
--master yarn \
--name {} \
--conf spark.driver.memory={} \
--conf spark.driver.cores={} \
--conf spark.executor.memory={} \
--conf spark.executor.cores={} \
--conf spark.executor.instances={} \
--conf spark.default.parallelism={}
"""


def execSparkCommand(sparkSql, task_name, remote_server, captureStartDay, captureEndDay,
                     captureStartHour,
                     captureEndHour):
    """
    执行spark-sql
    :param sparkSql: 待执行的sql
    :param task_name: 任务名称
    :param remote_server: 远程执行spark_sql主机地址
    :param captureStartDay: 生产时间起始时间
    :param captureEndDay:  生产时间截止时间
    :return:
    """
    # 加载资源配置
    recConfJson = json.loads(sparkSql[1])
    if captureStartHour != "" and captureEndHour != "":
        # 若包含起止小时，则拼接起止到sql
        sql = sparkSql[0].format(captureStartDay=captureStartDay, captureEndDay=captureEndDay,
                                 captureStartHour=captureStartHour, captureEndHour=captureEndHour)
    else:
        # sql拼接起止时间
        sql = sparkSql[0].format(captureStartDay=captureStartDay, captureEndDay=captureEndDay)
    command = spark_sql_command.format(
        sql,
        task_name,
        recConfJson['spark.driver.memory'],
        recConfJson['spark.driver.cores'],
        recConfJson['spark.executor.memory'],
        recConfJson['spark.executor.cores'],
        recConfJson['spark.executor.instances'],
        recConfJson['spark.default.parallelism'],
    )
    logger.info(f"本次执行的命令{command}")
    # 构建包含 ssh 的完整命令
    ssh_command = f"ssh -o StrictHostKeyChecking=no {remote_server} '{command}'"

    try:
        # 执行命令并捕获输出
        result = subprocess.run(ssh_command, shell=True, capture_output=True, text=True, check=True)
        # logger.info(result)
        # 获取标准输出
        output = result.stdout
        # 打印结果
        # logger.info("查询结果：")
        # logger.info(output)
        return output
    except subprocess.CalledProcessError as e:
        # 若命令执行出错，打印错误信息
        logger.error(f"执行命令时出错: {e.stderr}")


def main():
    try:
        parser = argparse.ArgumentParser(
            description="这是一个接收命令行参数的示例脚本。"
        )
        # 处理命令行参数
        # 添加参数
        parser.add_argument("--task_conf", type=str, help="任务配置参数")
        parser.add_argument("--sql_id", type=str, help="待执行sql_id")
        parser.add_argument("--capture_start_day", type=str, help="天任务区间开始时间")
        parser.add_argument("--capture_end_day", type=str, help="天任务区间截止时间")

        parser.add_argument("--capture_start_hour", type=str, default="", help="小时任务区间开始小时")
        parser.add_argument("--capture_end_hour", type=str, default="", help="小时任务区间截止小时")

        # 解析参数
        args = parser.parse_args()
        # 获取任务配置
        task_conf = json.loads(args.task_conf)

        sql_id = getProductSql(args.sql_id, task_conf["mysql_conf"])
        # 输出str类型的结果
        outputs = execSparkCommand(sql_id, task_conf["spark_task_name"], task_conf["spark_remote_host"],
                                   args.capture_start_day,
                                   args.capture_end_day,
                                   args.capture_start_hour,
                                   args.capture_end_hour
                                   )
        # 写入doris
        loadToDoris(outputs=outputs,
                    target_table_name=task_conf["target_table_name"],
                    columns=task_conf["columns"],
                    doris_conf=task_conf["doris_conf"])
    except argparse.ArgumentError as e:
        logger.error(f"命令行参数解析错误: {e}")
    except Exception as e:
        logger.error(f"发生未知错误: {e}")


if __name__ == "__main__":
    main()

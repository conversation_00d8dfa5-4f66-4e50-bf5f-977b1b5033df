import argparse
import json
import subprocess

from common_util.mysql_common_util import getProductSql
from common_util.doris_common_util import loadToDoris
import common_util.log_common_util as logger

# 定义 spark-sql 命令
spark_sql_command = """
spark-sql -e "{}" \
--master yarn \
--name {} \
--conf spark.driver.memory={} \
--conf spark.driver.cores={} \
--conf spark.executor.memory={} \
--conf spark.executor.cores={} \
--conf spark.executor.instances={} \
--conf spark.default.parallelism={}
"""


def execSparkCommand(sparkSql, task_name, remote_server, captureDay,
                     start_time,
                     end_time,
                     mobilenet_start_hour,
                     mobilenet_end_hour,
                     fixed_start_hour,
                     fixed_end_hour,
                     limit_count,
                     logout_delay_seconds,
                     online_limit_min
                     ):
    """
    执行spark-sql
    :param sparkSql: 待执行的sql
    :param task_name: 任务名称
    :param remote_server: 远程执行spark_sql主机地址
    :param captureDay: 生产时间日期
    :param start_time: 生产时间起始时间
    :param end_time:  生产时间截止时间
    :param mobilenet_start_hour: 移动网开始小时
    :param mobilenet_end_hour: 移动网结束小时
    :param fixed_start_hour: 固网开始小时
    :param fixed_end_hour: 固网结束小时
    :param limit_count: 限制记录数
    :param logout_delay_seconds: 下线延迟秒数
    :param online_limit_min: 上线限制最小分钟数
    :return:
    """
    # 加载资源配置
    sql = sparkSql[0].format(captureDay=captureDay, start_time=start_time, end_time=end_time,
                             mobilenet_start_hour=mobilenet_start_hour, mobilenet_end_hour=mobilenet_end_hour,
                             fixed_start_hour=fixed_start_hour, fixed_end_hour=fixed_end_hour, limit_count=limit_count,
                             logout_delay_seconds=logout_delay_seconds, online_limit_min=online_limit_min)
    recConfJson = json.loads(sparkSql[1])
    command = spark_sql_command.format(
        sql,
        task_name,
        recConfJson['spark.driver.memory'],
        recConfJson['spark.driver.cores'],
        recConfJson['spark.executor.memory'],
        recConfJson['spark.executor.cores'],
        recConfJson['spark.executor.instances'],
        recConfJson['spark.default.parallelism'],
    )
    logger.info(f"本次执行的命令{command}")
    # 构建包含 ssh 的完整命令
    ssh_command = f"ssh -o StrictHostKeyChecking=no {remote_server} '{command}'"

    try:
        # 执行命令并捕获输出
        result = subprocess.run(ssh_command, shell=True, capture_output=True, text=True, check=True)
        # logger.info(result)
        # 获取标准输出
        output = result.stdout
        # 打印结果
        # logger.info("查询结果：")
        # logger.info(output)
        return output
    except subprocess.CalledProcessError as e:
        # 若命令执行出错，打印错误信息
        logger.error(f"执行命令时出错: {e.stderr}")


def main():
    try:
        """
        --task_conf {\"mysql_conf\":{\"user\":\"root\",\"password\":\"123456\",\"host\":\"*************\",\"database\":\"deye_ops\",\"raise_on_warnings\":true},\"doris_conf\":{\"host\":\"************\",\"port\":8030,\"fe_port\":9030,\"database\":\"ads_ops\",\"username\":\"root\",\"password\":\"123456\"},\"spark_remote_host\":\"************\",\"radius_association\":{\"mobilenet_start_hour\":\"10\",\"mobilenet_end_hour\":\"12\",\"fixed_start_hour\":\"00\",\"fixed_end_hour\":\"23\",\"limit_count\":10000,\"logout_delay_seconds\":30,\"online_limit_min\":2},\"target_table_name\":\"ops_pr_fixed_radius_association_statistics\",\"columns\":\"capture_day,statistics_type,auth_count,radius_count,statistics_value,create_time\",\"spark_task_name\":\"pr_fixed_radius_association_rate_2025-03-10\"} --sql_id 60 --capture_day 2025-03-19 --start_time 1742313600000 --end_time 1742317200000
        """
        parser = argparse.ArgumentParser(
            description="这是一个接收命令行参数的示例脚本。"
        )
        # 处理命令行参数
        # 添加参数
        parser.add_argument("--task_conf", type=str, help="任务配置参数")
        parser.add_argument("--sql_id", type=str, help="待执行sql_id")
        parser.add_argument("--capture_day", type=str, help="天任务日期")
        parser.add_argument("--start_time", type=str, default="", help="天任务区间开始时间")
        parser.add_argument("--end_time", type=str, default="", help="天任务区间截止时间")

        # 解析参数
        args = parser.parse_args()
        logger.info(f"获取参数--task_conf: {args.task_conf}")
        logger.info(f"获取参数--sql_id: {args.sql_id}")
        logger.info(f"获取参数--capture_day: {args.capture_day}")
        logger.info(f"获取参数--start_time: {args.start_time}")
        logger.info(f"获取参数--end_time: {args.end_time}")
        # 获取任务配置
        task_conf = json.loads(args.task_conf)
        logger.info(f"序列化task_conf参数成功")

        sql_id = getProductSql(args.sql_id, task_conf["mysql_conf"])
        radius_association_conf = task_conf["radius_association"]
        radius_association_conf_json = json.loads(radius_association_conf)
        logger.info(f"序列化radius_association_conf参数成功")
        mobilenet_start_hour = radius_association_conf_json["mobilenet_start_hour"]
        mobilenet_end_hour = radius_association_conf_json["mobilenet_end_hour"]
        fixed_start_hour = radius_association_conf_json["fixed_start_hour"]
        fixed_end_hour = radius_association_conf_json["fixed_end_hour"]
        limit_count = radius_association_conf_json["limit_count"]
        logout_delay_seconds = radius_association_conf_json["logout_delay_seconds"]
        online_limit_min = radius_association_conf_json["online_limit_min"]
        logger.info(f"获取radius_association_conf参数成功")
        # 输出str类型的结果
        outputs = execSparkCommand(sql_id, task_conf["spark_task_name"], task_conf["spark_remote_host"],
                                   args.capture_day,
                                   args.start_time,
                                   args.end_time,
                                   mobilenet_start_hour,
                                   mobilenet_end_hour,
                                   fixed_start_hour,
                                   fixed_end_hour,
                                   limit_count,
                                   logout_delay_seconds,
                                   online_limit_min
                                   )
        # 写入doris
        loadToDoris(outputs, task_conf["target_table_name"], task_conf["columns"], task_conf["doris_conf"])
    except argparse.ArgumentError as e:
        logger.error(f"命令行参数解析错误: {e}")
    except Exception as e:
        logger.error(f"发生未知错误: {e}")


if __name__ == "__main__":
    main()

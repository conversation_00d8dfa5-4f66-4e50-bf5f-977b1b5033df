import argparse
import json

from common_util.doris_common_util import loadToDorisForCompleteData
from common_util.es_common_util import get_ads_count
from common_util.mysql_common_util import getProductSql
import common_util.log_common_util as logger

def main():
    try:
        parser = argparse.ArgumentParser(
            description="这是一个接收命令行参数的示例脚本。"
        )
        # 处理命令行参数
        # 添加参数
        parser.add_argument("--task_conf", type=str, help="任务配置参数")
        parser.add_argument("--sql_id", type=str, help="待执行sql_id")
        parser.add_argument("--capture_start_day", type=str, help="天任务区间开始时间")
        parser.add_argument("--capture_end_day", type=str, help="天任务区间截止时间")

        parser.add_argument("--resource_names", type=str, help="查询的资源名集合，逗号拼接")

        # 解析参数
        args = parser.parse_args()
        # 获取任务配置
        task_conf = json.loads(args.task_conf)
        dsl = getProductSql(args.sql_id, task_conf["mysql_conf"])
        datas = get_ads_count(es_conf=task_conf["es_conf"], dsl=dsl,
                              resource_names=args.resource_names,
                              captureStartDay=args.capture_start_day,
                              captureEndDay=args.capture_end_day)
        # 写入doris
        loadToDorisForCompleteData(datas, task_conf["target_table_name"], task_conf["doris_conf"])
    except argparse.ArgumentError as e:
        logger.info(f"命令行参数解析错误: {e}")
    except Exception as e:
        logger.error(f"发生未知错误: {e}")


if __name__ == "__main__":
    main()
